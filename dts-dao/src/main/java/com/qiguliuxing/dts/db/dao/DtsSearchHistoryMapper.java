package com.qiguliuxing.dts.db.dao;

import com.qiguliuxing.dts.db.domain.DtsSearchHistory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DtsSearchHistoryMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(DtsSearchHistory record);

    int insertSelective(DtsSearchHistory record);
    DtsSearchHistory selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DtsSearchHistory record);

    int updateByPrimaryKey(DtsSearchHistory record);

    int logicalDeleteByPrimaryKey(Integer id);

    List<DtsSearchHistory> findByUserId(@Param("uid") int uid);

    List<DtsSearchHistory> queryByKeyword(@Param("userId") String userId,
                                          @Param("keyword") String keyword,
                                          @Param("offset") Integer offset,
                                          @Param("limit") int limit,
                                          @Param("sort") String sort,
                                          @Param("order") String order);

    int countsByKeyword(@Param("userId") String userId, @Param("keyword") String keyword);
}
