package com.qiguliuxing.dts.db.dao;

import com.qiguliuxing.dts.db.bean.AccountTraceQueryDTO;
import com.qiguliuxing.dts.db.domain.DtsAccountTrace;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 账户跟踪数据访问层接口
 */
public interface DtsAccountTraceMapper {

    /**
     * 根据主键查询账户跟踪记录
     * 
     * @param id 主键ID
     * @return 账户跟踪记录
     */
    DtsAccountTrace selectByPrimaryKey(Integer id);

    /**
     * 根据主键选择性更新账户跟踪记录
     * 
     * @param record 要更新的记录
     * @return 更新的记录数
     */
    int updateByPrimaryKeySelective(DtsAccountTrace record);

    /**
     * 根据主键更新账户跟踪记录
     * 
     * @param record 要更新的记录
     * @return 更新的记录数
     */
    int updateByPrimaryKey(DtsAccountTrace record);

    /**
     * 插入账户跟踪记录
     * 
     * @param trace 要插入的记录
     * @return 插入的记录数
     */
    int insert(DtsAccountTrace trace);

    /**
     * 选择性插入账户跟踪记录
     * 
     * @param trace 要插入的记录
     * @return 插入的记录数
     */
    int insertSelective(DtsAccountTrace trace);

    /**
     * 根据用户ID分页查询账户跟踪记录
     * 
     * @param userId 用户ID
     * @param limit 每页记录数
     * @param offset 偏移量
     * @return 账户跟踪记录列表
     */
    List<DtsAccountTrace> queryByUserId(@Param("userId") Integer userId, @Param("limit") Integer limit,
                                        @Param("offset") Integer offset);

    /**
     * 根据条件查询账户跟踪记录
     * 
     * @param queryDTO 查询参数DTO
     * @return 账户跟踪记录列表
     */
    List<DtsAccountTrace> querySelectiveTrace(@Param("query") AccountTraceQueryDTO queryDTO);

    /**
     * 根据用户ID和状态查询账户跟踪记录
     * 
     * @param userId 用户ID
     * @param statusList 状态列表
     * @return 账户跟踪记录列表
     */
    List<DtsAccountTrace> queryByUserIdAndStatus(@Param("userId") Integer userId, @Param("statusList") List<Integer> statusList);

    /**
     * 根据主键删除账户跟踪记录
     * 
     * @param id 主键ID
     * @return 删除的记录数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 统计用户的账户跟踪记录数
     * 
     * @param userId 用户ID
     * @return 记录数
     */
    int countByUserId(@Param("userId") Integer userId);

    /**
     * 根据用户ID数组和状态统计账户跟踪记录数
     * 
     * @param userIdArray 用户ID数组
     * @param status 状态列表
     * @return 记录数
     */
    int countByUserIdAndStatus(@Param("userIdArray") List<Integer> userIdArray,
                               @Param("status") List<Integer> status);
}