package com.qiguliuxing.dts.db.dao;

import com.qiguliuxing.dts.db.domain.DtsOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DtsOrderMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dts_order
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dts_order
     */
    int insert(DtsOrder record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dts_order
     */
    int insertSelective(DtsOrder record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dts_order
     */
    DtsOrder selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dts_order
     */
    int updateByPrimaryKeySelective(DtsOrder record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dts_order
     */
    int updateByPrimaryKey(DtsOrder record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dts_order
     */
    int logicalDeleteByPrimaryKey(Integer id);

    // New XML-based query methods
    int countByUserId(@Param("userId") Integer userId);

    int countByOrderSn(@Param("userId") Integer userId, @Param("orderSn") String orderSn);

    List<DtsOrder> queryByOrderStatus(@Param("userId") Integer userId,
                                      @Param("orderStatus") List<Integer> orderStatus,
                                      @Param("statusCreate") Integer statusCreate,
                                      @Param("statusPay") Integer statusPay,
                                      @Param("statusShip") Integer statusShip,
                                      @Param("statusConfirm") Integer statusConfirm,
                                      @Param("statusAutoConfirm") Integer statusAutoConfirm,
                                      @Param("offset") int offset,
                                      @Param("limit") int limit);

    List<DtsOrder> querySelective(@Param("userId") Integer userId,
                                  @Param("orderSn") String orderSn,
                                  @Param("orderStatusArray") List<Integer> orderStatusArray,
                                  @Param("offset") Integer offset,
                                  @Param("limit") Integer size,
                                  @Param("sort") String sort,
                                  @Param("order") String order);

    int countAll();

    List<DtsOrder> queryUnpaid(@Param("statusCreate") Integer statusCreate);

    List<DtsOrder> queryUnconfirm(@Param("statusShip") Integer statusShip);

    DtsOrder findBySn(@Param("orderSn") String orderSn);

    Map<String, Object> queryOrderInfo(@Param("userId") Integer userId,
                                       @Param("statusCreate") Integer statusCreate,
                                       @Param("statusPay") Integer statusPay,
                                       @Param("statusShip") Integer statusShip,
                                       @Param("statusConfirm") Integer statusConfirm,
                                       @Param("statusAutoConfirm") Integer statusAutoConfirm);

    List<DtsOrder> queryComment();

    long countByOrderStatus(@Param("userId") Integer userId,
                            @Param("orderStatus") List<Integer> orderStatus,
                            @Param("statusCreate") Integer statusCreate,
                            @Param("statusPay") Integer statusPay,
                            @Param("statusShip") Integer statusShip,
                            @Param("statusConfirm") Integer statusConfirm,
                            @Param("statusAutoConfirm") Integer statusAutoConfirm);

    long countSelective(@Param("userId") Integer userId,
                        @Param("orderSn") String orderSn,
                        @Param("orderStatusArray") List<Integer> orderStatusArray);
}
