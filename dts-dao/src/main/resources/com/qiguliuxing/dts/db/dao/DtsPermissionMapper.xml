<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiguliuxing.dts.db.dao.DtsPermissionMapper">
    <resultMap id="BaseResultMap" type="com.qiguliuxing.dts.db.domain.DtsPermission">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="role_id" jdbcType="INTEGER" property="roleId"/>
        <result column="permission" jdbcType="VARCHAR" property="permission"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
            id,
            role_id,
            permission,
            add_time,
            update_time,
            deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_permission
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
        select
        <choose>
            <when test="selective != null and selective.length &gt; 0">
                <foreach collection="selective" item="column" separator=",">
                    ${column.escapedColumnName}
                </foreach>
            </when>
            <otherwise>
                id, role_id, permission, add_time, update_time, deleted
            </otherwise>
        </choose>
        from dts_permission
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from dts_permission
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.qiguliuxing.dts.db.domain.DtsPermission">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into dts_permission (role_id, permission, add_time,
        update_time, deleted)
        values (#{roleId,jdbcType=INTEGER}, #{permission,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT})
    </insert>
    <insert id="insertSelective" parameterType="com.qiguliuxing.dts.db.domain.DtsPermission">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into dts_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                role_id,
            </if>
            <if test="permission != null">
                permission,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                #{roleId,jdbcType=INTEGER},
            </if>
            <if test="permission != null">
                #{permission,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.qiguliuxing.dts.db.domain.DtsPermission">
        update dts_permission
        <set>
            <if test="roleId != null">
                role_id = #{roleId,jdbcType=INTEGER},
            </if>
            <if test="permission != null">
                permission = #{permission,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                add_time = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.qiguliuxing.dts.db.domain.DtsPermission">
        update dts_permission
        set role_id = #{roleId,jdbcType=INTEGER},
        permission = #{permission,jdbcType=VARCHAR},
        add_time = #{addTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        deleted = #{deleted,jdbcType=BIT}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
        update dts_permission set deleted = 1
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- New methods for replacing Example -->
    <select id="selectByRoleIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_permission
        where deleted = 0
        <if test="roleIds != null and roleIds.size() > 0">
            and role_id in
            <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>
        </if>
    </select>

    <select id="selectByRoleId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_permission
        where deleted = 0
        and role_id = #{roleId}
    </select>

    <select id="countSuperPermission" resultType="long">
        select count(*) from dts_permission
        where deleted = 0
        and role_id = #{roleId}
        and permission = '*'
    </select>

    <update id="logicalDeleteByRoleId">
        update dts_permission set deleted = 1
        where role_id = #{roleId}
        and deleted = 0
    </update>
</mapper>
