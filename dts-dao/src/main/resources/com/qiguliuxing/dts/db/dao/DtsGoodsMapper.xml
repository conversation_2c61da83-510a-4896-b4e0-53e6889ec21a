<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiguliuxing.dts.db.dao.DtsGoodsMapper">
    <resultMap id="BaseResultMap" type="com.qiguliuxing.dts.db.domain.DtsGoods">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="goods_sn" jdbcType="VARCHAR" property="goodsSn"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="category_id" jdbcType="INTEGER" property="categoryId"/>
        <result column="brand_id" jdbcType="INTEGER" property="brandId"/>
        <result column="gallery" jdbcType="VARCHAR" property="gallery"
                typeHandler="com.qiguliuxing.dts.db.mybatis.JsonStringArrayTypeHandler"/>
        <result column="keywords" jdbcType="VARCHAR" property="keywords"/>
        <result column="brief" jdbcType="VARCHAR" property="brief"/>
        <result column="is_on_sale" jdbcType="BIT" property="isOnSale"/>
        <result column="sort_order" jdbcType="SMALLINT" property="sortOrder"/>
        <result column="pic_url" jdbcType="VARCHAR" property="picUrl"/>
        <result column="share_url" jdbcType="VARCHAR" property="shareUrl"/>
        <result column="is_new" jdbcType="BIT" property="isNew"/>
        <result column="is_hot" jdbcType="BIT" property="isHot"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="counter_price" jdbcType="DECIMAL" property="counterPrice"/>
        <result column="retail_price" jdbcType="DECIMAL" property="retailPrice"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="browse" jdbcType="INTEGER" property="browse"/>
        <result column="sales" jdbcType="INTEGER" property="sales"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
        <result column="commpany" jdbcType="VARCHAR" property="commpany"/>
        <result column="wholesale_price" jdbcType="DECIMAL" property="wholesalePrice"/>
        <result column="approve_status" jdbcType="INTEGER" property="approveStatus"/>
        <result column="approve_msg" jdbcType="VARCHAR" property="approveMsg"/>
        <result column="brokerage_type" jdbcType="INTEGER" property="brokerageType"/>
        <result column="brokerage_price" jdbcType="DECIMAL" property="brokeragePrice"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.qiguliuxing.dts.db.domain.DtsGoods">
        <result column="detail" jdbcType="LONGVARCHAR" property="detail"/>
    </resultMap>
    <sql id="Base_Column_List">
            id,
            goods_sn,
            `name`,
            category_id,
            brand_id,
            gallery,
            keywords,
            brief,
            is_on_sale,
            sort_order,
            pic_url,
            share_url,
            is_new,
            is_hot,
            unit,
            counter_price,
            retail_price,
            add_time,
            update_time,
            `browse`,
            sales,
            deleted,
            commpany,
            wholesale_price,
            approve_status,
            approve_msg,
            brokerage_type,
            brokerage_price
    </sql>
    <sql id="Blob_Column_List">
            detail
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from dts_goods
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from dts_goods
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.qiguliuxing.dts.db.domain.DtsGoods">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into dts_goods (goods_sn, `name`, category_id,
        brand_id, gallery,
        keywords, brief, is_on_sale,
        sort_order, pic_url, share_url,
        is_new, is_hot, unit, counter_price,
        retail_price, add_time, update_time,
        `browse`, sales, deleted,
        commpany, wholesale_price, detail, approve_status, approve_msg,brokerage_type,
        brokerage_price
        )
        values (#{goodsSn,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{categoryId,jdbcType=INTEGER},
        #{brandId,jdbcType=INTEGER},
        #{gallery,jdbcType=VARCHAR,typeHandler=com.qiguliuxing.dts.db.mybatis.JsonStringArrayTypeHandler},
        #{keywords,jdbcType=VARCHAR}, #{brief,jdbcType=VARCHAR}, #{isOnSale,jdbcType=BIT},
        #{sortOrder,jdbcType=SMALLINT}, #{picUrl,jdbcType=VARCHAR}, #{shareUrl,jdbcType=VARCHAR},
        #{isNew,jdbcType=BIT}, #{isHot,jdbcType=BIT}, #{unit,jdbcType=VARCHAR}, #{counterPrice,jdbcType=DECIMAL},
        #{retailPrice,jdbcType=DECIMAL}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
        #{browse,jdbcType=INTEGER}, #{sales,jdbcType=INTEGER}, #{deleted,jdbcType=BIT},
        #{commpany,jdbcType=VARCHAR}, #{wholesalePrice,jdbcType=DECIMAL}, #{detail,jdbcType=LONGVARCHAR}, #{approveStatus,jdbcType=INTEGER}, #{approveMsg,jdbcType=VARCHAR}
        ,#{brokerageType,jdbcType=INTEGER}, #{brokeragePrice,jdbcType=DECIMAL}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.qiguliuxing.dts.db.domain.DtsGoods">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into dts_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="goodsSn != null">
                goods_sn,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="categoryId != null">
                category_id,
            </if>
            <if test="brandId != null">
                brand_id,
            </if>
            <if test="gallery != null">
                gallery,
            </if>
            <if test="keywords != null">
                keywords,
            </if>
            <if test="brief != null">
                brief,
            </if>
            <if test="isOnSale != null">
                is_on_sale,
            </if>
            <if test="sortOrder != null">
                sort_order,
            </if>
            <if test="picUrl != null">
                pic_url,
            </if>
            <if test="shareUrl != null">
                share_url,
            </if>
            <if test="isNew != null">
                is_new,
            </if>
            <if test="isHot != null">
                is_hot,
            </if>
            <if test="unit != null">
                unit,
            </if>
            <if test="counterPrice != null">
                counter_price,
            </if>
            <if test="retailPrice != null">
                retail_price,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="browse != null">
                `browse`,
            </if>
            <if test="sales != null">
                sales,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="commpany != null">
                commpany,
            </if>
            <if test="wholesalePrice != null">
                wholesale_price,
            </if>
            <if test="detail != null">
                detail,
            </if>
            <if test="approveStatus != null">
                approve_status,
            </if>
            <if test="approveMsg != null">
                approve_msg,
            </if>
            <if test="brokerageType != null">
                brokerage_type,
            </if>
            <if test="brokeragePrice != null">
                brokerage_price,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="goodsSn != null">
                #{goodsSn,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="categoryId != null">
                #{categoryId,jdbcType=INTEGER},
            </if>
            <if test="brandId != null">
                #{brandId,jdbcType=INTEGER},
            </if>
            <if test="gallery != null">
                #{gallery,jdbcType=VARCHAR,typeHandler=com.qiguliuxing.dts.db.mybatis.JsonStringArrayTypeHandler},
            </if>
            <if test="keywords != null">
                #{keywords,jdbcType=VARCHAR},
            </if>
            <if test="brief != null">
                #{brief,jdbcType=VARCHAR},
            </if>
            <if test="isOnSale != null">
                #{isOnSale,jdbcType=BIT},
            </if>
            <if test="sortOrder != null">
                #{sortOrder,jdbcType=SMALLINT},
            </if>
            <if test="picUrl != null">
                #{picUrl,jdbcType=VARCHAR},
            </if>
            <if test="shareUrl != null">
                #{shareUrl,jdbcType=VARCHAR},
            </if>
            <if test="isNew != null">
                #{isNew,jdbcType=BIT},
            </if>
            <if test="isHot != null">
                #{isHot,jdbcType=BIT},
            </if>
            <if test="unit != null">
                #{unit,jdbcType=VARCHAR},
            </if>
            <if test="counterPrice != null">
                #{counterPrice,jdbcType=DECIMAL},
            </if>
            <if test="retailPrice != null">
                #{retailPrice,jdbcType=DECIMAL},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="browse != null">
                #{browse,jdbcType=INTEGER},
            </if>
            <if test="sales != null">
                #{sales,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
            <if test="commpany != null">
                #{commpany,jdbcType=VARCHAR},
            </if>
            <if test="wholesalePrice != null">
                #{wholesalePrice,jdbcType=DECIMAL},
            </if>
            <if test="detail != null">
                #{detail,jdbcType=LONGVARCHAR},
            </if>
            <if test="approveStatus != null">
                #{approveStatus,jdbcType=INTEGER},
            </if>
            <if test="approveMsg != null">
                #{approveMsg,jdbcType=VARCHAR},
            </if>
            <if test="brokerageType != null">
                #{brokerageType,jdbcType=INTEGER},
            </if>
            <if test="brokeragePrice != null">
                #{brokeragePrice,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.qiguliuxing.dts.db.domain.DtsGoods">
        update dts_goods
        <set>
            <if test="goodsSn != null">
                goods_sn = #{goodsSn,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="categoryId != null">
                category_id = #{categoryId,jdbcType=INTEGER},
            </if>
            <if test="brandId != null">
                brand_id = #{brandId,jdbcType=INTEGER},
            </if>
            <if test="gallery != null">
                gallery =
                #{gallery,jdbcType=VARCHAR,typeHandler=com.qiguliuxing.dts.db.mybatis.JsonStringArrayTypeHandler},
            </if>
            <if test="keywords != null">
                keywords = #{keywords,jdbcType=VARCHAR},
            </if>
            <if test="brief != null">
                brief = #{brief,jdbcType=VARCHAR},
            </if>
            <if test="isOnSale != null">
                is_on_sale = #{isOnSale,jdbcType=BIT},
            </if>
            <if test="sortOrder != null">
                sort_order = #{sortOrder,jdbcType=SMALLINT},
            </if>
            <if test="picUrl != null">
                pic_url = #{picUrl,jdbcType=VARCHAR},
            </if>
            <if test="shareUrl != null">
                share_url = #{shareUrl,jdbcType=VARCHAR},
            </if>
            <if test="isNew != null">
                is_new = #{isNew,jdbcType=BIT},
            </if>
            <if test="isHot != null">
                is_hot = #{isHot,jdbcType=BIT},
            </if>
            <if test="unit != null">
                unit = #{unit,jdbcType=VARCHAR},
            </if>
            <if test="counterPrice != null">
                counter_price = #{counterPrice,jdbcType=DECIMAL},
            </if>
            <if test="retailPrice != null">
                retail_price = #{retailPrice,jdbcType=DECIMAL},
            </if>
            <if test="addTime != null">
                add_time = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="browse != null">
                `browse` = #{browse,jdbcType=INTEGER},
            </if>
            <if test="sales != null">
                sales = #{sales,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="commpany != null">
                commpany = #{commpany,jdbcType=VARCHAR},
            </if>
            <if test="wholesalePrice != null">
                wholesale_price = #{wholesalePrice,jdbcType=DECIMAL},
            </if>
            <if test="detail != null">
                detail = #{detail,jdbcType=LONGVARCHAR},
            </if>
            <if test="approveStatus != null">
                approve_status = #{approveStatus,jdbcType=INTEGER},
            </if>
            <if test="approveMsg != null">
                approve_msg = #{approveMsg,jdbcType=VARCHAR},
            </if>
            <if test="brokerageType != null">
                brokerage_type = #{brokerageType,jdbcType=INTEGER},
            </if>
            <if test="brokeragePrice != null">
                brokerage_price = #{brokeragePrice,jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.qiguliuxing.dts.db.domain.DtsGoods">
        update dts_goods
        set goods_sn = #{goodsSn,jdbcType=VARCHAR},
        `name` = #{name,jdbcType=VARCHAR},
        category_id = #{categoryId,jdbcType=INTEGER},
        brand_id = #{brandId,jdbcType=INTEGER},
        gallery = #{gallery,jdbcType=VARCHAR,typeHandler=com.qiguliuxing.dts.db.mybatis.JsonStringArrayTypeHandler},
        keywords = #{keywords,jdbcType=VARCHAR},
        brief = #{brief,jdbcType=VARCHAR},
        is_on_sale = #{isOnSale,jdbcType=BIT},
        sort_order = #{sortOrder,jdbcType=SMALLINT},
        pic_url = #{picUrl,jdbcType=VARCHAR},
        share_url = #{shareUrl,jdbcType=VARCHAR},
        is_new = #{isNew,jdbcType=BIT},
        is_hot = #{isHot,jdbcType=BIT},
        unit = #{unit,jdbcType=VARCHAR},
        counter_price = #{counterPrice,jdbcType=DECIMAL},
        retail_price = #{retailPrice,jdbcType=DECIMAL},
        add_time = #{addTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        `browse` = #{browse,jdbcType=INTEGER},
        sales = #{sales,jdbcType=INTEGER},
        deleted = #{deleted,jdbcType=BIT},
        commpany = #{commpany,jdbcType=VARCHAR},
        wholesale_price = #{wholesalePrice,jdbcType=DECIMAL},
        detail = #{detail,jdbcType=LONGVARCHAR},
        approve_status = #{approveStatus,jdbcType=INTEGER},
        approve_msg = #{approveMsg,jdbcType=VARCHAR},
        brokerage_type = #{brokerageType,jdbcType=INTEGER},
        brokerage_price = #{brokeragePrice,jdbcType=DECIMAL}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.qiguliuxing.dts.db.domain.DtsGoods">
        update dts_goods
        set goods_sn = #{goodsSn,jdbcType=VARCHAR},
        `name` = #{name,jdbcType=VARCHAR},
        category_id = #{categoryId,jdbcType=INTEGER},
        brand_id = #{brandId,jdbcType=INTEGER},
        gallery = #{gallery,jdbcType=VARCHAR,typeHandler=com.qiguliuxing.dts.db.mybatis.JsonStringArrayTypeHandler},
        keywords = #{keywords,jdbcType=VARCHAR},
        brief = #{brief,jdbcType=VARCHAR},
        is_on_sale = #{isOnSale,jdbcType=BIT},
        sort_order = #{sortOrder,jdbcType=SMALLINT},
        pic_url = #{picUrl,jdbcType=VARCHAR},
        share_url = #{shareUrl,jdbcType=VARCHAR},
        is_new = #{isNew,jdbcType=BIT},
        is_hot = #{isHot,jdbcType=BIT},
        unit = #{unit,jdbcType=VARCHAR},
        counter_price = #{counterPrice,jdbcType=DECIMAL},
        retail_price = #{retailPrice,jdbcType=DECIMAL},
        add_time = #{addTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        `browse` = #{browse,jdbcType=INTEGER},
        sales = #{sales,jdbcType=INTEGER},
        deleted = #{deleted,jdbcType=BIT},
        commpany = #{commpany,jdbcType=VARCHAR},
        wholesale_price = #{wholesalePrice,jdbcType=DECIMAL},
        approve_status = #{approveStatus,jdbcType=INTEGER},
        approve_msg = #{approveMsg,jdbcType=VARCHAR},
        brokerage_type = #{brokerageType,jdbcType=INTEGER},
        brokerage_price = #{brokeragePrice,jdbcType=DECIMAL}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
        update dts_goods set deleted = 1
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- New native dynamic SQL queries -->

    <select id="queryByHot" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_goods
        where is_hot = 1
        and is_on_sale = 1
        and deleted = 0
        order by browse desc
        limit #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </select>

    <select id="countByHot" resultType="int">
        select count(*)
        from dts_goods
        where is_hot = 1
        and is_on_sale = 1
        and deleted = 0
    </select>

    <select id="queryByNew" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_goods
        where is_new = 1
        and is_on_sale = 1
        and deleted = 0
        order by add_time desc
        limit #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </select>

    <select id="countByNew" resultType="int">
        select count(*)
        from dts_goods
        where is_new = 1
        and is_on_sale = 1
        and deleted = 0
    </select>

    <select id="queryByCategory" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_goods
        where category_id = #{categoryId}
        and is_on_sale = 1
        and deleted = 0
        order by add_time desc
        limit #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </select>

    <select id="countByCategory" resultType="int">
        select count(*)
        from dts_goods
        where category_id = #{categoryId}
        and is_on_sale = 1
        and deleted = 0
    </select>

    <select id="queryByCategoryIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_goods
        where category_id in
        <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        and is_on_sale = 1
        and deleted = 0
        order by sort_order asc
        limit #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </select>

    <select id="countByCategoryIds" resultType="int">
        select count(*)
        from dts_goods
        where category_id in
        <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        and is_on_sale = 1
        and deleted = 0
    </select>

    <select id="querySelective" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_goods
        <where>
            <if test="categoryId != null and categoryId != 0">
                and category_id = #{categoryId}
            </if>
            <if test="brandId != null">
                and brand_id = #{brandId}
            </if>
            <if test="isNew != null">
                and is_new = #{isNew}
            </if>
            <if test="isHot != null">
                and is_hot = #{isHot}
            </if>
            <if test="keywords != null and keywords != ''">
                and (keywords like concat('%', #{keywords}, '%') or name like concat('%', #{keywords}, '%'))
            </if>
            and is_on_sale = 1
            and deleted = 0
        </where>
        <if test="sort != null and sort != '' and order != null and order != ''">
            order by ${sort} ${order}
        </if>
        limit #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </select>

    <select id="countSelective" resultType="int">
        select count(*)
        from dts_goods
        <where>
            <if test="categoryId != null and categoryId != 0">
                and category_id = #{categoryId}
            </if>
            <if test="brandId != null">
                and brand_id = #{brandId}
            </if>
            <if test="isNew != null">
                and is_new = #{isNew}
            </if>
            <if test="isHot != null">
                and is_hot = #{isHot}
            </if>
            <if test="keywords != null and keywords != ''">
                and (keywords like concat('%', #{keywords}, '%') or name like concat('%', #{keywords}, '%'))
            </if>
            and is_on_sale = 1
            and deleted = 0
        </where>
    </select>

    <select id="querySelective2" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>,
        detail
        from dts_goods
        <where>
            <if test="goodsSn != null and goodsSn != ''">
                and goods_sn = #{goodsSn}
            </if>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="brandIds != null and brandIds.size() > 0">
                and brand_id in
                <foreach collection="brandIds" item="brandId" open="(" separator="," close=")">
                    #{brandId}
                </foreach>
            </if>
            and deleted = 0
        </where>
        <if test="sort != null and sort != '' and order != null and order != ''">
            order by ${sort} ${order}
        </if>
        <if test="offset != null and limit != null">
            limit #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
        </if>
    </select>

    <select id="countSelective2" resultType="int">
        select count(*)
        from dts_goods
        <where>
            <if test="goodsSn != null and goodsSn != ''">
                and goods_sn = #{goodsSn}
            </if>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="brandIds != null and brandIds.size() > 0">
                and brand_id in
                <foreach collection="brandIds" item="brandId" open="(" separator="," close=")">
                    #{brandId}
                </foreach>
            </if>
            and deleted = 0
        </where>
    </select>

    <select id="findById" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>,
        detail
        from dts_goods
        where id = #{id}
        and deleted = 0
    </select>

    <select id="findByIds" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>,
        detail
        from dts_goods
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and deleted = 0
    </select>

    <select id="findByGoodsSn" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>,
        detail
        from dts_goods
        where goods_sn = #{goodsSn}
        and deleted = 0
    </select>

    <select id="findByIdVO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_goods
        where id = #{id}
        and is_on_sale = 1
        and deleted = 0
    </select>

    <select id="findBySnVO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_goods
        where goods_sn = #{goodsSn}
        and is_on_sale = 1
        and deleted = 0
    </select>

    <select id="countOnSale" resultType="int">
        select count(*)
        from dts_goods
        where is_on_sale = 1
          and deleted = 0
    </select>

    <select id="countAll" resultType="int">
        select count(*)
        from dts_goods
        where deleted = 0
    </select>

    <select id="getCatIds" resultType="int">
        select distinct category_id
        from dts_goods
        <where>
            <if test="brandId != null">
                and brand_id = #{brandId}
            </if>
            <if test="isNew != null">
                and is_new = #{isNew}
            </if>
            <if test="isHot != null">
                and is_hot = #{isHot}
            </if>
            <if test="keywords != null and keywords != ''">
                and (keywords like concat('%', #{keywords}, '%') or name like concat('%', #{keywords}, '%'))
            </if>
            and is_on_sale = 1
            and deleted = 0
        </where>
    </select>

    <select id="queryByBrandId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_goods
        where brand_id = #{brandId}
        and category_id = #{categoryId}
        and is_on_sale = 1
        and deleted = 0
        order by browse desc
        limit #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </select>

    <select id="countByBrandId" resultType="int">
        select count(*)
        from dts_goods
        where brand_id = #{brandId}
        and category_id = #{categoryId}
        and is_on_sale = 1
        and deleted = 0
    </select>

    <select id="queryByCategoryAndNotSameBrandId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_goods
        where brand_id != #{brandId}
        and category_id = #{categoryId}
        and is_on_sale = 1
        and deleted = 0
        order by browse desc
        limit #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </select>

    <select id="countByCategoryAndNotSameBrandId" resultType="int">
        select count(*)
        from dts_goods
        where brand_id != #{brandId}
        and category_id = #{categoryId}
        and is_on_sale = 1
        and deleted = 0
    </select>
</mapper>
