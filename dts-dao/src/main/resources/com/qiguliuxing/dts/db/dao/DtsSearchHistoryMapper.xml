<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiguliuxing.dts.db.dao.DtsSearchHistoryMapper">
    <resultMap id="BaseResultMap" type="com.qiguliuxing.dts.db.domain.DtsSearchHistory">

        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="keyword" jdbcType="VARCHAR" property="keyword"/>
        <result column="from" jdbcType="VARCHAR" property="from"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
            `id`,
            user_id,
            keyword,
            `from`,
            add_time,
            update_time,
            deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_search_history
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from dts_search_history
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.qiguliuxing.dts.db.domain.DtsSearchHistory">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into dts_search_history (user_id, keyword, `from`,
        add_time, update_time, deleted
        )
        values (#{userId,jdbcType=INTEGER}, #{keyword,jdbcType=VARCHAR}, #{from,jdbcType=VARCHAR},
        #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.qiguliuxing.dts.db.domain.DtsSearchHistory">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into dts_search_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="keyword != null">
                keyword,
            </if>
            <if test="from != null">
                `from`,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="keyword != null">
                #{keyword,jdbcType=VARCHAR},
            </if>
            <if test="from != null">
                #{from,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.qiguliuxing.dts.db.domain.DtsSearchHistory">
        update dts_search_history
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="keyword != null">
                keyword = #{keyword,jdbcType=VARCHAR},
            </if>
            <if test="from != null">
                `from` = #{from,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                add_time = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.qiguliuxing.dts.db.domain.DtsSearchHistory">
        update dts_search_history
        set user_id = #{userId,jdbcType=INTEGER},
        keyword = #{keyword,jdbcType=VARCHAR},
        `from` = #{from,jdbcType=VARCHAR},
        add_time = #{addTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        deleted = #{deleted,jdbcType=BIT}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
        update dts_search_history
        set deleted = 1
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="findByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_search_history
        where user_id = #{uid}
        and deleted = 0
    </select>

    <select id="countsByKeyword" resultType="int">
        select count(*) from dts_search_history
        where `user_id` = #{userId} and `keyword` like concat('%',concat(#{keyword}, '%')) and `deleted` = 0
    </select>

    <select id="queryByKeyword" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_search_history
        <where>
            <if test="userId != null">
                user_id = #{userId}
            </if>
            <if test="keyword != null">
                and keyword like concat('%',concat(#{keyword}, '%'))
            </if>
            and deleted = 0
        </where>
        <if test="sort != null and order != null">
            order by #{sort,jdbcType=VARCHAR} #{order,jdbcType=VARCHAR}
        </if>
        <if test="limit != null and offset != null">
            limit #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
        </if>
    </select>
</mapper>
