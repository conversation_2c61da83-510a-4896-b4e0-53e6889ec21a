<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiguliuxing.dts.db.dao.DtsAccountTraceMapper">
    <resultMap id="BaseResultMap" type="com.qiguliuxing.dts.db.domain.DtsAccountTrace">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="trace_sn" jdbcType="VARCHAR" property="traceSn"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="total_amount" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="sms_code" jdbcType="VARCHAR" property="smsCode"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="trace_msg" jdbcType="VARCHAR" property="traceMsg"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
            id,
            trace_sn,
            user_id,
            `type`,
            amount,
            total_amount,
            add_time,
            mobile,
            sms_code,
            `status`,
            trace_msg,
            update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_account_trace
        where id = #{id,jdbcType=INTEGER}
    </select>
    <insert id="insert" parameterType="com.qiguliuxing.dts.db.domain.DtsAccountTrace">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into dts_account_trace (trace_sn, user_id, `type`,
        amount, total_amount, add_time,
        mobile, sms_code, `status`,
        trace_msg, update_time)
        values (#{traceSn,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER}, #{type,jdbcType=INTEGER},
        #{amount,jdbcType=DECIMAL}, #{totalAmount,jdbcType=DECIMAL}, #{addTime,jdbcType=TIMESTAMP},
        #{mobile,jdbcType=VARCHAR}, #{smsCode,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
        #{traceMsg,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.qiguliuxing.dts.db.domain.DtsAccountTrace">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into dts_account_trace
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="traceSn != null">
                trace_sn,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="totalAmount != null">
                total_amount,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="smsCode != null">
                sms_code,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="traceMsg != null">
                trace_msg,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="traceSn != null">
                #{traceSn,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="totalAmount != null">
                #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="smsCode != null">
                #{smsCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="traceMsg != null">
                #{traceMsg,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.qiguliuxing.dts.db.domain.DtsAccountTrace">
        update dts_account_trace
        <set>
            <if test="traceSn != null">
                trace_sn = #{traceSn,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="addTime != null">
                add_time = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="smsCode != null">
                sms_code = #{smsCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
            <if test="traceMsg != null">
                trace_msg = #{traceMsg,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.qiguliuxing.dts.db.domain.DtsAccountTrace">
        update dts_account_trace
        set trace_sn = #{traceSn,jdbcType=VARCHAR},
        user_id = #{userId,jdbcType=INTEGER},
        `type` = #{type,jdbcType=INTEGER},
        amount = #{amount,jdbcType=DECIMAL},
        total_amount = #{totalAmount,jdbcType=DECIMAL},
        add_time = #{addTime,jdbcType=TIMESTAMP},
        mobile = #{mobile,jdbcType=VARCHAR},
        sms_code = #{smsCode,jdbcType=VARCHAR},
        `status` = #{status,jdbcType=TINYINT},
        trace_msg = #{traceMsg,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="queryByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_account_trace
        where user_id = #{userId,jdbcType=INTEGER}
        order by add_time desc
        <if test="limit != null and offset != null">
            limit #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
        </if>
    </select>

    <select id="querySelectiveTrace" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_account_trace
        <where>
            <if test="query.userIdArray != null and query.userIdArray.size() > 0">
                user_id in
                <foreach collection="query.userIdArray" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="query.status != null and query.status.size() > 0">
                and status in
                <foreach collection="query.status" item="statusItem" open="(" separator="," close=")">
                    #{statusItem}
                </foreach>
            </if>
        </where>
        <if test="query.sort != null and query.sort != '' and query.order != null and query.order != ''">
            order by ${query.sort} ${query.order}
        </if>
        <if test="query.limit != null and query.offset != null">
            limit #{query.offset}, #{query.limit}
        </if>
    </select>

    <select id="queryByUserIdAndStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_account_trace
        where user_id = #{userId}
        <if test="statusList != null and statusList.size() > 0">
            and status in
            <foreach collection="statusList" item="statusItem" open="(" separator="," close=")">
                #{statusItem}
            </foreach>
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        delete from dts_account_trace where id = #{id}
    </delete>

    <select id="countByUserId" resultType="int">
        select count(0)
        from dts_account_trace
        where user_id = #{userId,jdbcType=INTEGER}
    </select>

    <select id="countByUserIdAndStatus" resultType="int">
        select count(0)
        from dts_account_trace
        where user_id = #{userId}
        <if test="statusList != null and statusList.size() > 0">
            and status in
            <foreach collection="statusList" item="statusItem" open="(" separator="," close=")">
                #{statusItem}
            </foreach>
        </if>
    </select>

</mapper>