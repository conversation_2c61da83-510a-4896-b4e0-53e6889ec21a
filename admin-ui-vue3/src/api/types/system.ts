import { BaseQuery, PageData } from './index';

// 登录信息相关类型
export interface LoginInfo {
  infoId: number;
  userName: string;
  ipaddr: string;
  loginLocation: string;
  browser: string;
  os: string;
  status: string;
  msg: string;
  loginTime: string;
}

export interface LoginInfoQuery extends BaseQuery {
  userName?: string;
  status?: string;
  loginTime?: string[];
}

export interface LoginInfoResponse extends PageData<LoginInfo> {}

// 在线用户相关类型
export interface OnlineUser {
  tokenId: string;
  userName: string;
  ipaddr: string;
  loginLocation: string;
  browser: string;
  os: string;
  loginTime: string;
}

export interface OnlineUserQuery extends BaseQuery {
  userName?: string;
  ipaddr?: string;
}

export interface OnlineUserResponse extends PageData<OnlineUser> {}

// 操作日志相关类型
export interface OperLog {
  operId: number;
  title: string;
  businessType: number;
  method: string;
  requestMethod: string;
  operatorType: number;
  operName: string;
  deptName: string;
  operUrl: string;
  operIp: string;
  operLocation: string;
  operParam: string;
  jsonResult: string;
  status: number;
  errorMsg: string;
  operTime: string;
}

export interface OperLogQuery extends BaseQuery {
  title?: string;
  operName?: string;
  businessType?: number;
  status?: number;
  operTime?: string[];
}

export interface OperLogResponse extends PageData<OperLog> {}

// 服务器信息相关类型
export interface ServerInfo {
  cpu: CpuInfo;
  mem: MemInfo;
  jvm: JvmInfo;
  sys: SysInfo;
  sysFiles: SysFile[];
}

export interface CpuInfo {
  cpuNum: number;
  total: number;
  sys: number;
  used: number;
  wait: number;
  free: number;
}

export interface MemInfo {
  total: number;
  used: number;
  free: number;
  usage: number;
}

export interface JvmInfo {
  total: number;
  max: number;
  used: number;
  free: number;
  usage: number;
  name: string;
  version: string;
  home: string;
  startTime: string;
  runTime: string;
  inputArgs: string;
}

export interface SysInfo {
  computerName: string;
  computerIp: string;
  userDir: string;
  osName: string;
  osArch: string;
}

export interface SysFile {
  dirName: string;
  sysTypeName: string;
  typeName: string;
  total: string;
  used: string;
  free: string;
  usage: number;
}

// 系统配置相关类型
export interface ConfigInfo {
  configId: number;
  configName: string;
  configKey: string;
  configValue: string;
  configType: string;
  createTime: string;
  updateTime: string;
  remark: string;
}

export interface ConfigQuery extends BaseQuery {
  configName?: string;
  configKey?: string;
  configType?: string;
}

export interface ConfigResponse extends PageData<ConfigInfo> {}

// 字典类型相关类型
export interface DictType {
  dictId: number;
  dictName: string;
  dictType: string;
  status: string;
  createTime: string;
  updateTime: string;
  remark: string;
}

export interface DictTypeQuery extends BaseQuery {
  dictName?: string;
  dictType?: string;
  status?: string;
}

export interface DictTypeResponse extends PageData<DictType> {}

// 字典数据相关类型
export interface DictData {
  dictCode: number;
  dictSort: number;
  dictLabel: string;
  dictValue: string;
  dictType: string;
  cssClass: string;
  listClass: string;
  isDefault: string;
  status: string;
  createTime: string;
  updateTime: string;
  remark: string;
}

export interface DictDataQuery extends BaseQuery {
  dictLabel?: string;
  dictType?: string;
  status?: string;
}

export interface DictDataResponse extends PageData<DictData> {}

// 通知公告相关类型
export interface NoticeInfo {
  noticeId: number;
  noticeTitle: string;
  noticeType: string;
  noticeContent: string;
  status: string;
  createBy: string;
  createTime: string;
  updateBy: string;
  updateTime: string;
  remark: string;
}

export interface NoticeQuery extends BaseQuery {
  noticeTitle?: string;
  noticeType?: string;
  status?: string;
}

export interface NoticeResponse extends PageData<NoticeInfo> {}
