import { BaseQuery, PageData } from './index';

// 用户相关类型
export interface UserInfo {
  id: number;
  username: string;
  password: string;
  gender: number;
  birthday: string;
  lastLoginTime: string;
  lastLoginIp: string;
  userLevel: number;
  nickname: string;
  mobile: string;
  avatar: string;
  weixinOpenid: string;
  sessionKey: string;
  status: number;
  addTime: string;
  updateTime: string;
  deleted: boolean;
}

export interface UserQuery extends BaseQuery {
  username?: string;
  mobile?: string;
  status?: number;
}

export interface UserListResponse extends PageData<UserInfo> {}

// 管理员相关类型
export interface AdminInfo {
  id: number;
  username: string;
  password: string;
  lastLoginIp: string;
  lastLoginTime: string;
  avatar: string;
  addTime: string;
  updateTime: string;
  roleIds: number[];
}

export interface AdminQuery extends BaseQuery {
  username?: string;
}

// 角色相关类型
export interface RoleInfo {
  id: number;
  name: string;
  desc: string;
  enabled: boolean;
  addTime: string;
  updateTime: string;
  deleted: boolean;
}

export interface RoleQuery extends BaseQuery {
  name?: string;
  enabled?: boolean;
}

// 商品相关类型
export interface GoodsInfo {
  id: number;
  goodsSn: string;
  name: string;
  categoryId: number;
  brandId: number;
  gallery: string[];
  keywords: string;
  brief: string;
  isOnSale: boolean;
  sortOrder: number;
  picUrl: string;
  shareUrl: string;
  isNew: boolean;
  isHot: boolean;
  unit: string;
  counterPrice: number;
  retailPrice: number;
  detail: string;
  addTime: string;
  updateTime: string;
  deleted: boolean;
}

export interface GoodsQuery extends BaseQuery {
  goodsSn?: string;
  name?: string;
  isOnSale?: boolean;
  isNew?: boolean;
  isHot?: boolean;
}

// 分类相关类型
export interface CategoryInfo {
  id: number;
  name: string;
  keywords: string;
  desc: string;
  pid: number;
  iconUrl: string;
  picUrl: string;
  level: string;
  sortOrder: number;
  addTime: string;
  updateTime: string;
  deleted: boolean;
}

export interface CategoryQuery extends BaseQuery {
  name?: string;
  level?: string;
}

// 品牌相关类型
export interface BrandInfo {
  id: number;
  name: string;
  desc: string;
  picUrl: string;
  sortOrder: number;
  floorPrice: number;
  addTime: string;
  updateTime: string;
  deleted: boolean;
}

export interface BrandQuery extends BaseQuery {
  name?: string;
}

// 订单相关类型
export interface OrderInfo {
  id: number;
  userId: number;
  orderSn: string;
  orderStatus: number;
  aftersaleStatus: number;
  consignee: string;
  mobile: string;
  address: string;
  message: string;
  goodsPrice: number;
  freightPrice: number;
  couponPrice: number;
  integralPrice: number;
  grouponPrice: number;
  orderPrice: number;
  actualPrice: number;
  payId: string;
  payTime: string;
  shipSn: string;
  shipChannel: string;
  shipTime: string;
  refundAmount: number;
  refundType: string;
  refundContent: string;
  refundTime: string;
  confirmTime: string;
  comments: number;
  endTime: string;
  addTime: string;
  updateTime: string;
  deleted: boolean;
}

export interface OrderQuery extends BaseQuery {
  orderSn?: string;
  consignee?: string;
  orderStatus?: number;
  userId?: number;
}

// 文章相关类型
export interface ArticleInfo {
  id: number;
  title: string;
  type: number;
  content: string;
  addTime: string;
  updateTime: string;
  deleted: boolean;
}

export interface ArticleQuery extends BaseQuery {
  title?: string;
  type?: number;
}

// 专题相关类型
export interface TopicInfo {
  id: number;
  title: string;
  subtitle: string;
  price: number;
  readCount: string;
  picUrl: string;
  sortOrder: number;
  goods: string;
  content: string;
  addTime: string;
  updateTime: string;
  deleted: boolean;
}

export interface TopicQuery extends BaseQuery {
  title?: string;
}

// 评论相关类型
export interface CommentInfo {
  id: number;
  valueId: number;
  type: number;
  content: string;
  userId: number;
  hasPicture: boolean;
  picUrls: string[];
  star: number;
  addTime: string;
  updateTime: string;
  deleted: boolean;
}

export interface CommentQuery extends BaseQuery {
  valueId?: number;
  type?: number;
  userId?: number;
}

// 优惠券相关类型
export interface CouponInfo {
  id: number;
  name: string;
  desc: string;
  tag: string;
  total: number;
  discount: number;
  min: number;
  limit: number;
  type: number;
  status: number;
  goodsType: number;
  goodsValue: string;
  code: string;
  timeType: number;
  days: number;
  startTime: string;
  endTime: string;
  addTime: string;
  updateTime: string;
  deleted: boolean;
}

export interface CouponQuery extends BaseQuery {
  name?: string;
  type?: number;
  status?: number;
}

// 登录相关类型
export interface LoginRequest {
  username: string;
  password: string;
  code?: string;
}

export interface LoginResponse {
  token: string;
  adminInfo: AdminInfo;
}

// 个人资料相关类型
export interface ProfileInfo {
  name: string;
  avatar: string;
  email: string;
  mobile: string;
}

export interface PasswordChangeRequest {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// 仪表板相关类型
export interface DashboardData {
  userTotal: number;
  goodsTotal: number;
  productTotal: number;
  orderTotal: number;
}
