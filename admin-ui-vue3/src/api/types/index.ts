// API 通用类型声明
export interface ApiResponse<T = any> {
  errno: number;
  data: T;
  errmsg?: string;
}

// 通用响应类型（别名）
export interface BaseResponse<T = any> {
  errno: number;
  data: T;
  errmsg?: string;
}

// 分页请求参数
export interface PageQuery {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

// 分页请求参数（别名）
export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

// 分页响应数据
export interface PageData<T = any> {
  total: number;
  pages: number;
  limit: number;
  page: number;
  list: T[];
}

// 分页响应数据（别名）
export interface PaginationResponse {
  total: number;
  pages: number;
  limit: number;
  page: number;
}

// 通用查询参数
export interface BaseQuery extends PageQuery {
  [key: string]: any;
}

// 通用响应类型
export type ApiResult<T = any> = Promise<ApiResponse<T>>;

// 错误码枚举
export enum ErrorCode {
  SUCCESS = 0,
  UNAUTHORIZED = 501,
  INTERNAL_ERROR = 502,
  UNSUPPORTED = 503,
  DATA_EXPIRED = 504,
  UPDATE_FAILED = 505,
  NO_PERMISSION = 506
}

// 请求配置接口
export interface RequestConfig {
  url: string;
  method: 'get' | 'post' | 'put' | 'delete' | 'patch';
  params?: Record<string, any>;
  data?: Record<string, any>;
  headers?: Record<string, string>;
}

// 文件上传相关类型
export interface UploadFile {
  uid: string;
  name: string;
  status: 'uploading' | 'done' | 'error';
  url?: string;
  percent?: number;
}

export interface UploadResponse {
  url: string;
  key: string;
  hash: string;
}
