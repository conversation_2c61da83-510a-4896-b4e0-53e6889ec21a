import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 在线用户相关类型定义
export interface OnlineUser {
  sessionId: string
  userId?: number
  userName: string
  deptName?: string
  ipaddr: string
  loginLocation: string
  browser: string
  os: string
  status: number
  startTimestamp: number
  lastAccessTime: number
  expireTime: number
  // 扩展信息
  userAgent?: string
  deviceType?: string
  deviceId?: string
  loginType?: number
  permissions?: string[]
  roles?: string[]
  loginTime?: string
  onlineTime?: number
  idleTime?: number
}

export interface OnlineUserListParams extends PaginationParams {
  userName?: string
  ipaddr?: string
  loginLocation?: string
  browser?: string
  os?: string
  deviceType?: string
  minOnlineTime?: number
  maxIdleTime?: number
}

export interface OnlineUserListResponse extends PaginationResponse {
  list: OnlineUser[]
}

export interface OnlineStatistics {
  // 在线统计
  totalOnline: number
  peakOnline: number
  peakTime: string
  avgOnlineTime: number
  
  // 分布统计
  browserDistribution: {
    browser: string
    count: number
    percentage: number
  }[]
  
  osDistribution: {
    os: string
    count: number
    percentage: number
  }[]
  
  deviceDistribution: {
    device: string
    count: number
    percentage: number
  }[]
  
  locationDistribution: {
    location: string
    count: number
    percentage: number
  }[]
  
  // 趋势数据
  onlineTrend: {
    time: string
    count: number
  }[]
  
  // 部门统计
  deptDistribution: {
    deptName: string
    count: number
    percentage: number
  }[]
}

export interface SessionActivity {
  sessionId: string
  userName: string
  action: string
  module: string
  method: string
  params: string
  ip: string
  time: string
  duration: number
  status: 'success' | 'error'
  errorMsg?: string
}

// 用户状态枚举
export enum OnlineStatus {
  ACTIVE = 1,   // 活跃
  IDLE = 2,     // 空闲
  AWAY = 3,     // 离开
  BUSY = 4      // 忙碌
}

// 设备类型枚举
export enum DeviceType {
  DESKTOP = 'desktop',
  MOBILE = 'mobile',
  TABLET = 'tablet',
  UNKNOWN = 'unknown'
}

// 在线用户管理 API
export const onlineApi = {
  // 获取在线用户列表
  list: (params: OnlineUserListParams): Promise<BaseResponse<OnlineUserListResponse>> => {
    return request({
      url: '/admin/system/online/list',
      method: 'get',
      params
    })
  },

  // 获取在线用户详情
  detail: (sessionId: string): Promise<BaseResponse<OnlineUser>> => {
    return request({
      url: `/admin/system/online/detail/${sessionId}`,
      method: 'get'
    })
  },

  // 强制退出用户
  forceLogout: (sessionId: string): Promise<BaseResponse<null>> => {
    return request({
      url: `/admin/system/online/logout/${sessionId}`,
      method: 'delete'
    })
  },

  // 批量强制退出用户
  batchForceLogout: (sessionIds: string[]): Promise<BaseResponse<{
    success: number
    failed: number
    results: {
      sessionId: string
      success: boolean
      error?: string
    }[]
  }>> => {
    return request({
      url: '/admin/system/online/batch/logout',
      method: 'delete',
      data: { sessionIds }
    })
  },

  // 发送消息给在线用户
  sendMessage: (data: {
    sessionIds: string[]
    title: string
    content: string
    type?: 'info' | 'warning' | 'error' | 'success'
    urgent?: boolean
  }): Promise<BaseResponse<{
    sent: number
    failed: number
    results: {
      sessionId: string
      success: boolean
      error?: string
    }[]
  }>> => {
    return request({
      url: '/admin/system/online/message',
      method: 'post',
      data
    })
  },

  // 广播消息给所有在线用户
  broadcastMessage: (data: {
    title: string
    content: string
    type?: 'info' | 'warning' | 'error' | 'success'
    urgent?: boolean
    excludeUsers?: string[]
  }): Promise<BaseResponse<{
    sent: number
    failed: number
  }>> => {
    return request({
      url: '/admin/system/online/broadcast',
      method: 'post',
      data
    })
  },

  // 获取在线统计信息
  getStatistics: (params?: {
    period?: 'hour' | 'day' | 'week' | 'month'
  }): Promise<BaseResponse<OnlineStatistics>> => {
    return request({
      url: '/admin/system/online/statistics',
      method: 'get',
      params
    })
  },

  // 获取用户会话活动
  getSessionActivity: (sessionId: string, params?: {
    page?: number
    limit?: number
    startTime?: string
    endTime?: string
    module?: string
  }): Promise<BaseResponse<{
    total: number
    list: SessionActivity[]
  }>> => {
    return request({
      url: `/admin/system/online/activity/${sessionId}`,
      method: 'get',
      params
    })
  },

  // 获取用户在线历史
  getUserOnlineHistory: (userName: string, params?: {
    page?: number
    limit?: number
    startTime?: string
    endTime?: string
  }): Promise<BaseResponse<{
    total: number
    list: {
      sessionId: string
      loginTime: string
      logoutTime?: string
      onlineDuration: number
      ip: string
      location: string
      browser: string
      os: string
    }[]
  }>> => {
    return request({
      url: `/admin/system/online/history/${userName}`,
      method: 'get',
      params
    })
  },

  // 设置用户状态
  setUserStatus: (sessionId: string, status: OnlineStatus): Promise<BaseResponse<null>> => {
    return request({
      url: `/admin/system/online/status/${sessionId}`,
      method: 'put',
      data: { status }
    })
  },

  // 延长会话时间
  extendSession: (sessionId: string, minutes: number): Promise<BaseResponse<{
    newExpireTime: number
    extended: boolean
  }>> => {
    return request({
      url: `/admin/system/online/extend/${sessionId}`,
      method: 'put',
      data: { minutes }
    })
  },

  // 获取会话详细信息
  getSessionInfo: (sessionId: string): Promise<BaseResponse<{
    sessionId: string
    userId: number
    userName: string
    attributes: Record<string, any>
    creationTime: number
    lastAccessedTime: number
    maxInactiveInterval: number
    isNew: boolean
    permissions: string[]
    roles: string[]
  }>> => {
    return request({
      url: `/admin/system/online/session/${sessionId}`,
      method: 'get'
    })
  },

  // 获取实时在线数据
  getRealTimeData: (): Promise<BaseResponse<{
    current: number
    peak: number
    trend: number[]
    recent: OnlineUser[]
  }>> => {
    return request({
      url: '/admin/system/online/realtime',
      method: 'get'
    })
  },

  // 导出在线用户数据
  export: (params: OnlineUserListParams): Promise<Blob> => {
    return request({
      url: '/admin/system/online/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  },

  // 获取用户活动地图
  getActivityMap: (params?: {
    period?: 'hour' | 'day' | 'week'
  }): Promise<BaseResponse<{
    country: string
    province: string
    city: string
    count: number
    lng: number
    lat: number
  }[]>> => {
    return request({
      url: '/admin/system/online/map',
      method: 'get',
      params
    })
  },

  // 设置在线监控配置
  setMonitorConfig: (config: {
    maxIdleTime?: number
    autoKickIdle?: boolean
    maxSessionsPerUser?: number
    enableLocationTracking?: boolean
    enableActivityTracking?: boolean
    alertThresholds?: {
      maxOnlineUsers?: number
      abnormalLoginPattern?: boolean
      multipleSessionAlert?: boolean
    }
  }): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/system/online/config',
      method: 'put',
      data: config
    })
  },

  // 获取在线监控配置
  getMonitorConfig: (): Promise<BaseResponse<{
    maxIdleTime: number
    autoKickIdle: boolean
    maxSessionsPerUser: number
    enableLocationTracking: boolean
    enableActivityTracking: boolean
    alertThresholds: {
      maxOnlineUsers: number
      abnormalLoginPattern: boolean
      multipleSessionAlert: boolean
    }
  }>> => {
    return request({
      url: '/admin/system/online/config',
      method: 'get'
    })
  },

  // 清理过期会话
  cleanExpiredSessions: (): Promise<BaseResponse<{
    cleaned: number
    errors: string[]
  }>> => {
    return request({
      url: '/admin/system/online/clean',
      method: 'post'
    })
  }
}

export default onlineApi
