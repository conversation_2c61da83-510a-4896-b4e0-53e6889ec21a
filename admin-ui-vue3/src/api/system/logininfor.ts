import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 登录信息相关类型定义
export interface LoginInfo {
  id?: number
  userName: string
  ipaddr: string
  loginLocation: string
  browser: string
  os: string
  status: number
  msg: string
  loginTime: string
  // 扩展信息
  userAgent?: string
  sessionId?: string
  loginType?: number
  deviceType?: string
  deviceId?: string
}

export interface LoginInfoListParams extends PaginationParams {
  userName?: string
  ipaddr?: string
  status?: number
  loginLocation?: string
  browser?: string
  os?: string
  beginTime?: string
  endTime?: string
}

export interface LoginInfoListResponse extends PaginationResponse {
  list: LoginInfo[]
}

export interface LoginStatistics {
  // 登录统计
  totalLogins: number
  todayLogins: number
  successfulLogins: number
  failedLogins: number
  uniqueUsers: number
  
  // 时间分布
  hourlyStats: {
    hour: number
    count: number
  }[]
  
  // 地区分布
  locationStats: {
    location: string
    count: number
    percentage: number
  }[]
  
  // 浏览器分布
  browserStats: {
    browser: string
    count: number
    percentage: number
  }[]
  
  // 操作系统分布
  osStats: {
    os: string
    count: number
    percentage: number
  }[]
  
  // 登录趋势
  trendData: {
    date: string
    successful: number
    failed: number
    total: number
  }[]
}

// 登录状态枚举
export enum LoginStatus {
  SUCCESS = 0, // 成功
  FAILED = 1   // 失败
}

// 登录类型枚举
export enum LoginType {
  WEB = 1,    // 网页登录
  MOBILE = 2, // 移动端登录
  API = 3     // API登录
}

// 登录信息管理 API
export const logininforApi = {
  // 获取登录信息列表
  list: (params: LoginInfoListParams): Promise<BaseResponse<LoginInfoListResponse>> => {
    return request({
      url: '/admin/system/logininfor/list',
      method: 'get',
      params
    })
  },

  // 获取登录信息详情
  detail: (id: number): Promise<BaseResponse<LoginInfo>> => {
    return request({
      url: `/admin/system/logininfor/detail/${id}`,
      method: 'get'
    })
  },

  // 删除登录信息
  delete: (ids: number[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/system/logininfor/delete',
      method: 'delete',
      data: { ids }
    })
  },

  // 清空登录日志
  clean: (): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/system/logininfor/clean',
      method: 'delete'
    })
  },

  // 解锁用户
  unlock: (userName: string): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/system/logininfor/unlock',
      method: 'post',
      data: { userName }
    })
  },

  // 批量解锁用户
  batchUnlock: (userNames: string[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/system/logininfor/batch/unlock',
      method: 'post',
      data: { userNames }
    })
  },

  // 获取登录统计信息
  getStatistics: (params?: {
    beginTime?: string
    endTime?: string
  }): Promise<BaseResponse<LoginStatistics>> => {
    return request({
      url: '/admin/system/logininfor/statistics',
      method: 'get',
      params
    })
  },

  // 获取用户登录历史
  getUserLoginHistory: (userName: string, params?: {
    page?: number
    limit?: number
    beginTime?: string
    endTime?: string
  }): Promise<BaseResponse<LoginInfoListResponse>> => {
    return request({
      url: `/admin/system/logininfor/user/${userName}`,
      method: 'get',
      params
    })
  },

  // 获取IP登录记录
  getIpLoginHistory: (ipaddr: string, params?: {
    page?: number
    limit?: number
    beginTime?: string
    endTime?: string
  }): Promise<BaseResponse<LoginInfoListResponse>> => {
    return request({
      url: `/admin/system/logininfor/ip/${ipaddr}`,
      method: 'get',
      params
    })
  },

  // 获取异常登录记录
  getAbnormalLogins: (params?: {
    page?: number
    limit?: number
    beginTime?: string
    endTime?: string
    type?: 'multiple_ip' | 'multiple_location' | 'failed_attempts'
  }): Promise<BaseResponse<LoginInfoListResponse>> => {
    return request({
      url: '/admin/system/logininfor/abnormal',
      method: 'get',
      params
    })
  },

  // 导出登录日志
  export: (params: LoginInfoListParams): Promise<Blob> => {
    return request({
      url: '/admin/system/logininfor/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  },

  // 获取登录地图数据
  getLoginMap: (params?: {
    beginTime?: string
    endTime?: string
  }): Promise<BaseResponse<{
    country: string
    province: string
    city: string
    count: number
    lng: number
    lat: number
  }[]>> => {
    return request({
      url: '/admin/system/logininfor/map',
      method: 'get',
      params
    })
  },

  // 获取实时登录信息
  getRealTimeLogins: (params?: {
    limit?: number
  }): Promise<BaseResponse<LoginInfo[]>> => {
    return request({
      url: '/admin/system/logininfor/realtime',
      method: 'get',
      params
    })
  },

  // 获取登录风险分析
  getRiskAnalysis: (params?: {
    userName?: string
    beginTime?: string
    endTime?: string
  }): Promise<BaseResponse<{
    riskLevel: 'low' | 'medium' | 'high'
    riskScore: number
    riskFactors: {
      factor: string
      description: string
      score: number
    }[]
    suggestions: string[]
  }>> => {
    return request({
      url: '/admin/system/logininfor/risk',
      method: 'get',
      params
    })
  },

  // 设置登录监控规则
  setMonitorRules: (rules: {
    maxFailedAttempts?: number
    lockoutDuration?: number
    unusualLocationAlert?: boolean
    multipleIpAlert?: boolean
    offHoursAlert?: boolean
  }): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/system/logininfor/monitor/rules',
      method: 'post',
      data: rules
    })
  },

  // 获取登录监控规则
  getMonitorRules: (): Promise<BaseResponse<{
    maxFailedAttempts: number
    lockoutDuration: number
    unusualLocationAlert: boolean
    multipleIpAlert: boolean
    offHoursAlert: boolean
  }>> => {
    return request({
      url: '/admin/system/logininfor/monitor/rules',
      method: 'get'
    })
  }
}

export default logininforApi
