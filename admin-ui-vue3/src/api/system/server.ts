import type { BaseResponse } from '@/api/types'
import request from '@/utils/request'

// 服务器监控相关类型定义
export interface ServerInfo {
  // 系统信息
  sys: {
    computerName: string
    computerIp: string
    userDir: string
    osName: string
    osArch: string
    osVersion: string
  }
  
  // Java虚拟机信息
  jvm: {
    name: string
    version: string
    vendor: string
    startTime: string
    runTime: string
    home: string
    inputArgs: string[]
    total: number
    max: number
    used: number
    free: number
    usage: number
  }
  
  // 内存信息
  mem: {
    total: number
    used: number
    free: number
    usage: number
  }
  
  // CPU信息
  cpu: {
    cpuNum: number
    used: number
    sys: number
    wait: number
    free: number
  }
  
  // 磁盘信息
  sysFiles: {
    dirName: string
    sysTypeName: string
    typeName: string
    total: string
    used: string
    avail: string
    usage: number
  }[]
}

export interface SystemMetrics {
  // 实时指标
  timestamp: number
  
  // CPU指标
  cpu: {
    usage: number
    userTime: number
    systemTime: number
    idleTime: number
    waitTime: number
    loadAverage: number[]
  }
  
  // 内存指标
  memory: {
    total: number
    used: number
    free: number
    cached: number
    buffers: number
    available: number
    usage: number
  }
  
  // 磁盘指标
  disk: {
    total: number
    used: number
    free: number
    usage: number
    readBytes: number
    writeBytes: number
    readOps: number
    writeOps: number
  }
  
  // 网络指标
  network: {
    bytesReceived: number
    bytesSent: number
    packetsReceived: number
    packetsSent: number
    errorsReceived: number
    errorsSent: number
  }
  
  // 进程指标
  process: {
    total: number
    running: number
    sleeping: number
    stopped: number
    zombie: number
  }
}

export interface PerformanceHistory {
  time: string
  cpu: number
  memory: number
  disk: number
  network: number
}

export interface ServiceStatus {
  name: string
  displayName: string
  status: 'running' | 'stopped' | 'starting' | 'stopping' | 'error'
  pid?: number
  port?: number
  startTime?: string
  memoryUsage?: number
  cpuUsage?: number
  description?: string
  autoStart?: boolean
  restartCount?: number
  lastRestart?: string
}

export interface AlertRule {
  id?: number
  name: string
  metric: 'cpu' | 'memory' | 'disk' | 'network' | 'service'
  operator: '>' | '<' | '>=' | '<=' | '=' | '!='
  threshold: number
  duration: number
  enabled: boolean
  severity: 'low' | 'medium' | 'high' | 'critical'
  description?: string
  actions: {
    email?: boolean
    sms?: boolean
    webhook?: string
  }
  createTime?: string
  updateTime?: string
}

export interface SystemAlert {
  id: number
  ruleName: string
  metric: string
  currentValue: number
  threshold: number
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  status: 'active' | 'resolved' | 'acknowledged'
  startTime: string
  endTime?: string
  acknowledgedBy?: string
  acknowledgedTime?: string
  resolvedTime?: string
}

export interface LogFile {
  name: string
  path: string
  size: number
  lastModified: string
  lines: number
  type: 'application' | 'access' | 'error' | 'system'
}

export interface LogContent {
  content: string
  totalLines: number
  currentLine: number
  hasMore: boolean
}

// 服务器监控管理 API
export const serverApi = {
  // 获取服务器信息
  getServerInfo: (): Promise<BaseResponse<ServerInfo>> => {
    return request({
      url: '/admin/system/server/info',
      method: 'get'
    })
  },

  // 获取系统实时指标
  getSystemMetrics: (): Promise<BaseResponse<SystemMetrics>> => {
    return request({
      url: '/admin/system/server/metrics',
      method: 'get'
    })
  },

  // 获取性能历史数据
  getPerformanceHistory: (params?: {
    period?: 'hour' | 'day' | 'week' | 'month'
    metric?: 'cpu' | 'memory' | 'disk' | 'network' | 'all'
    startTime?: string
    endTime?: string
  }): Promise<BaseResponse<PerformanceHistory[]>> => {
    return request({
      url: '/admin/system/server/performance/history',
      method: 'get',
      params
    })
  },

  // 获取服务状态列表
  getServiceStatus: (): Promise<BaseResponse<ServiceStatus[]>> => {
    return request({
      url: '/admin/system/server/services',
      method: 'get'
    })
  },

  // 启动服务
  startService: (serviceName: string): Promise<BaseResponse<{
    success: boolean
    message: string
    status: string
  }>> => {
    return request({
      url: `/admin/system/server/service/${serviceName}/start`,
      method: 'post'
    })
  },

  // 停止服务
  stopService: (serviceName: string): Promise<BaseResponse<{
    success: boolean
    message: string
    status: string
  }>> => {
    return request({
      url: `/admin/system/server/service/${serviceName}/stop`,
      method: 'post'
    })
  },

  // 重启服务
  restartService: (serviceName: string): Promise<BaseResponse<{
    success: boolean
    message: string
    status: string
  }>> => {
    return request({
      url: `/admin/system/server/service/${serviceName}/restart`,
      method: 'post'
    })
  },

  // 获取进程列表
  getProcessList: (params?: {
    keyword?: string
    sortBy?: 'cpu' | 'memory' | 'pid' | 'name'
    order?: 'asc' | 'desc'
    limit?: number
  }): Promise<BaseResponse<{
    pid: number
    name: string
    user: string
    cpuUsage: number
    memoryUsage: number
    startTime: string
    command: string
    status: string
  }[]>> => {
    return request({
      url: '/admin/system/server/processes',
      method: 'get',
      params
    })
  },

  // 终止进程
  killProcess: (pid: number, force?: boolean): Promise<BaseResponse<{
    success: boolean
    message: string
  }>> => {
    return request({
      url: `/admin/system/server/process/${pid}/kill`,
      method: 'post',
      data: { force }
    })
  },

  // 获取网络连接信息
  getNetworkConnections: (params?: {
    protocol?: 'tcp' | 'udp'
    state?: 'LISTEN' | 'ESTABLISHED' | 'TIME_WAIT' | 'CLOSE_WAIT'
    port?: number
  }): Promise<BaseResponse<{
    protocol: string
    localAddress: string
    localPort: number
    remoteAddress: string
    remotePort: number
    state: string
    pid: number
    processName: string
  }[]>> => {
    return request({
      url: '/admin/system/server/network/connections',
      method: 'get',
      params
    })
  },

  // 获取磁盘使用详情
  getDiskUsage: (): Promise<BaseResponse<{
    filesystem: string
    size: number
    used: number
    available: number
    usage: number
    mountPoint: string
    type: string
  }[]>> => {
    return request({
      url: '/admin/system/server/disk/usage',
      method: 'get'
    })
  },

  // 清理磁盘空间
  cleanDiskSpace: (paths: string[]): Promise<BaseResponse<{
    cleaned: number
    errors: string[]
    details: {
      path: string
      before: number
      after: number
      cleaned: number
    }[]
  }>> => {
    return request({
      url: '/admin/system/server/disk/clean',
      method: 'post',
      data: { paths }
    })
  },

  // 获取告警规则列表
  getAlertRules: (): Promise<BaseResponse<AlertRule[]>> => {
    return request({
      url: '/admin/system/server/alert/rules',
      method: 'get'
    })
  },

  // 创建告警规则
  createAlertRule: (rule: Omit<AlertRule, 'id' | 'createTime' | 'updateTime'>): Promise<BaseResponse<AlertRule>> => {
    return request({
      url: '/admin/system/server/alert/rules',
      method: 'post',
      data: rule
    })
  },

  // 更新告警规则
  updateAlertRule: (id: number, rule: Partial<AlertRule>): Promise<BaseResponse<AlertRule>> => {
    return request({
      url: `/admin/system/server/alert/rules/${id}`,
      method: 'put',
      data: rule
    })
  },

  // 删除告警规则
  deleteAlertRule: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: `/admin/system/server/alert/rules/${id}`,
      method: 'delete'
    })
  },

  // 获取系统告警列表
  getSystemAlerts: (params?: {
    status?: 'active' | 'resolved' | 'acknowledged'
    severity?: 'low' | 'medium' | 'high' | 'critical'
    startTime?: string
    endTime?: string
  }): Promise<BaseResponse<SystemAlert[]>> => {
    return request({
      url: '/admin/system/server/alerts',
      method: 'get',
      params
    })
  },

  // 确认告警
  acknowledgeAlert: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: `/admin/system/server/alert/${id}/acknowledge`,
      method: 'post'
    })
  },

  // 解决告警
  resolveAlert: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: `/admin/system/server/alert/${id}/resolve`,
      method: 'post'
    })
  },

  // 获取日志文件列表
  getLogFiles: (): Promise<BaseResponse<LogFile[]>> => {
    return request({
      url: '/admin/system/server/logs/files',
      method: 'get'
    })
  },

  // 读取日志内容
  readLogContent: (filename: string, params?: {
    lines?: number
    from?: number
    tail?: boolean
    search?: string
  }): Promise<BaseResponse<LogContent>> => {
    return request({
      url: `/admin/system/server/logs/${filename}/content`,
      method: 'get',
      params
    })
  },

  // 下载日志文件
  downloadLogFile: (filename: string): Promise<Blob> => {
    return request({
      url: `/admin/system/server/logs/${filename}/download`,
      method: 'get',
      responseType: 'blob'
    })
  },

  // 清理日志文件
  cleanLogFiles: (files: string[]): Promise<BaseResponse<{
    cleaned: number
    errors: string[]
    details: {
      file: string
      success: boolean
      error?: string
    }[]
  }>> => {
    return request({
      url: '/admin/system/server/logs/clean',
      method: 'post',
      data: { files }
    })
  },

  // 系统重启
  systemRestart: (delay?: number): Promise<BaseResponse<{
    scheduled: boolean
    delay: number
    message: string
  }>> => {
    return request({
      url: '/admin/system/server/restart',
      method: 'post',
      data: { delay }
    })
  },

  // 系统关机
  systemShutdown: (delay?: number): Promise<BaseResponse<{
    scheduled: boolean
    delay: number
    message: string
  }>> => {
    return request({
      url: '/admin/system/server/shutdown',
      method: 'post',
      data: { delay }
    })
  },

  // 获取系统环境变量
  getEnvironmentVariables: (): Promise<BaseResponse<Record<string, string>>> => {
    return request({
      url: '/admin/system/server/environment',
      method: 'get'
    })
  },

  // 执行系统命令
  executeCommand: (command: string, timeout?: number): Promise<BaseResponse<{
    exitCode: number
    stdout: string
    stderr: string
    duration: number
  }>> => {
    return request({
      url: '/admin/system/server/command',
      method: 'post',
      data: { command, timeout }
    })
  }
}

export default serverApi
