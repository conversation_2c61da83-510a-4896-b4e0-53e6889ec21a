import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 操作日志相关类型定义
export interface OperationLog {
  id?: number
  title: string
  businessType: number
  method: string
  requestMethod: string
  operatorType: number
  operName: string
  deptName: string
  operUrl: string
  operIp: string
  operLocation: string
  operParam: string
  jsonResult: string
  status: number
  errorMsg: string
  operTime: string
  // 扩展信息
  userAgent?: string
  browser?: string
  os?: string
  costTime?: number
  module?: string
  action?: string
  description?: string
  riskLevel?: number
}

export interface OperationLogListParams extends PaginationParams {
  title?: string
  operName?: string
  businessType?: number
  status?: number
  operIp?: string
  module?: string
  beginTime?: string
  endTime?: string
  requestMethod?: string
  riskLevel?: number
}

export interface OperationLogListResponse extends PaginationResponse {
  list: OperationLog[]
}

export interface OperationStatistics {
  // 操作统计
  totalOperations: number
  todayOperations: number
  successfulOperations: number
  failedOperations: number
  
  // 业务类型分布
  businessTypeStats: {
    type: number
    typeName: string
    count: number
    percentage: number
  }[]
  
  // 操作人员排行
  operatorStats: {
    operName: string
    deptName: string
    count: number
    successRate: number
  }[]
  
  // 时间分布
  hourlyStats: {
    hour: number
    count: number
  }[]
  
  // 模块统计
  moduleStats: {
    module: string
    count: number
    percentage: number
  }[]
  
  // 错误统计
  errorStats: {
    errorType: string
    count: number
    percentage: number
  }[]
  
  // 趋势数据
  trendData: {
    date: string
    total: number
    success: number
    failed: number
  }[]
  
  // 风险等级分布
  riskLevelStats: {
    level: number
    levelName: string
    count: number
    percentage: number
  }[]
}

export interface OperationDetail {
  id: number
  title: string
  businessType: number
  businessTypeName: string
  method: string
  requestMethod: string
  operatorType: number
  operatorTypeName: string
  operName: string
  deptName: string
  operUrl: string
  operIp: string
  operLocation: string
  operParam: string
  jsonResult: string
  status: number
  statusName: string
  errorMsg: string
  operTime: string
  costTime: number
  userAgent: string
  browser: string
  os: string
  module: string
  action: string
  description: string
  riskLevel: number
  riskLevelName: string
}

// 业务类型枚举
export enum BusinessType {
  OTHER = 0,     // 其它
  INSERT = 1,    // 新增
  UPDATE = 2,    // 修改
  DELETE = 3,    // 删除
  GRANT = 4,     // 授权
  EXPORT = 5,    // 导出
  IMPORT = 6,    // 导入
  FORCE = 7,     // 强退
  GENCODE = 8,   // 生成代码
  CLEAN = 9      // 清空数据
}

// 操作类型枚举
export enum OperatorType {
  OTHER = 0,    // 其它
  MANAGE = 1,   // 后台用户
  MOBILE = 2    // 手机端用户
}

// 操作状态枚举
export enum OperationStatus {
  SUCCESS = 0,  // 正常
  FAIL = 1      // 异常
}

// 风险等级枚举
export enum RiskLevel {
  LOW = 1,      // 低风险
  MEDIUM = 2,   // 中风险
  HIGH = 3,     // 高风险
  CRITICAL = 4  // 严重风险
}

// 操作日志管理 API
export const operlogApi = {
  // 获取操作日志列表
  list: (params: OperationLogListParams): Promise<BaseResponse<OperationLogListResponse>> => {
    return request({
      url: '/admin/system/operlog/list',
      method: 'get',
      params
    })
  },

  // 获取操作日志详情
  detail: (id: number): Promise<BaseResponse<OperationDetail>> => {
    return request({
      url: `/admin/system/operlog/detail/${id}`,
      method: 'get'
    })
  },

  // 删除操作日志
  delete: (ids: number[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/system/operlog/delete',
      method: 'delete',
      data: { ids }
    })
  },

  // 清空操作日志
  clean: (): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/system/operlog/clean',
      method: 'delete'
    })
  },

  // 批量删除操作日志
  batchDelete: (data: {
    ids?: number[]
    businessType?: number
    status?: number
    beginTime?: string
    endTime?: string
  }): Promise<BaseResponse<{
    deleted: number
    errors: string[]
  }>> => {
    return request({
      url: '/admin/system/operlog/batch/delete',
      method: 'delete',
      data
    })
  },

  // 获取操作统计信息
  getStatistics: (params?: {
    beginTime?: string
    endTime?: string
    operName?: string
    module?: string
  }): Promise<BaseResponse<OperationStatistics>> => {
    return request({
      url: '/admin/system/operlog/statistics',
      method: 'get',
      params
    })
  },

  // 获取用户操作历史
  getUserOperations: (operName: string, params?: {
    page?: number
    limit?: number
    beginTime?: string
    endTime?: string
    businessType?: number
  }): Promise<BaseResponse<OperationLogListResponse>> => {
    return request({
      url: `/admin/system/operlog/user/${operName}`,
      method: 'get',
      params
    })
  },

  // 获取模块操作记录
  getModuleOperations: (module: string, params?: {
    page?: number
    limit?: number
    beginTime?: string
    endTime?: string
    operName?: string
  }): Promise<BaseResponse<OperationLogListResponse>> => {
    return request({
      url: `/admin/system/operlog/module/${module}`,
      method: 'get',
      params
    })
  },

  // 获取失败操作记录
  getFailedOperations: (params?: {
    page?: number
    limit?: number
    beginTime?: string
    endTime?: string
    operName?: string
    businessType?: number
  }): Promise<BaseResponse<OperationLogListResponse>> => {
    return request({
      url: '/admin/system/operlog/failed',
      method: 'get',
      params
    })
  },

  // 获取高风险操作记录
  getHighRiskOperations: (params?: {
    page?: number
    limit?: number
    beginTime?: string
    endTime?: string
    operName?: string
    minRiskLevel?: number
  }): Promise<BaseResponse<OperationLogListResponse>> => {
    return request({
      url: '/admin/system/operlog/highrisk',
      method: 'get',
      params
    })
  },

  // 导出操作日志
  export: (params: OperationLogListParams): Promise<Blob> => {
    return request({
      url: '/admin/system/operlog/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  },

  // 获取操作趋势
  getTrend: (params?: {
    period?: 'hour' | 'day' | 'week' | 'month'
    beginTime?: string
    endTime?: string
    granularity?: 'hour' | 'day'
  }): Promise<BaseResponse<{
    date: string
    total: number
    success: number
    failed: number
    avgCostTime: number
  }[]>> => {
    return request({
      url: '/admin/system/operlog/trend',
      method: 'get',
      params
    })
  },

  // 获取操作热力图数据
  getHeatmap: (params?: {
    beginTime?: string
    endTime?: string
    type?: 'time' | 'user' | 'module'
  }): Promise<BaseResponse<{
    x: string
    y: string
    value: number
  }[]>> => {
    return request({
      url: '/admin/system/operlog/heatmap',
      method: 'get',
      params
    })
  },

  // 获取操作路径分析
  getOperationPath: (operName: string, params?: {
    beginTime?: string
    endTime?: string
    limit?: number
  }): Promise<BaseResponse<{
    path: string[]
    frequency: number
    avgTime: number
  }[]>> => {
    return request({
      url: `/admin/system/operlog/path/${operName}`,
      method: 'get',
      params
    })
  },

  // 获取异常操作分析
  getAbnormalOperations: (params?: {
    type?: 'frequent' | 'unusual_time' | 'suspicious_ip' | 'error_pattern'
    beginTime?: string
    endTime?: string
    threshold?: number
  }): Promise<BaseResponse<{
    type: string
    description: string
    operations: OperationLog[]
    riskScore: number
  }[]>> => {
    return request({
      url: '/admin/system/operlog/abnormal',
      method: 'get',
      params
    })
  },

  // 设置日志监控规则
  setMonitorRules: (rules: {
    enableRealTimeAlert?: boolean
    maxFailureCount?: number
    suspiciousIpAlert?: boolean
    highRiskOperationAlert?: boolean
    retentionDays?: number
    autoCleanup?: boolean
    alertRecipients?: string[]
  }): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/system/operlog/monitor/rules',
      method: 'post',
      data: rules
    })
  },

  // 获取日志监控规则
  getMonitorRules: (): Promise<BaseResponse<{
    enableRealTimeAlert: boolean
    maxFailureCount: number
    suspiciousIpAlert: boolean
    highRiskOperationAlert: boolean
    retentionDays: number
    autoCleanup: boolean
    alertRecipients: string[]
  }>> => {
    return request({
      url: '/admin/system/operlog/monitor/rules',
      method: 'get'
    })
  },

  // 获取日志存储信息
  getStorageInfo: (): Promise<BaseResponse<{
    totalSize: number
    totalRecords: number
    oldestRecord: string
    newestRecord: string
    dailyGrowth: number
    retentionDays: number
    estimatedCleanupSize: number
  }>> => {
    return request({
      url: '/admin/system/operlog/storage',
      method: 'get'
    })
  },

  // 压缩历史日志
  compressLogs: (beforeDate: string): Promise<BaseResponse<{
    compressed: number
    originalSize: number
    compressedSize: number
    compressionRatio: number
  }>> => {
    return request({
      url: '/admin/system/operlog/compress',
      method: 'post',
      data: { beforeDate }
    })
  }
}

export default operlogApi
