import { RoleInfo, RoleQuery } from '@/api/types/business';
import { ApiResult, PageData } from '@/api/types/index';
import request from '@/utils/request';

// 获取角色列表
export function list(query: RoleQuery): ApiResult<PageData<RoleInfo>> {
  return request({
    url: '/admin/role/list',
    method: 'get',
    params: query
  });
}

// 获取所有角色选项（不分页）
export function options(): ApiResult<RoleInfo[]> {
  return request({
    url: '/admin/role/options',
    method: 'get'
  });
}

// 创建角色
export function create(data: Partial<RoleInfo>): ApiResult<RoleInfo> {
  return request({
    url: '/admin/role/create',
    method: 'post',
    data
  });
}

// 获取角色详情
export function read(id: number): ApiResult<RoleInfo> {
  return request({
    url: '/admin/role/read',
    method: 'get',
    params: { id }
  });
}

// 更新角色信息
export function update(data: Partial<RoleInfo>): ApiResult<null> {
  return request({
    url: '/admin/role/update',
    method: 'post',
    data
  });
}

// 删除角色
export function remove(data: { id: number }): ApiResult<null> {
  return request({
    url: '/admin/role/delete',
    method: 'post',
    data
  });
}

// 获取角色权限
export function getPermissions(roleId: number): ApiResult<{
  systemPermissions: string[];
  menuPermissions: string[];
}> {
  return request({
    url: '/admin/role/permissions',
    method: 'get',
    params: { roleId }
  });
}

// 更新角色权限
export function updatePermissions(data: {
  roleId: number;
  permissions: string[];
}): ApiResult<null> {
  return request({
    url: '/admin/role/updatePermissions',
    method: 'post',
    data
  });
}

// 启用/禁用角色
export function updateStatus(data: {
  id: number;
  enabled: boolean;
}): ApiResult<null> {
  return request({
    url: '/admin/role/updateStatus',
    method: 'post',
    data
  });
}
