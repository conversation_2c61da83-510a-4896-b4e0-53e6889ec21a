import { LoginRequest, LoginResponse } from '@/api/types/business';
import { ApiResult } from '@/api/types/index';
import request from '@/utils/request';

// 管理员登录
export function login(data: LoginRequest): ApiResult<LoginResponse> {
  return request({
    url: '/admin/auth/login',
    method: 'post',
    data
  });
}

// 管理员登出
export function logout(): ApiResult<null> {
  return request({
    url: '/admin/auth/logout',
    method: 'post'
  });
}

// 获取管理员信息
export function getInfo(): ApiResult<LoginResponse> {
  return request({
    url: '/admin/auth/info',
    method: 'get'
  });
}

// 修改密码
export function updatePassword(data: {
  oldPassword: string;
  newPassword: string;
}): ApiResult<null> {
  return request({
    url: '/admin/auth/password',
    method: 'post',
    data
  });
}

// 获取验证码（如果需要）
export function getCaptcha(): ApiResult<{
  img: string;
  uuid: string;
}> {
  return request({
    url: '/admin/auth/captcha',
    method: 'get'
  });
}
