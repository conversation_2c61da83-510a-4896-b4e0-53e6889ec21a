import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 专题相关类型定义
export interface TopicInfo {
  id?: number
  title: string
  subtitle: string
  content?: string
  price: number
  readCount: string
  picUrl: string
  sortOrder: number
  goods: number[]
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface TopicCreateParams {
  title: string
  subtitle: string
  content?: string
  price: number
  readCount: string
  picUrl: string
  sortOrder: number
  goods: number[]
}

export interface TopicUpdateParams extends TopicCreateParams {
  id: number
}

export interface TopicListParams extends PaginationParams {
  title?: string
  subtitle?: string
  sortOrder?: number
}

export interface TopicListResponse extends PaginationResponse {
  list: TopicInfo[]
}

export interface TopicDetailResponse extends BaseResponse {
  data: {
    topic: TopicInfo
    goods: {
      id: number
      name: string
      picUrl: string
      retailPrice: number
      counterPrice: number
    }[]
  }
}

// 专题管理 API
export const topicApi = {
  // 获取专题列表
  list: (params: TopicListParams): Promise<BaseResponse<TopicListResponse>> => {
    return request({
      url: '/admin/topic/list',
      method: 'get',
      params
    })
  },

  // 获取专题详情
  detail: (id: number): Promise<TopicDetailResponse> => {
    return request({
      url: `/admin/topic/detail/${id}`,
      method: 'get'
    })
  },

  // 创建专题
  create: (data: TopicCreateParams): Promise<BaseResponse<TopicInfo>> => {
    return request({
      url: '/admin/topic/create',
      method: 'post',
      data
    })
  },

  // 更新专题
  update: (data: TopicUpdateParams): Promise<BaseResponse<TopicInfo>> => {
    return request({
      url: '/admin/topic/update',
      method: 'post',
      data
    })
  },

  // 删除专题
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/topic/delete',
      method: 'post',
      data: { id }
    })
  },

  // 批量删除专题
  batchDelete: (ids: number[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/topic/batch/delete',
      method: 'post',
      data: { ids }
    })
  },

  // 批量更新排序
  updateSort: (topics: { id: number; sortOrder: number }[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/topic/sort',
      method: 'post',
      data: { topics }
    })
  }
}

export default topicApi
