import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 存储相关类型定义
export interface StorageInfo {
  id?: number
  key: string
  name: string
  type: string
  size: number
  url: string
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface StorageCreateParams {
  key: string
  name: string
  type: string
  size: number
  url: string
}

export interface StorageUpdateParams extends StorageCreateParams {
  id: number
}

export interface StorageListParams extends PaginationParams {
  key?: string
  name?: string
  type?: string
}

export interface StorageListResponse extends PaginationResponse {
  list: StorageInfo[]
}

export interface UploadConfig {
  // 上传配置
  maxSize: number // 最大文件大小（字节）
  allowedTypes: string[] // 允许的文件类型
  uploadPath: string // 上传路径
}

export interface UploadResponse {
  url: string
  filename: string
  size: number
  type: string
}

// 存储管理 API
export const storageApi = {
  // 获取存储文件列表
  list: (params: StorageListParams): Promise<BaseResponse<StorageListResponse>> => {
    return request({
      url: '/admin/storage/list',
      method: 'get',
      params
    })
  },

  // 获取存储文件详情
  detail: (id: number): Promise<BaseResponse<StorageInfo>> => {
    return request({
      url: `/admin/storage/detail/${id}`,
      method: 'get'
    })
  },

  // 创建存储记录
  create: (data: StorageCreateParams): Promise<BaseResponse<StorageInfo>> => {
    return request({
      url: '/admin/storage/create',
      method: 'post',
      data
    })
  },

  // 更新存储记录
  update: (data: StorageUpdateParams): Promise<BaseResponse<StorageInfo>> => {
    return request({
      url: '/admin/storage/update',
      method: 'post',
      data
    })
  },

  // 删除存储文件
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/storage/delete',
      method: 'post',
      data: { id }
    })
  },

  // 批量删除存储文件
  batchDelete: (ids: number[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/storage/batch/delete',
      method: 'post',
      data: { ids }
    })
  },

  // 上传文件
  upload: (file: File): Promise<BaseResponse<UploadResponse>> => {
    const formData = new FormData()
    formData.append('file', file)
    
    return request({
      url: '/admin/storage/upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 批量上传文件
  batchUpload: (files: File[]): Promise<BaseResponse<UploadResponse[]>> => {
    const formData = new FormData()
    files.forEach(file => {
      formData.append('files', file)
    })
    
    return request({
      url: '/admin/storage/batch/upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取上传配置
  getUploadConfig: (): Promise<BaseResponse<UploadConfig>> => {
    return request({
      url: '/admin/storage/config',
      method: 'get'
    })
  },

  // 获取存储统计
  statistics: (): Promise<BaseResponse<{
    totalFiles: number
    totalSize: number
    todayUploads: number
    typeDistribution: { [key: string]: number }
    sizeDistribution: { [key: string]: number }
  }>> => {
    return request({
      url: '/admin/storage/statistics',
      method: 'get'
    })
  },

  // 检查文件是否存在
  exists: (key: string): Promise<BaseResponse<{ exists: boolean }>> => {
    return request({
      url: '/admin/storage/exists',
      method: 'get',
      params: { key }
    })
  },

  // 获取文件访问URL
  getUrl: (key: string): Promise<BaseResponse<{ url: string }>> => {
    return request({
      url: '/admin/storage/url',
      method: 'get',
      params: { key }
    })
  },

  // 清理过期文件
  cleanup: (): Promise<BaseResponse<{ count: number }>> => {
    return request({
      url: '/admin/storage/cleanup',
      method: 'post'
    })
  }
}

export default storageApi
