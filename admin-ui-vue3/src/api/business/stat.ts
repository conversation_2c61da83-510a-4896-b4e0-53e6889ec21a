import type { BaseResponse, PaginationParams } from '@/api/types'
import request from '@/utils/request'

// 统计数据相关类型定义
export interface DashboardStats {
  // 用户统计
  userStats: {
    totalUsers: number
    todayNewUsers: number
    activeUsers: number
    userGrowthRate: number
  }
  // 订单统计
  orderStats: {
    totalOrders: number
    todayOrders: number
    totalAmount: number
    todayAmount: number
    orderGrowthRate: number
    amountGrowthRate: number
  }
  // 商品统计
  goodsStats: {
    totalGoods: number
    onSaleGoods: number
    soldOutGoods: number
    totalViews: number
  }
  // 收入统计
  revenueStats: {
    totalRevenue: number
    todayRevenue: number
    monthlyRevenue: number
    revenueGrowthRate: number
  }
}

export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string
    borderColor?: string
    fill?: boolean
  }[]
}

export interface UserAnalytics {
  // 用户注册趋势
  registrationTrend: ChartData
  // 用户活跃度
  userActivity: ChartData
  // 用户地域分布
  userRegionDistribution: {
    region: string
    count: number
    percentage: number
  }[]
  // 用户年龄分布
  userAgeDistribution: {
    ageRange: string
    count: number
    percentage: number
  }[]
}

export interface SalesAnalytics {
  // 销售趋势
  salesTrend: ChartData
  // 销售额分析
  revenueAnalysis: ChartData
  // 热销商品
  topSellingGoods: {
    id: number
    name: string
    salesCount: number
    revenue: number
    picUrl: string
  }[]
  // 分类销售占比
  categorySalesDistribution: {
    categoryName: string
    salesCount: number
    revenue: number
    percentage: number
  }[]
}

export interface OrderAnalytics {
  // 订单状态分布
  orderStatusDistribution: {
    status: string
    count: number
    percentage: number
  }[]
  // 订单趋势
  orderTrend: ChartData
  // 平均订单价值
  averageOrderValue: ChartData
  // 订单来源分析
  orderSourceAnalysis: {
    source: string
    count: number
    percentage: number
  }[]
}

export interface StatQueryParams extends PaginationParams {
  startDate?: string
  endDate?: string
  granularity?: 'day' | 'week' | 'month' | 'year'
}

// 统计分析 API
export const statApi = {
  // 获取仪表盘统计数据
  getDashboardStats: (): Promise<BaseResponse<DashboardStats>> => {
    return request({
      url: '/admin/stat/dashboard',
      method: 'get'
    })
  },

  // 获取用户分析数据
  getUserAnalytics: (params?: StatQueryParams): Promise<BaseResponse<UserAnalytics>> => {
    return request({
      url: '/admin/stat/user/analytics',
      method: 'get',
      params
    })
  },

  // 获取销售分析数据
  getSalesAnalytics: (params?: StatQueryParams): Promise<BaseResponse<SalesAnalytics>> => {
    return request({
      url: '/admin/stat/sales/analytics',
      method: 'get',
      params
    })
  },

  // 获取订单分析数据
  getOrderAnalytics: (params?: StatQueryParams): Promise<BaseResponse<OrderAnalytics>> => {
    return request({
      url: '/admin/stat/order/analytics',
      method: 'get',
      params
    })
  },

  // 获取实时数据
  getRealTimeData: (): Promise<BaseResponse<{
    onlineUsers: number
    todayVisitors: number
    realTimeOrders: number
    realTimeRevenue: number
  }>> => {
    return request({
      url: '/admin/stat/realtime',
      method: 'get'
    })
  },

  // 获取销售趋势
  getSalesTrend: (params: StatQueryParams): Promise<BaseResponse<ChartData>> => {
    return request({
      url: '/admin/stat/sales/trend',
      method: 'get',
      params
    })
  },

  // 获取用户增长趋势
  getUserGrowthTrend: (params: StatQueryParams): Promise<BaseResponse<ChartData>> => {
    return request({
      url: '/admin/stat/user/growth',
      method: 'get',
      params
    })
  },

  // 获取商品销售排行
  getTopSellingGoods: (params?: {
    limit?: number
    startDate?: string
    endDate?: string
  }): Promise<BaseResponse<{
    id: number
    name: string
    salesCount: number
    revenue: number
    picUrl: string
  }[]>> => {
    return request({
      url: '/admin/stat/goods/top-selling',
      method: 'get',
      params
    })
  },

  // 获取地域分析
  getRegionAnalysis: (): Promise<BaseResponse<{
    region: string
    userCount: number
    orderCount: number
    revenue: number
  }[]>> => {
    return request({
      url: '/admin/stat/region/analysis',
      method: 'get'
    })
  },

  // 导出统计报告
  exportReport: (params: {
    type: 'user' | 'sales' | 'order' | 'goods'
    format: 'excel' | 'pdf'
    startDate?: string
    endDate?: string
  }): Promise<Blob> => {
    return request({
      url: '/admin/stat/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  }
}

export default statApi
