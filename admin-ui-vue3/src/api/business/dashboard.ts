import type { BaseResponse } from '@/api/types'
import request from '@/utils/request'

// 仪表盘相关类型定义
export interface DashboardOverview {
  // 核心指标
  totalUsers: number
  todayUsers: number
  totalOrders: number
  todayOrders: number
  totalSales: number
  todaySales: number
  totalGoods: number
  onlineGoods: number
  
  // 增长率
  userGrowthRate: number
  orderGrowthRate: number
  salesGrowthRate: number
  
  // 状态统计
  pendingOrders: number
  paidOrders: number
  shippedOrders: number
  completedOrders: number
  cancelledOrders: number
}

export interface SalesData {
  date: string
  sales: number
  orders: number
  users: number
}

export interface CategorySales {
  categoryId: number
  categoryName: string
  sales: number
  orders: number
  percentage: number
}

export interface GoodsRanking {
  goodsId: number
  goodsName: string
  goodsPicUrl: string
  sales: number
  orders: number
  revenue: number
  rank: number
}

export interface UserAnalysis {
  totalUsers: number
  activeUsers: number
  newUsers: number
  retentionRate: number
  // 地区分布
  regionDistribution: {
    region: string
    count: number
    percentage: number
  }[]
  // 年龄分布
  ageDistribution: {
    ageRange: string
    count: number
    percentage: number
  }[]
  // 性别分布
  genderDistribution: {
    gender: string
    count: number
    percentage: number
  }[]
}

export interface OrderAnalysis {
  totalOrders: number
  todayOrders: number
  avgOrderValue: number
  orderCompletionRate: number
  // 订单状态分布
  statusDistribution: {
    status: number
    statusName: string
    count: number
    percentage: number
  }[]
  // 支付方式分布
  paymentDistribution: {
    paymentType: string
    count: number
    amount: number
    percentage: number
  }[]
}

export interface RecentActivity {
  type: 'order' | 'user' | 'goods' | 'comment'
  title: string
  description: string
  time: string
  avatar?: string
  link?: string
}

export interface SystemStatus {
  // 系统信息
  systemInfo: {
    os: string
    cpu: string
    memory: string
    disk: string
    uptime: string
  }
  // 性能指标
  performance: {
    cpuUsage: number
    memoryUsage: number
    diskUsage: number
    networkIn: number
    networkOut: number
  }
  // 服务状态
  services: {
    name: string
    status: 'running' | 'stopped' | 'error'
    uptime: string
    cpu: number
    memory: number
  }[]
}

// 仪表盘 API
export const dashboardApi = {
  // 获取概览数据
  getOverview: (): Promise<BaseResponse<DashboardOverview>> => {
    return request({
      url: '/admin/dashboard/overview',
      method: 'get'
    })
  },

  // 获取销售趋势数据
  getSalesTrend: (params?: {
    period?: 'week' | 'month' | 'quarter' | 'year'
    startDate?: string
    endDate?: string
  }): Promise<BaseResponse<SalesData[]>> => {
    return request({
      url: '/admin/dashboard/sales/trend',
      method: 'get',
      params
    })
  },

  // 获取分类销售排行
  getCategorySales: (params?: {
    period?: 'week' | 'month' | 'quarter' | 'year'
    limit?: number
  }): Promise<BaseResponse<CategorySales[]>> => {
    return request({
      url: '/admin/dashboard/category/sales',
      method: 'get',
      params
    })
  },

  // 获取商品销售排行
  getGoodsRanking: (params?: {
    type?: 'sales' | 'orders' | 'revenue'
    period?: 'week' | 'month' | 'quarter' | 'year'
    limit?: number
  }): Promise<BaseResponse<GoodsRanking[]>> => {
    return request({
      url: '/admin/dashboard/goods/ranking',
      method: 'get',
      params
    })
  },

  // 获取用户分析数据
  getUserAnalysis: (params?: {
    period?: 'week' | 'month' | 'quarter' | 'year'
  }): Promise<BaseResponse<UserAnalysis>> => {
    return request({
      url: '/admin/dashboard/user/analysis',
      method: 'get',
      params
    })
  },

  // 获取订单分析数据
  getOrderAnalysis: (params?: {
    period?: 'week' | 'month' | 'quarter' | 'year'
  }): Promise<BaseResponse<OrderAnalysis>> => {
    return request({
      url: '/admin/dashboard/order/analysis',
      method: 'get',
      params
    })
  },

  // 获取最近活动
  getRecentActivities: (params?: {
    limit?: number
    types?: string[]
  }): Promise<BaseResponse<RecentActivity[]>> => {
    return request({
      url: '/admin/dashboard/activities',
      method: 'get',
      params
    })
  },

  // 获取系统状态
  getSystemStatus: (): Promise<BaseResponse<SystemStatus>> => {
    return request({
      url: '/admin/dashboard/system/status',
      method: 'get'
    })
  },

  // 获取实时数据
  getRealTimeData: (): Promise<BaseResponse<{
    onlineUsers: number
    todayOrders: number
    todaySales: number
    todayVisits: number
    recentOrders: {
      orderSn: string
      userName: string
      amount: number
      time: string
    }[]
    recentUsers: {
      username: string
      nickname: string
      avatar: string
      registerTime: string
    }[]
  }>> => {
    return request({
      url: '/admin/dashboard/realtime',
      method: 'get'
    })
  },

  // 获取热门搜索关键词
  getHotKeywords: (params?: {
    limit?: number
    period?: 'week' | 'month'
  }): Promise<BaseResponse<{
    keyword: string
    count: number
    trend: 'up' | 'down' | 'stable'
  }[]>> => {
    return request({
      url: '/admin/dashboard/hot/keywords',
      method: 'get',
      params
    })
  },

  // 获取地区销售分布
  getRegionSales: (params?: {
    period?: 'week' | 'month' | 'quarter' | 'year'
    limit?: number
  }): Promise<BaseResponse<{
    region: string
    provinceCode: string
    sales: number
    orders: number
    users: number
  }[]>> => {
    return request({
      url: '/admin/dashboard/region/sales',
      method: 'get',
      params
    })
  },

  // 获取转化漏斗数据
  getConversionFunnel: (params?: {
    period?: 'week' | 'month' | 'quarter' | 'year'
  }): Promise<BaseResponse<{
    visits: number
    views: number
    carts: number
    orders: number
    payments: number
    conversions: {
      visitToView: number
      viewToCart: number
      cartToOrder: number
      orderToPayment: number
    }
  }>> => {
    return request({
      url: '/admin/dashboard/conversion/funnel',
      method: 'get',
      params
    })
  },

  // 获取库存预警
  getStockAlerts: (): Promise<BaseResponse<{
    lowStock: {
      goodsId: number
      goodsName: string
      goodsPicUrl: string
      currentStock: number
      minStock: number
      status: 'warning' | 'danger'
    }[]
    outOfStock: {
      goodsId: number
      goodsName: string
      goodsPicUrl: string
      lastSaleTime: string
    }[]
  }>> => {
    return request({
      url: '/admin/dashboard/stock/alerts',
      method: 'get'
    })
  },

  // 导出仪表盘报告
  exportReport: (params: {
    type: 'overview' | 'sales' | 'user' | 'order'
    period: 'week' | 'month' | 'quarter' | 'year'
    format: 'pdf' | 'excel'
    startDate?: string
    endDate?: string
  }): Promise<Blob> => {
    return request({
      url: '/admin/dashboard/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  }
}

export default dashboardApi
