import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 用户关系相关类型定义
export interface UserRelationInfo {
  id?: number
  userId: number
  parentId: number
  level: number
  path: string
  directChildren: number
  totalChildren: number
  addTime?: string
  updateTime?: string
  deleted?: boolean
  // 关联数据
  user?: {
    id: number
    username: string
    nickname: string
    mobile: string
    avatar: string
    userLevel: number
  }
  parent?: {
    id: number
    username: string
    nickname: string
    mobile: string
    avatar: string
  }
  children?: UserRelationInfo[]
}

export interface UserRelationCreateParams {
  userId: number
  parentId: number
}

export interface UserRelationUpdateParams {
  id: number
  parentId: number
}

export interface UserRelationListParams extends PaginationParams {
  userId?: number
  parentId?: number
  level?: number
  username?: string
  mobile?: string
}

export interface UserRelationListResponse extends PaginationResponse {
  list: UserRelationInfo[]
}

export interface UserRelationTreeNode {
  id: number
  userId: number
  parentId: number
  level: number
  username: string
  nickname: string
  mobile: string
  avatar: string
  userLevel: number
  directChildren: number
  totalChildren: number
  children?: UserRelationTreeNode[]
}

export interface RelationStatistics {
  totalUsers: number
  totalRelations: number
  maxLevel: number
  avgChildrenPerUser: number
  levelDistribution: { [key: number]: number }
  topReferrers: {
    userId: number
    username: string
    nickname: string
    directChildren: number
    totalChildren: number
  }[]
}

// 用户关系管理 API
export const userRelationApi = {
  // 获取用户关系列表
  list: (params: UserRelationListParams): Promise<BaseResponse<UserRelationListResponse>> => {
    return request({
      url: '/admin/user/relation/list',
      method: 'get',
      params
    })
  },

  // 获取用户关系详情
  detail: (id: number): Promise<BaseResponse<UserRelationInfo>> => {
    return request({
      url: `/admin/user/relation/detail/${id}`,
      method: 'get'
    })
  },

  // 创建用户关系
  create: (data: UserRelationCreateParams): Promise<BaseResponse<UserRelationInfo>> => {
    return request({
      url: '/admin/user/relation/create',
      method: 'post',
      data
    })
  },

  // 更新用户关系
  update: (data: UserRelationUpdateParams): Promise<BaseResponse<UserRelationInfo>> => {
    return request({
      url: '/admin/user/relation/update',
      method: 'post',
      data
    })
  },

  // 删除用户关系
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/user/relation/delete',
      method: 'post',
      data: { id }
    })
  },

  // 获取用户关系树
  getTree: (userId?: number): Promise<BaseResponse<UserRelationTreeNode[]>> => {
    return request({
      url: '/admin/user/relation/tree',
      method: 'get',
      params: { userId }
    })
  },

  // 获取用户的上级关系链
  getParentChain: (userId: number): Promise<BaseResponse<UserRelationInfo[]>> => {
    return request({
      url: `/admin/user/relation/parent-chain/${userId}`,
      method: 'get'
    })
  },

  // 获取用户的直接下级
  getDirectChildren: (userId: number): Promise<BaseResponse<UserRelationInfo[]>> => {
    return request({
      url: `/admin/user/relation/direct-children/${userId}`,
      method: 'get'
    })
  },

  // 获取用户的所有下级
  getAllChildren: (userId: number, maxLevel?: number): Promise<BaseResponse<UserRelationInfo[]>> => {
    return request({
      url: `/admin/user/relation/all-children/${userId}`,
      method: 'get',
      params: { maxLevel }
    })
  },

  // 根据级别获取用户关系
  getByLevel: (level: number): Promise<BaseResponse<UserRelationInfo[]>> => {
    return request({
      url: `/admin/user/relation/level/${level}`,
      method: 'get'
    })
  },

  // 移动用户到新的上级
  moveUser: (userId: number, newParentId: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/user/relation/move',
      method: 'post',
      data: { userId, newParentId }
    })
  },

  // 批量设置用户关系
  batchSetRelation: (relations: { userId: number; parentId: number }[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/user/relation/batch/set',
      method: 'post',
      data: { relations }
    })
  },

  // 重建用户关系树（修复数据）
  rebuildTree: (): Promise<BaseResponse<{
    totalProcessed: number
    errors: string[]
  }>> => {
    return request({
      url: '/admin/user/relation/rebuild',
      method: 'post'
    })
  },

  // 验证用户关系完整性
  validateIntegrity: (): Promise<BaseResponse<{
    valid: boolean
    errors: {
      type: string
      userId: number
      message: string
    }[]
  }>> => {
    return request({
      url: '/admin/user/relation/validate',
      method: 'get'
    })
  },

  // 获取关系统计信息
  getStatistics: (): Promise<BaseResponse<RelationStatistics>> => {
    return request({
      url: '/admin/user/relation/statistics',
      method: 'get'
    })
  },

  // 搜索用户关系
  search: (keyword: string): Promise<BaseResponse<UserRelationInfo[]>> => {
    return request({
      url: '/admin/user/relation/search',
      method: 'get',
      params: { keyword }
    })
  },

  // 导出用户关系数据
  exportData: (format: 'excel' | 'csv' = 'excel'): Promise<Blob> => {
    return request({
      url: '/admin/user/relation/export',
      method: 'get',
      params: { format },
      responseType: 'blob'
    })
  },

  // 导入用户关系数据
  importData: (file: File): Promise<BaseResponse<{
    success: number
    failed: number
    errors: string[]
  }>> => {
    const formData = new FormData()
    formData.append('file', file)
    
    return request({
      url: '/admin/user/relation/import',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取推荐人排行
  getTopReferrers: (params?: {
    limit?: number
    timeRange?: string
  }): Promise<BaseResponse<{
    userId: number
    username: string
    nickname: string
    directChildren: number
    totalChildren: number
    recentChildren: number
  }[]>> => {
    return request({
      url: '/admin/user/relation/top-referrers',
      method: 'get',
      params
    })
  }
}

export default userRelationApi
