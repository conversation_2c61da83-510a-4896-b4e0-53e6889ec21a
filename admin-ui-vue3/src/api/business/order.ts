import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 订单相关类型定义
export interface OrderInfo {
  id?: number
  userId: number
  orderSn: string
  orderStatus: number
  aftersaleStatus: number
  consignee: string
  mobile: string
  address: string
  message?: string
  goodsPrice: number
  freightPrice: number
  couponPrice: number
  integralPrice: number
  grouponPrice: number
  orderPrice: number
  actualPrice: number
  payId?: string
  payTime?: string
  shipSn?: string
  shipChannel?: string
  shipTime?: string
  refundAmount?: number
  refundType?: string
  refundContent?: string
  refundTime?: string
  confirmTime?: string
  comments?: number
  endTime?: string
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface OrderGoods {
  id?: number
  orderId: number
  goodsId: number
  goodsName: string
  goodsSn: string
  productId: number
  number: number
  price: number
  specifications: string[]
  picUrl: string
  comment?: number
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface OrderListParams extends PaginationParams {
  userId?: number
  orderSn?: string
  orderStatus?: number
  consignee?: string
  mobile?: string
  startTime?: string
  endTime?: string
}

export interface OrderListResponse extends PaginationResponse {
  list: OrderInfo[]
}

export interface OrderDetailResponse extends BaseResponse {
  data: {
    order: OrderInfo
    orderGoods: OrderGoods[]
    user: {
      id: number
      username: string
      mobile: string
      avatar: string
    }
  }
}

export interface ShipParams {
  orderId: number
  shipSn: string
  shipChannel: string
}

export interface RefundParams {
  orderId: number
  refundMoney: number
  refundType: string
  refundContent: string
}

// 订单状态枚举
export enum OrderStatus {
  PENDING = 101, // 待付款
  PAID = 201, // 已付款
  SHIPPED = 301, // 已发货
  CONFIRMED = 401, // 用户确认收货
  CANCELLED = 102, // 已取消
  AUTO_CANCELLED = 103 // 系统取消
}

// 售后状态枚举
export enum AftersaleStatus {
  NONE = 0, // 无售后
  PENDING = 1, // 待处理
  PROCESSING = 2, // 处理中
  COMPLETED = 3, // 已完成
  REJECTED = 4 // 已拒绝
}

// 订单管理 API
export const orderApi = {
  // 获取订单列表
  list: (params: OrderListParams): Promise<BaseResponse<OrderListResponse>> => {
    return request({
      url: '/admin/order/list',
      method: 'get',
      params
    })
  },

  // 获取订单详情
  detail: (id: number): Promise<OrderDetailResponse> => {
    return request({
      url: `/admin/order/detail/${id}`,
      method: 'get'
    })
  },

  // 发货
  ship: (data: ShipParams): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/order/ship',
      method: 'post',
      data
    })
  },

  // 退款
  refund: (data: RefundParams): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/order/refund',
      method: 'post',
      data
    })
  },

  // 删除订单
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/order/delete',
      method: 'post',
      data: { id }
    })
  },

  // 回复订单留言
  reply: (orderId: number, replyContent: string): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/order/reply',
      method: 'post',
      data: { orderId, replyContent }
    })
  },

  // 获取订单统计
  statistics: (): Promise<BaseResponse<{
    totalOrders: number
    pendingOrders: number
    paidOrders: number
    shippedOrders: number
    totalAmount: number
    todayOrders: number
    todayAmount: number
  }>> => {
    return request({
      url: '/admin/order/statistics',
      method: 'get'
    })
  },

  // 导出订单
  export: (params: OrderListParams): Promise<Blob> => {
    return request({
      url: '/admin/order/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  }
}

export default orderApi
