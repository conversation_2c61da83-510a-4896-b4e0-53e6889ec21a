import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 商品相关类型定义
export interface GoodsInfo {
  id?: number
  goodsSn: string
  name: string
  categoryId: number
  brandId?: number
  gallery: string[]
  keywords: string
  brief: string
  isOnSale: boolean
  sortOrder: number
  picUrl: string
  shareUrl?: string
  isNew: boolean
  isHot: boolean
  unit: string
  counterPrice: number
  retailPrice: number
  detail: string
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface GoodsSpecification {
  id?: number
  goodsId: number
  specification: string
  value: string
  picUrl?: string
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface GoodsAttribute {
  id?: number
  goodsId: number
  attribute: string
  value: string
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface GoodsProduct {
  id?: number
  goodsId: number
  specifications: string[]
  price: number
  number: number
  url?: string
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface GoodsCreateParams {
  goods: Omit<GoodsInfo, 'id' | 'addTime' | 'updateTime' | 'deleted'>
  specifications?: Omit<GoodsSpecification, 'id' | 'goodsId' | 'addTime' | 'updateTime' | 'deleted'>[]
  attributes?: Omit<GoodsAttribute, 'id' | 'goodsId' | 'addTime' | 'updateTime' | 'deleted'>[]
  products?: Omit<GoodsProduct, 'id' | 'goodsId' | 'addTime' | 'updateTime' | 'deleted'>[]
}

export interface GoodsUpdateParams extends GoodsCreateParams {
  id: number
}

export interface GoodsListParams extends PaginationParams {
  goodsSn?: string
  name?: string
  categoryId?: number
  brandId?: number
  isOnSale?: boolean
  isNew?: boolean
  isHot?: boolean
}

export interface GoodsListResponse extends PaginationResponse {
  list: GoodsInfo[]
}

export interface GoodsDetailResponse extends BaseResponse {
  data: {
    goods: GoodsInfo
    specifications: GoodsSpecification[]
    attributes: GoodsAttribute[]
    products: GoodsProduct[]
  }
}

// 商品管理 API
export const goodsApi = {
  // 获取商品列表
  list: (params: GoodsListParams): Promise<BaseResponse<GoodsListResponse>> => {
    return request({
      url: '/admin/goods/list',
      method: 'get',
      params
    })
  },

  // 获取商品详情
  detail: (id: number): Promise<GoodsDetailResponse> => {
    return request({
      url: `/admin/goods/detail/${id}`,
      method: 'get'
    })
  },

  // 创建商品
  create: (data: GoodsCreateParams): Promise<BaseResponse<GoodsInfo>> => {
    return request({
      url: '/admin/goods/create',
      method: 'post',
      data
    })
  },

  // 更新商品
  update: (data: GoodsUpdateParams): Promise<BaseResponse<GoodsInfo>> => {
    return request({
      url: '/admin/goods/update',
      method: 'post',
      data
    })
  },

  // 删除商品
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/goods/delete',
      method: 'post',
      data: { id }
    })
  },

  // 上架商品
  onSale: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/goods/onsale',
      method: 'post',
      data: { id }
    })
  },

  // 下架商品
  offSale: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/goods/offsale',
      method: 'post',
      data: { id }
    })
  },

  // 设置新品
  setNew: (id: number, isNew: boolean): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/goods/new',
      method: 'post',
      data: { id, isNew }
    })
  },

  // 设置热门
  setHot: (id: number, isHot: boolean): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/goods/hot',
      method: 'post',
      data: { id, isHot }
    })
  },

  // 批量操作
  batchOperation: (ids: number[], operation: 'delete' | 'onsale' | 'offsale'): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/goods/batch',
      method: 'post',
      data: { ids, operation }
    })
  }
}

export default goodsApi
