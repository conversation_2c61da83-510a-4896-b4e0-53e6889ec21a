import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 地区相关类型定义
export interface RegionInfo {
  id?: number
  pid: number
  name: string
  type: number
  code: number
  addTime?: string
  updateTime?: string
  deleted?: boolean
  // 关联数据
  children?: RegionInfo[]
  parent?: RegionInfo
}

export interface RegionCreateParams {
  pid: number
  name: string
  type: number
  code: number
}

export interface RegionUpdateParams extends RegionCreateParams {
  id: number
}

export interface RegionListParams extends PaginationParams {
  pid?: number
  name?: string
  type?: number
  code?: number
}

export interface RegionListResponse extends PaginationResponse {
  list: RegionInfo[]
}

export interface RegionTreeNode {
  id: number
  pid: number
  name: string
  type: number
  code: number
  children?: RegionTreeNode[]
}

// 地区类型枚举
export enum RegionType {
  COUNTRY = 1, // 国家
  PROVINCE = 2, // 省份
  CITY = 3, // 城市
  DISTRICT = 4 // 区县
}

// 地区管理 API
export const regionApi = {
  // 获取地区列表
  list: (params: RegionListParams): Promise<BaseResponse<RegionListResponse>> => {
    return request({
      url: '/admin/region/list',
      method: 'get',
      params
    })
  },

  // 获取所有地区（不分页）
  all: (): Promise<BaseResponse<RegionInfo[]>> => {
    return request({
      url: '/admin/region/all',
      method: 'get'
    })
  },

  // 获取地区详情
  detail: (id: number): Promise<BaseResponse<RegionInfo>> => {
    return request({
      url: `/admin/region/detail/${id}`,
      method: 'get'
    })
  },

  // 创建地区
  create: (data: RegionCreateParams): Promise<BaseResponse<RegionInfo>> => {
    return request({
      url: '/admin/region/create',
      method: 'post',
      data
    })
  },

  // 更新地区
  update: (data: RegionUpdateParams): Promise<BaseResponse<RegionInfo>> => {
    return request({
      url: '/admin/region/update',
      method: 'post',
      data
    })
  },

  // 删除地区
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/region/delete',
      method: 'post',
      data: { id }
    })
  },

  // 获取地区树
  tree: (): Promise<BaseResponse<RegionTreeNode[]>> => {
    return request({
      url: '/admin/region/tree',
      method: 'get'
    })
  },

  // 根据父级ID获取子地区
  getChildren: (pid: number): Promise<BaseResponse<RegionInfo[]>> => {
    return request({
      url: `/admin/region/children/${pid}`,
      method: 'get'
    })
  },

  // 根据类型获取地区
  getByType: (type: number): Promise<BaseResponse<RegionInfo[]>> => {
    return request({
      url: `/admin/region/type/${type}`,
      method: 'get'
    })
  },

  // 搜索地区
  search: (keyword: string): Promise<BaseResponse<RegionInfo[]>> => {
    return request({
      url: '/admin/region/search',
      method: 'get',
      params: { keyword }
    })
  },

  // 获取省份列表
  getProvinces: (): Promise<BaseResponse<RegionInfo[]>> => {
    return request({
      url: '/admin/region/provinces',
      method: 'get'
    })
  },

  // 根据省份获取城市
  getCitiesByProvince: (provinceId: number): Promise<BaseResponse<RegionInfo[]>> => {
    return request({
      url: `/admin/region/cities/${provinceId}`,
      method: 'get'
    })
  },

  // 根据城市获取区县
  getDistrictsByCity: (cityId: number): Promise<BaseResponse<RegionInfo[]>> => {
    return request({
      url: `/admin/region/districts/${cityId}`,
      method: 'get'
    })
  },

  // 获取完整地址路径
  getFullPath: (id: number): Promise<BaseResponse<RegionInfo[]>> => {
    return request({
      url: `/admin/region/fullpath/${id}`,
      method: 'get'
    })
  }
}

export default regionApi
