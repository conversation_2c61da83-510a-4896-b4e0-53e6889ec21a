import type { BaseResponse, Pa<PERSON>ationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 订单佣金相关类型定义
export interface OrderCommissionInfo {
  id?: number
  orderId: number
  userId: number
  referrerId: number
  goodsId: number
  commissionRuleId: number
  level: number
  orderAmount: number
  commissionRate: number
  commissionAmount: number
  status: number
  settlementTime?: string
  remark?: string
  addTime?: string
  updateTime?: string
  deleted?: boolean
  // 关联数据
  order?: {
    id: number
    orderSn: string
    orderStatus: number
    actualPrice: number
    payTime: string
  }
  user?: {
    id: number
    username: string
    nickname: string
    mobile: string
    avatar: string
  }
  referrer?: {
    id: number
    username: string
    nickname: string
    mobile: string
    avatar: string
  }
  goods?: {
    id: number
    name: string
    picUrl: string
    retailPrice: number
  }
  commissionRule?: {
    id: number
    name: string
    type: number
    rate: number
  }
}

export interface OrderCommissionCreateParams {
  orderId: number
  userId: number
  referrerId: number
  goodsId: number
  commissionRuleId: number
  level: number
  orderAmount: number
  commissionRate: number
  commissionAmount: number
  remark?: string
}

export interface OrderCommissionUpdateParams {
  id: number
  status?: number
  remark?: string
}

export interface OrderCommissionListParams extends PaginationParams {
  orderId?: number
  userId?: number
  referrerId?: number
  goodsId?: number
  level?: number
  status?: number
  startTime?: string
  endTime?: string
  username?: string
  referrerName?: string
  orderSn?: string
}

export interface OrderCommissionListResponse extends PaginationResponse {
  list: OrderCommissionInfo[]
}

export interface CommissionSettleParams {
  ids: number[]
  remark?: string
}

export interface CommissionCalculateParams {
  orderId: number
  userId: number
}

export interface CommissionCalculateResult {
  orderId: number
  userId: number
  totalCommission: number
  commissions: {
    level: number
    referrerId: number
    referrerName: string
    goodsId: number
    goodsName: string
    ruleId: number
    ruleName: string
    rate: number
    amount: number
    commission: number
  }[]
}

// 佣金状态枚举
export enum CommissionStatus {
  PENDING = 0, // 待结算
  SETTLED = 1, // 已结算
  CANCELLED = 2, // 已取消
  FROZEN = 3 // 已冻结
}

// 订单佣金管理 API
export const orderCommissionApi = {
  // 获取订单佣金列表
  list: (params: OrderCommissionListParams): Promise<BaseResponse<OrderCommissionListResponse>> => {
    return request({
      url: '/admin/order/commission/list',
      method: 'get',
      params
    })
  },

  // 获取订单佣金详情
  detail: (id: number): Promise<BaseResponse<OrderCommissionInfo>> => {
    return request({
      url: `/admin/order/commission/detail/${id}`,
      method: 'get'
    })
  },

  // 创建订单佣金
  create: (data: OrderCommissionCreateParams): Promise<BaseResponse<OrderCommissionInfo>> => {
    return request({
      url: '/admin/order/commission/create',
      method: 'post',
      data
    })
  },

  // 更新订单佣金
  update: (data: OrderCommissionUpdateParams): Promise<BaseResponse<OrderCommissionInfo>> => {
    return request({
      url: '/admin/order/commission/update',
      method: 'post',
      data
    })
  },

  // 删除订单佣金
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/order/commission/delete',
      method: 'post',
      data: { id }
    })
  },

  // 计算订单佣金
  calculate: (params: CommissionCalculateParams): Promise<BaseResponse<CommissionCalculateResult>> => {
    return request({
      url: '/admin/order/commission/calculate',
      method: 'post',
      data: params
    })
  },

  // 生成订单佣金
  generate: (orderId: number): Promise<BaseResponse<{
    orderId: number
    commissions: OrderCommissionInfo[]
    totalAmount: number
  }>> => {
    return request({
      url: '/admin/order/commission/generate',
      method: 'post',
      data: { orderId }
    })
  },

  // 批量生成订单佣金
  batchGenerate: (orderIds: number[]): Promise<BaseResponse<{
    success: number
    failed: number
    results: {
      orderId: number
      success: boolean
      error?: string
      commissions?: OrderCommissionInfo[]
    }[]
  }>> => {
    return request({
      url: '/admin/order/commission/batch/generate',
      method: 'post',
      data: { orderIds }
    })
  },

  // 结算佣金
  settle: (data: CommissionSettleParams): Promise<BaseResponse<{
    success: number
    failed: number
    totalAmount: number
  }>> => {
    return request({
      url: '/admin/order/commission/settle',
      method: 'post',
      data
    })
  },

  // 取消佣金
  cancel: (ids: number[], remark?: string): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/order/commission/cancel',
      method: 'post',
      data: { ids, remark }
    })
  },

  // 冻结佣金
  freeze: (ids: number[], remark?: string): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/order/commission/freeze',
      method: 'post',
      data: { ids, remark }
    })
  },

  // 解冻佣金
  unfreeze: (ids: number[], remark?: string): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/order/commission/unfreeze',
      method: 'post',
      data: { ids, remark }
    })
  },

  // 根据订单获取佣金
  getByOrder: (orderId: number): Promise<BaseResponse<OrderCommissionInfo[]>> => {
    return request({
      url: `/admin/order/commission/order/${orderId}`,
      method: 'get'
    })
  },

  // 根据用户获取佣金
  getByUser: (userId: number, params?: {
    status?: number
    startTime?: string
    endTime?: string
    page?: number
    limit?: number
  }): Promise<BaseResponse<OrderCommissionListResponse>> => {
    return request({
      url: `/admin/order/commission/user/${userId}`,
      method: 'get',
      params
    })
  },

  // 根据推荐人获取佣金
  getByReferrer: (referrerId: number, params?: {
    status?: number
    startTime?: string
    endTime?: string
    page?: number
    limit?: number
  }): Promise<BaseResponse<OrderCommissionListResponse>> => {
    return request({
      url: `/admin/order/commission/referrer/${referrerId}`,
      method: 'get',
      params
    })
  },

  // 获取佣金统计
  getStatistics: (params?: {
    startTime?: string
    endTime?: string
    userId?: number
    referrerId?: number
  }): Promise<BaseResponse<{
    totalCommissions: number
    totalAmount: number
    pendingCommissions: number
    pendingAmount: number
    settledCommissions: number
    settledAmount: number
    levelDistribution: { [key: number]: { count: number; amount: number } }
    monthlyTrend: { month: string; count: number; amount: number }[]
    topReferrers: {
      referrerId: number
      referrerName: string
      commissions: number
      amount: number
    }[]
  }>> => {
    return request({
      url: '/admin/order/commission/statistics',
      method: 'get',
      params
    })
  },

  // 导出佣金数据
  export: (params: OrderCommissionListParams & {
    format?: 'excel' | 'csv'
  }): Promise<Blob> => {
    return request({
      url: '/admin/order/commission/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  },

  // 重新计算佣金
  recalculate: (orderId: number): Promise<BaseResponse<{
    orderId: number
    oldCommissions: OrderCommissionInfo[]
    newCommissions: OrderCommissionInfo[]
    changes: string[]
  }>> => {
    return request({
      url: '/admin/order/commission/recalculate',
      method: 'post',
      data: { orderId }
    })
  }
}

export default orderCommissionApi
