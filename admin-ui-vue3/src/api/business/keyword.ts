import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 关键词相关类型定义
export interface KeywordInfo {
  id?: number
  keyword: string
  url: string
  isHot: boolean
  isDefault: boolean
  sortOrder: number
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface KeywordCreateParams {
  keyword: string
  url: string
  isHot: boolean
  isDefault: boolean
  sortOrder: number
}

export interface KeywordUpdateParams extends KeywordCreateParams {
  id: number
}

export interface KeywordListParams extends PaginationParams {
  keyword?: string
  url?: string
  isHot?: boolean
  isDefault?: boolean
}

export interface KeywordListResponse extends PaginationResponse {
  list: KeywordInfo[]
}

// 关键词管理 API
export const keywordApi = {
  // 获取关键词列表
  list: (params: KeywordListParams): Promise<BaseResponse<KeywordListResponse>> => {
    return request({
      url: '/admin/keyword/list',
      method: 'get',
      params
    })
  },

  // 获取所有关键词（不分页）
  all: (): Promise<BaseResponse<KeywordInfo[]>> => {
    return request({
      url: '/admin/keyword/all',
      method: 'get'
    })
  },

  // 获取关键词详情
  detail: (id: number): Promise<BaseResponse<KeywordInfo>> => {
    return request({
      url: `/admin/keyword/detail/${id}`,
      method: 'get'
    })
  },

  // 创建关键词
  create: (data: KeywordCreateParams): Promise<BaseResponse<KeywordInfo>> => {
    return request({
      url: '/admin/keyword/create',
      method: 'post',
      data
    })
  },

  // 更新关键词
  update: (data: KeywordUpdateParams): Promise<BaseResponse<KeywordInfo>> => {
    return request({
      url: '/admin/keyword/update',
      method: 'post',
      data
    })
  },

  // 删除关键词
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/keyword/delete',
      method: 'post',
      data: { id }
    })
  },

  // 批量删除关键词
  batchDelete: (ids: number[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/keyword/batch/delete',
      method: 'post',
      data: { ids }
    })
  },

  // 设置热门关键词
  setHot: (id: number, isHot: boolean): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/keyword/hot',
      method: 'post',
      data: { id, isHot }
    })
  },

  // 设置默认关键词
  setDefault: (id: number, isDefault: boolean): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/keyword/default',
      method: 'post',
      data: { id, isDefault }
    })
  },

  // 批量更新排序
  updateSort: (keywords: { id: number; sortOrder: number }[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/keyword/sort',
      method: 'post',
      data: { keywords }
    })
  },

  // 获取热门关键词
  getHotKeywords: (): Promise<BaseResponse<KeywordInfo[]>> => {
    return request({
      url: '/admin/keyword/hot',
      method: 'get'
    })
  },

  // 获取默认关键词
  getDefaultKeywords: (): Promise<BaseResponse<KeywordInfo[]>> => {
    return request({
      url: '/admin/keyword/default',
      method: 'get'
    })
  }
}

export default keywordApi
