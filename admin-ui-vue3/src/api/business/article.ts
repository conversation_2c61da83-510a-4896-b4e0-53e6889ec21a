import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 文章相关类型定义
export interface ArticleInfo {
  id?: number
  title: string
  subtitle?: string
  content: string
  picUrl?: string
  sortOrder: number
  goods: number[]
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface ArticleCreateParams {
  title: string
  subtitle?: string
  content: string
  picUrl?: string
  sortOrder: number
  goods: number[]
}

export interface ArticleUpdateParams extends ArticleCreateParams {
  id: number
}

export interface ArticleListParams extends PaginationParams {
  title?: string
  subtitle?: string
  sortOrder?: number
}

export interface ArticleListResponse extends PaginationResponse {
  list: ArticleInfo[]
}

export interface ArticleDetailResponse extends BaseResponse {
  data: {
    article: ArticleInfo
    goods: {
      id: number
      name: string
      picUrl: string
      retailPrice: number
      counterPrice: number
    }[]
  }
}

// 文章管理 API
export const articleApi = {
  // 获取文章列表
  list: (params: ArticleListParams): Promise<BaseResponse<ArticleListResponse>> => {
    return request({
      url: '/admin/article/list',
      method: 'get',
      params
    })
  },

  // 获取所有文章（不分页）
  all: (): Promise<BaseResponse<ArticleInfo[]>> => {
    return request({
      url: '/admin/article/all',
      method: 'get'
    })
  },

  // 获取文章详情
  detail: (id: number): Promise<ArticleDetailResponse> => {
    return request({
      url: `/admin/article/detail/${id}`,
      method: 'get'
    })
  },

  // 创建文章
  create: (data: ArticleCreateParams): Promise<BaseResponse<ArticleInfo>> => {
    return request({
      url: '/admin/article/create',
      method: 'post',
      data
    })
  },

  // 更新文章
  update: (data: ArticleUpdateParams): Promise<BaseResponse<ArticleInfo>> => {
    return request({
      url: '/admin/article/update',
      method: 'post',
      data
    })
  },

  // 删除文章
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/article/delete',
      method: 'post',
      data: { id }
    })
  },

  // 批量删除文章
  batchDelete: (ids: number[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/article/batch/delete',
      method: 'post',
      data: { ids }
    })
  },

  // 批量更新排序
  updateSort: (articles: { id: number; sortOrder: number }[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/article/sort',
      method: 'post',
      data: { articles }
    })
  },

  // 获取文章统计
  statistics: (): Promise<BaseResponse<{
    totalArticles: number
    todayArticles: number
    totalViews: number
    averageLength: number
  }>> => {
    return request({
      url: '/admin/article/statistics',
      method: 'get'
    })
  }
}

export default articleApi
