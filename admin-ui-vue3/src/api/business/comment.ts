import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 评论相关类型定义
export interface CommentInfo {
  id?: number
  userId: number
  type: number
  valueId: number
  content: string
  adminContent?: string
  picUrls: string[]
  star: number
  addTime?: string
  updateTime?: string
  deleted?: boolean
  // 关联数据
  user?: {
    id: number
    username: string
    nickname: string
    avatar: string
  }
  // 评论对象信息
  valueInfo?: {
    id: number
    name: string
    picUrl?: string
  }
}

export interface CommentCreateParams {
  userId: number
  type: number
  valueId: number
  content: string
  picUrls: string[]
  star: number
}

export interface CommentUpdateParams {
  id: number
  content?: string
  adminContent?: string
  star?: number
}

export interface CommentListParams extends PaginationParams {
  userId?: number
  type?: number
  valueId?: number
  star?: number
  content?: string
  addTime?: string[]
}

export interface CommentListResponse extends PaginationResponse {
  list: CommentInfo[]
}

// 评论类型枚举
export enum CommentType {
  GOODS = 0, // 商品评论
  TOPIC = 1, // 专题评论
  ARTICLE = 2 // 文章评论
}

// 评论管理 API
export const commentApi = {
  // 获取评论列表
  list: (params: CommentListParams): Promise<BaseResponse<CommentListResponse>> => {
    return request({
      url: '/admin/comment/list',
      method: 'get',
      params
    })
  },

  // 获取评论详情
  detail: (id: number): Promise<BaseResponse<CommentInfo>> => {
    return request({
      url: `/admin/comment/detail/${id}`,
      method: 'get'
    })
  },

  // 回复评论
  reply: (id: number, adminContent: string): Promise<BaseResponse<CommentInfo>> => {
    return request({
      url: '/admin/comment/reply',
      method: 'post',
      data: { id, adminContent }
    })
  },

  // 删除评论
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/comment/delete',
      method: 'post',
      data: { id }
    })
  },

  // 批量删除评论
  batchDelete: (ids: number[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/comment/batch/delete',
      method: 'post',
      data: { ids }
    })
  },

  // 获取评论统计
  statistics: (): Promise<BaseResponse<{
    totalComments: number
    todayComments: number
    averageStar: number
    starDistribution: { [key: number]: number }
  }>> => {
    return request({
      url: '/admin/comment/statistics',
      method: 'get'
    })
  }
}

export default commentApi
