import type { BaseResponse } from '@/api/types'
import request from '@/utils/request'

// 个人资料相关类型定义
export interface ProfileInfo {
  id: number
  username: string
  nickname: string
  email: string
  mobile: string
  avatar: string
  gender: number
  birthday: string
  signature: string
  department: string
  position: string
  lastLoginTime: string
  lastLoginIp: string
  status: number
  addTime: string
  updateTime: string
}

export interface ProfileUpdateParams {
  nickname?: string
  email?: string
  mobile?: string
  avatar?: string
  gender?: number
  birthday?: string
  signature?: string
  department?: string
  position?: string
}

export interface PasswordChangeParams {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

export interface SecuritySettings {
  // 登录设置
  loginSettings: {
    enableTwoFactor: boolean
    enableEmailLogin: boolean
    enableMobileLogin: boolean
    sessionTimeout: number
    maxLoginAttempts: number
  }
  // 安全日志
  securityLogs: {
    id: number
    type: string
    action: string
    ip: string
    userAgent: string
    location: string
    result: 'success' | 'failed'
    time: string
    remark: string
  }[]
  // 登录设备
  loginDevices: {
    id: string
    deviceType: string
    deviceName: string
    browser: string
    os: string
    ip: string
    location: string
    lastActiveTime: string
    isCurrent: boolean
  }[]
}

export interface NotificationSettings {
  // 系统通知
  systemNotifications: {
    orderNotification: boolean
    userNotification: boolean
    goodsNotification: boolean
    commentNotification: boolean
    systemMaintenance: boolean
  }
  // 邮件通知
  emailNotifications: {
    loginAlert: boolean
    passwordChange: boolean
    dataExport: boolean
    systemError: boolean
    weeklyReport: boolean
  }
  // 短信通知
  smsNotifications: {
    loginAlert: boolean
    passwordChange: boolean
    emergencyAlert: boolean
  }
}

export interface OperationLog {
  id: number
  module: string
  action: string
  description: string
  ip: string
  userAgent: string
  location: string
  time: string
  result: 'success' | 'failed'
  remark: string
}

export interface PreferenceSettings {
  // 界面设置
  interface: {
    theme: 'light' | 'dark' | 'auto'
    language: string
    timezone: string
    dateFormat: string
    pageSize: number
  }
  // 功能设置
  features: {
    autoSave: boolean
    confirmBeforeDelete: boolean
    showHelpTips: boolean
    enableShortcuts: boolean
  }
  // 数据显示
  dataDisplay: {
    defaultCurrency: string
    numberFormat: string
    showDecimalPlaces: number
    chartType: string
  }
}

// 个人资料管理 API
export const profileApi = {
  // 获取个人资料
  getProfile: (): Promise<BaseResponse<ProfileInfo>> => {
    return request({
      url: '/admin/profile/info',
      method: 'get'
    })
  },

  // 更新个人资料
  updateProfile: (data: ProfileUpdateParams): Promise<BaseResponse<ProfileInfo>> => {
    return request({
      url: '/admin/profile/update',
      method: 'post',
      data
    })
  },

  // 修改密码
  changePassword: (data: PasswordChangeParams): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/profile/password',
      method: 'post',
      data
    })
  },

  // 上传头像
  uploadAvatar: (file: File): Promise<BaseResponse<{ url: string }>> => {
    const formData = new FormData()
    formData.append('avatar', file)
    
    return request({
      url: '/admin/profile/avatar',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取安全设置
  getSecuritySettings: (): Promise<BaseResponse<SecuritySettings>> => {
    return request({
      url: '/admin/profile/security',
      method: 'get'
    })
  },

  // 更新安全设置
  updateSecuritySettings: (data: {
    enableTwoFactor?: boolean
    enableEmailLogin?: boolean
    enableMobileLogin?: boolean
    sessionTimeout?: number
    maxLoginAttempts?: number
  }): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/profile/security',
      method: 'post',
      data
    })
  },

  // 启用/禁用双因子认证
  toggleTwoFactor: (enable: boolean, code?: string): Promise<BaseResponse<{
    enabled: boolean
    qrCode?: string
    backupCodes?: string[]
  }>> => {
    return request({
      url: '/admin/profile/two-factor',
      method: 'post',
      data: { enable, code }
    })
  },

  // 生成备用代码
  generateBackupCodes: (): Promise<BaseResponse<{ codes: string[] }>> => {
    return request({
      url: '/admin/profile/backup-codes',
      method: 'post'
    })
  },

  // 踢出登录设备
  kickoutDevice: (deviceId: string): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/profile/kickout-device',
      method: 'post',
      data: { deviceId }
    })
  },

  // 清理所有其他设备
  clearOtherDevices: (): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/profile/clear-devices',
      method: 'post'
    })
  },

  // 获取通知设置
  getNotificationSettings: (): Promise<BaseResponse<NotificationSettings>> => {
    return request({
      url: '/admin/profile/notifications',
      method: 'get'
    })
  },

  // 更新通知设置
  updateNotificationSettings: (data: Partial<NotificationSettings>): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/profile/notifications',
      method: 'post',
      data
    })
  },

  // 获取操作日志
  getOperationLogs: (params?: {
    page?: number
    limit?: number
    module?: string
    action?: string
    startTime?: string
    endTime?: string
  }): Promise<BaseResponse<{
    total: number
    list: OperationLog[]
  }>> => {
    return request({
      url: '/admin/profile/operation-logs',
      method: 'get',
      params
    })
  },

  // 导出操作日志
  exportOperationLogs: (params?: {
    module?: string
    action?: string
    startTime?: string
    endTime?: string
    format?: 'excel' | 'csv'
  }): Promise<Blob> => {
    return request({
      url: '/admin/profile/operation-logs/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  },

  // 获取偏好设置
  getPreferences: (): Promise<BaseResponse<PreferenceSettings>> => {
    return request({
      url: '/admin/profile/preferences',
      method: 'get'
    })
  },

  // 更新偏好设置
  updatePreferences: (data: Partial<PreferenceSettings>): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/profile/preferences',
      method: 'post',
      data
    })
  },

  // 重置偏好设置
  resetPreferences: (): Promise<BaseResponse<PreferenceSettings>> => {
    return request({
      url: '/admin/profile/preferences/reset',
      method: 'post'
    })
  },

  // 验证当前密码
  verifyPassword: (password: string): Promise<BaseResponse<{ valid: boolean }>> => {
    return request({
      url: '/admin/profile/verify-password',
      method: 'post',
      data: { password }
    })
  },

  // 绑定邮箱
  bindEmail: (data: {
    email: string
    code: string
  }): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/profile/bind-email',
      method: 'post',
      data
    })
  },

  // 绑定手机
  bindMobile: (data: {
    mobile: string
    code: string
  }): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/profile/bind-mobile',
      method: 'post',
      data
    })
  },

  // 发送验证码
  sendVerificationCode: (data: {
    type: 'email' | 'mobile'
    target: string
    purpose: 'bind' | 'unbind' | 'login' | 'password'
  }): Promise<BaseResponse<{ sent: boolean; waitTime: number }>> => {
    return request({
      url: '/admin/profile/send-code',
      method: 'post',
      data
    })
  },

  // 获取账户统计
  getAccountStats: (): Promise<BaseResponse<{
    loginCount: number
    lastLoginTime: string
    onlineTime: number
    operationCount: number
    dataSize: number
    achievements: {
      name: string
      description: string
      icon: string
      unlockTime: string
    }[]
  }>> => {
    return request({
      url: '/admin/profile/stats',
      method: 'get'
    })
  }
}

export default profileApi
