import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 团购相关类型定义
export interface GrouponRulesInfo {
  id?: number
  goodsId: number
  goodsName: string
  picUrl: string
  discount: number
  discountMember: number
  expireTime?: string
  status: number
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface GrouponInfo {
  id?: number
  orderId: number
  grouponId?: number
  rulesId: number
  userId: number
  shareUrl?: string
  creatorUserId: number
  creatorUserTime?: string
  status: number
  addTime?: string
  updateTime?: string
  deleted?: boolean
  // 关联数据
  rules?: GrouponRulesInfo
  user?: {
    id: number
    username: string
    nickname: string
    avatar: string
  }
  creator?: {
    id: number
    username: string
    nickname: string
    avatar: string
  }
  joiners?: GrouponInfo[]
}

export interface GrouponRulesCreateParams {
  goodsId: number
  discount: number
  discountMember: number
  expireTime?: string
}

export interface GrouponRulesUpdateParams extends GrouponRulesCreateParams {
  id: number
}

export interface GrouponRulesListParams extends PaginationParams {
  goodsId?: number
  goodsName?: string
  status?: number
}

export interface GrouponListParams extends PaginationParams {
  rulesId?: number
  userId?: number
  creatorUserId?: number
  status?: number
}

export interface GrouponRulesListResponse extends PaginationResponse {
  list: GrouponRulesInfo[]
}

export interface GrouponListResponse extends PaginationResponse {
  list: GrouponInfo[]
}

// 团购规则状态枚举
export enum GrouponRulesStatus {
  NORMAL = 0, // 正常
  EXPIRED = 1, // 已过期
  DISABLED = 2 // 已禁用
}

// 团购状态枚举
export enum GrouponStatus {
  NONE = 0, // 未开团
  ON = 1, // 开团中
  SUCCEED = 2, // 团购成功
  FAIL = 3 // 团购失败
}

// 团购管理 API
export const grouponApi = {
  // 团购规则管理
  rules: {
    // 获取团购规则列表
    list: (params: GrouponRulesListParams): Promise<BaseResponse<GrouponRulesListResponse>> => {
      return request({
        url: '/admin/groupon/rules/list',
        method: 'get',
        params
      })
    },

    // 获取团购规则详情
    detail: (id: number): Promise<BaseResponse<GrouponRulesInfo>> => {
      return request({
        url: `/admin/groupon/rules/detail/${id}`,
        method: 'get'
      })
    },

    // 创建团购规则
    create: (data: GrouponRulesCreateParams): Promise<BaseResponse<GrouponRulesInfo>> => {
      return request({
        url: '/admin/groupon/rules/create',
        method: 'post',
        data
      })
    },

    // 更新团购规则
    update: (data: GrouponRulesUpdateParams): Promise<BaseResponse<GrouponRulesInfo>> => {
      return request({
        url: '/admin/groupon/rules/update',
        method: 'post',
        data
      })
    },

    // 删除团购规则
    delete: (id: number): Promise<BaseResponse<null>> => {
      return request({
        url: '/admin/groupon/rules/delete',
        method: 'post',
        data: { id }
      })
    },

    // 启用/禁用团购规则
    updateStatus: (id: number, status: number): Promise<BaseResponse<null>> => {
      return request({
        url: '/admin/groupon/rules/status',
        method: 'post',
        data: { id, status }
      })
    }
  },

  // 团购活动管理
  groupon: {
    // 获取团购活动列表
    list: (params: GrouponListParams): Promise<BaseResponse<GrouponListResponse>> => {
      return request({
        url: '/admin/groupon/list',
        method: 'get',
        params
      })
    },

    // 获取团购活动详情
    detail: (id: number): Promise<BaseResponse<GrouponInfo>> => {
      return request({
        url: `/admin/groupon/detail/${id}`,
        method: 'get'
      })
    },

    // 结束团购
    close: (id: number): Promise<BaseResponse<null>> => {
      return request({
        url: '/admin/groupon/close',
        method: 'post',
        data: { id }
      })
    },

    // 获取团购统计
    statistics: (): Promise<BaseResponse<{
      totalGroupons: number
      activeGroupons: number
      successGroupons: number
      failGroupons: number
      successRate: number
    }>> => {
      return request({
        url: '/admin/groupon/statistics',
        method: 'get'
      })
    }
  }
}

export default grouponApi
