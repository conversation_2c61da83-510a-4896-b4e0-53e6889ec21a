import { AdminInfo, AdminQuery } from '@/api/types/business';
import { ApiResult, PageData } from '@/api/types/index';
import request from '@/utils/request';

// 获取管理员列表
export function list(query: AdminQuery): ApiResult<PageData<AdminInfo>> {
  return request({
    url: '/admin/admin/list',
    method: 'get',
    params: query
  });
}

// 创建管理员
export function create(data: Partial<AdminInfo>): ApiResult<AdminInfo> {
  return request({
    url: '/admin/admin/create',
    method: 'post',
    data
  });
}

// 获取管理员详情
export function read(id: number): ApiResult<AdminInfo> {
  return request({
    url: '/admin/admin/read',
    method: 'get',
    params: { id }
  });
}

// 更新管理员信息
export function update(data: Partial<AdminInfo>): ApiResult<null> {
  return request({
    url: '/admin/admin/update',
    method: 'post',
    data
  });
}

// 删除管理员
export function remove(data: { id: number }): ApiResult<null> {
  return request({
    url: '/admin/admin/delete',
    method: 'post',
    data
  });
}

// 更新管理员角色
export function updateRole(data: {
  adminId: number;
  roleIds: number[];
}): ApiResult<null> {
  return request({
    url: '/admin/admin/updateRole',
    method: 'post',
    data
  });
}

// 启用/禁用管理员
export function updateStatus(data: {
  id: number;
  enabled: boolean;
}): ApiResult<null> {
  return request({
    url: '/admin/admin/updateStatus',
    method: 'post',
    data
  });
}

// 重置管理员密码
export function resetPassword(data: {
  id: number;
  newPassword: string;
}): ApiResult<null> {
  return request({
    url: '/admin/admin/resetPassword',
    method: 'post',
    data
  });
}
