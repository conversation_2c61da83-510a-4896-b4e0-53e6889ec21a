import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 品牌相关类型定义
export interface BrandInfo {
  id?: number
  name: string
  desc: string
  picUrl: string
  sortOrder: number
  floorPrice: number
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface BrandCreateParams {
  name: string
  desc: string
  picUrl: string
  sortOrder: number
  floorPrice: number
}

export interface BrandUpdateParams extends BrandCreateParams {
  id: number
}

export interface BrandListParams extends PaginationParams {
  name?: string
  sortOrder?: number
}

export interface BrandListResponse extends PaginationResponse {
  list: BrandInfo[]
}

// 品牌管理 API
export const brandApi = {
  // 获取品牌列表
  list: (params: BrandListParams): Promise<BaseResponse<BrandListResponse>> => {
    return request({
      url: '/admin/brand/list',
      method: 'get',
      params
    })
  },

  // 获取所有品牌（不分页）
  all: (): Promise<BaseResponse<BrandInfo[]>> => {
    return request({
      url: '/admin/brand/all',
      method: 'get'
    })
  },

  // 获取品牌详情
  detail: (id: number): Promise<BaseResponse<BrandInfo>> => {
    return request({
      url: `/admin/brand/detail/${id}`,
      method: 'get'
    })
  },

  // 创建品牌
  create: (data: BrandCreateParams): Promise<BaseResponse<BrandInfo>> => {
    return request({
      url: '/admin/brand/create',
      method: 'post',
      data
    })
  },

  // 更新品牌
  update: (data: BrandUpdateParams): Promise<BaseResponse<BrandInfo>> => {
    return request({
      url: '/admin/brand/update',
      method: 'post',
      data
    })
  },

  // 删除品牌
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/brand/delete',
      method: 'post',
      data: { id }
    })
  },

  // 批量删除品牌
  batchDelete: (ids: number[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/brand/batch/delete',
      method: 'post',
      data: { ids }
    })
  }
}

export default brandApi
