import { UserInfo, UserListResponse, UserQuery } from '@/api/types/business';
import { ApiResult } from '@/api/types/index';
import request from '@/utils/request';

// 获取用户列表
export function fetchList(query: UserQuery): ApiResult<UserListResponse> {
  return request({
    url: '/user/list',
    method: 'get',
    params: query
  });
}

// 审核代理申请
export function approveAgency(data: {
  id: number;
  status: number;
  reason?: string;
}): ApiResult<null> {
  return request({
    url: '/user/approveAgency',
    method: 'post',
    data
  });
}

// 获取代理申请详情
export function detailApprove(id: number): ApiResult<UserInfo> {
  return request({
    url: '/user/detailApprove',
    method: 'get',
    params: { id }
  });
}

// 获取用户地址列表
export function listAddress(query: {
  userId?: number;
  page?: number;
  limit?: number;
}): ApiResult<{
  total: number;
  pages: number;
  limit: number;
  page: number;
  list: Array<{
    id: number;
    name: string;
    userId: number;
    province: string;
    city: string;
    county: string;
    addressDetail: string;
    areaCode: string;
    postalCode: string;
    tel: string;
    isDefault: boolean;
    addTime: string;
    updateTime: string;
    deleted: boolean;
  }>;
}> {
  return request({
    url: '/address/list',
    method: 'get',
    params: query
  });
}

// 获取用户收藏列表
export function listCollect(query: {
  userId?: number;
  valueId?: number;
  type?: number;
  page?: number;
  limit?: number;
}): ApiResult<{
  total: number;
  pages: number;
  limit: number;
  page: number;
  list: Array<{
    id: number;
    userId: number;
    valueId: number;
    type: number;
    addTime: string;
    updateTime: string;
    deleted: boolean;
  }>;
}> {
  return request({
    url: '/collect/list',
    method: 'get',
    params: query
  });
}

// 获取用户反馈列表
export function listFeedback(query: {
  userId?: number;
  username?: string;
  page?: number;
  limit?: number;
}): ApiResult<{
  total: number;
  pages: number;
  limit: number;
  page: number;
  list: Array<{
    id: number;
    userId: number;
    username: string;
    mobile: string;
    feedType: string;
    content: string;
    status: number;
    hasPicture: boolean;
    picUrls: string[];
    addTime: string;
    updateTime: string;
    deleted: boolean;
  }>;
}> {
  return request({
    url: '/feedback/list',
    method: 'get',
    params: query
  });
}

// 获取用户足迹列表
export function listFootprint(query: {
  userId?: number;
  goodsId?: number;
  page?: number;
  limit?: number;
}): ApiResult<{
  total: number;
  pages: number;
  limit: number;
  page: number;
  list: Array<{
    id: number;
    userId: number;
    goodsId: number;
    addTime: string;
    updateTime: string;
    deleted: boolean;
  }>;
}> {
  return request({
    url: '/footprint/list',
    method: 'get',
    params: query
  });
}

// 获取用户搜索历史列表
export function listHistory(query: {
  userId?: number;
  keyword?: string;
  page?: number;
  limit?: number;
}): ApiResult<{
  total: number;
  pages: number;
  limit: number;
  page: number;
  list: Array<{
    id: number;
    userId: number;
    keyword: string;
    from: string;
    addTime: string;
    updateTime: string;
    deleted: boolean;
  }>;
}> {
  return request({
    url: '/history/list',
    method: 'get',
    params: query
  });
}
