import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 优惠券相关类型定义
export interface CouponInfo {
  id?: number
  name: string
  desc?: string
  tag?: string
  total: number
  discount: number
  min: number
  limit: number
  type: number
  status: number
  goodsType: number
  goodsValue: number[]
  timeType: number
  days?: number
  startTime?: string
  endTime?: string
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface CouponUser {
  id?: number
  userId: number
  couponId: number
  status: number
  usedTime?: string
  startTime: string
  endTime: string
  orderId?: number
  addTime?: string
  updateTime?: string
  deleted?: boolean
  // 关联数据
  user?: {
    id: number
    username: string
    mobile: string
  }
  coupon?: CouponInfo
}

export interface CouponCreateParams {
  name: string
  desc?: string
  tag?: string
  total: number
  discount: number
  min: number
  limit: number
  type: number
  goodsType: number
  goodsValue: number[]
  timeType: number
  days?: number
  startTime?: string
  endTime?: string
}

export interface CouponUpdateParams extends CouponCreateParams {
  id: number
}

export interface CouponListParams extends PaginationParams {
  name?: string
  type?: number
  status?: number
}

export interface CouponListResponse extends PaginationResponse {
  list: CouponInfo[]
}

export interface CouponUserListParams extends PaginationParams {
  userId?: number
  couponId?: number
  status?: number
}

export interface CouponUserListResponse extends PaginationResponse {
  list: CouponUser[]
}

// 优惠券类型枚举
export enum CouponType {
  GENERAL = 0, // 通用券
  REGISTER = 1, // 注册券
  CODE = 2 // 兑换码券
}

// 优惠券状态枚举
export enum CouponStatus {
  NORMAL = 0, // 正常
  EXPIRED = 1, // 已过期
  OUT_OF_STOCK = 2 // 已领完
}

// 用户优惠券状态枚举
export enum CouponUserStatus {
  AVAILABLE = 0, // 可用
  USED = 1, // 已使用
  EXPIRED = 2 // 已过期
}

// 商品类型枚举
export enum GoodsType {
  ALL = 0, // 全场通用
  CATEGORY = 1, // 指定分类
  GOODS = 2 // 指定商品
}

// 优惠券管理 API
export const couponApi = {
  // 获取优惠券列表
  list: (params: CouponListParams): Promise<BaseResponse<CouponListResponse>> => {
    return request({
      url: '/admin/coupon/list',
      method: 'get',
      params
    })
  },

  // 获取优惠券详情
  detail: (id: number): Promise<BaseResponse<CouponInfo>> => {
    return request({
      url: `/admin/coupon/detail/${id}`,
      method: 'get'
    })
  },

  // 创建优惠券
  create: (data: CouponCreateParams): Promise<BaseResponse<CouponInfo>> => {
    return request({
      url: '/admin/coupon/create',
      method: 'post',
      data
    })
  },

  // 更新优惠券
  update: (data: CouponUpdateParams): Promise<BaseResponse<CouponInfo>> => {
    return request({
      url: '/admin/coupon/update',
      method: 'post',
      data
    })
  },

  // 删除优惠券
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/coupon/delete',
      method: 'post',
      data: { id }
    })
  },

  // 获取用户优惠券列表
  userList: (params: CouponUserListParams): Promise<BaseResponse<CouponUserListResponse>> => {
    return request({
      url: '/admin/coupon/user/list',
      method: 'get',
      params
    })
  },

  // 给用户发放优惠券
  giveToUser: (couponId: number, userIds: number[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/coupon/give',
      method: 'post',
      data: { couponId, userIds }
    })
  },

  // 批量给用户发放优惠券
  batchGive: (couponId: number, userType: 'all' | 'level', levelId?: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/coupon/batch/give',
      method: 'post',
      data: { couponId, userType, levelId }
    })
  },

  // 获取优惠券统计
  statistics: (id: number): Promise<BaseResponse<{
    total: number
    used: number
    available: number
    expired: number
  }>> => {
    return request({
      url: `/admin/coupon/statistics/${id}`,
      method: 'get'
    })
  }
}

export default couponApi
