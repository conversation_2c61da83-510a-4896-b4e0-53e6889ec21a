import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 问题反馈相关类型定义
export interface IssueInfo {
  id?: number
  question: string
  answer?: string
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface FeedbackInfo {
  id?: number
  userId: number
  username: string
  mobile: string
  feedType: string
  content: string
  status: number
  hasPicture: boolean
  picUrls: string[]
  addTime?: string
  updateTime?: string
  deleted?: boolean
  // 关联数据
  user?: {
    id: number
    username: string
    nickname: string
    mobile: string
    avatar: string
  }
}

export interface IssueCreateParams {
  question: string
  answer?: string
}

export interface IssueUpdateParams extends IssueCreateParams {
  id: number
}

export interface IssueListParams extends PaginationParams {
  question?: string
  answer?: string
}

export interface FeedbackListParams extends PaginationParams {
  userId?: number
  username?: string
  mobile?: string
  feedType?: string
  status?: number
  content?: string
}

export interface IssueListResponse extends PaginationResponse {
  list: IssueInfo[]
}

export interface FeedbackListResponse extends PaginationResponse {
  list: FeedbackInfo[]
}

export interface FeedbackReplyParams {
  id: number
  replyContent: string
}

// 反馈状态枚举
export enum FeedbackStatus {
  PENDING = 0, // 待处理
  REPLIED = 1, // 已回复
  CLOSED = 2 // 已关闭
}

// 问题反馈管理 API
export const issueApi = {
  // 常见问题管理
  faq: {
    // 获取常见问题列表
    list: (params: IssueListParams): Promise<BaseResponse<IssueListResponse>> => {
      return request({
        url: '/admin/issue/list',
        method: 'get',
        params
      })
    },

    // 获取所有常见问题（不分页）
    all: (): Promise<BaseResponse<IssueInfo[]>> => {
      return request({
        url: '/admin/issue/all',
        method: 'get'
      })
    },

    // 获取常见问题详情
    detail: (id: number): Promise<BaseResponse<IssueInfo>> => {
      return request({
        url: `/admin/issue/detail/${id}`,
        method: 'get'
      })
    },

    // 创建常见问题
    create: (data: IssueCreateParams): Promise<BaseResponse<IssueInfo>> => {
      return request({
        url: '/admin/issue/create',
        method: 'post',
        data
      })
    },

    // 更新常见问题
    update: (data: IssueUpdateParams): Promise<BaseResponse<IssueInfo>> => {
      return request({
        url: '/admin/issue/update',
        method: 'post',
        data
      })
    },

    // 删除常见问题
    delete: (id: number): Promise<BaseResponse<null>> => {
      return request({
        url: '/admin/issue/delete',
        method: 'post',
        data: { id }
      })
    },

    // 批量删除常见问题
    batchDelete: (ids: number[]): Promise<BaseResponse<null>> => {
      return request({
        url: '/admin/issue/batch/delete',
        method: 'post',
        data: { ids }
      })
    }
  },

  // 用户反馈管理
  feedback: {
    // 获取用户反馈列表
    list: (params: FeedbackListParams): Promise<BaseResponse<FeedbackListResponse>> => {
      return request({
        url: '/admin/feedback/list',
        method: 'get',
        params
      })
    },

    // 获取用户反馈详情
    detail: (id: number): Promise<BaseResponse<FeedbackInfo>> => {
      return request({
        url: `/admin/feedback/detail/${id}`,
        method: 'get'
      })
    },

    // 回复用户反馈
    reply: (data: FeedbackReplyParams): Promise<BaseResponse<null>> => {
      return request({
        url: '/admin/feedback/reply',
        method: 'post',
        data
      })
    },

    // 关闭用户反馈
    close: (id: number): Promise<BaseResponse<null>> => {
      return request({
        url: '/admin/feedback/close',
        method: 'post',
        data: { id }
      })
    },

    // 删除用户反馈
    delete: (id: number): Promise<BaseResponse<null>> => {
      return request({
        url: '/admin/feedback/delete',
        method: 'post',
        data: { id }
      })
    },

    // 获取反馈统计
    statistics: (): Promise<BaseResponse<{
      totalFeedbacks: number
      pendingFeedbacks: number
      repliedFeedbacks: number
      closedFeedbacks: number
      todayFeedbacks: number
    }>> => {
      return request({
        url: '/admin/feedback/statistics',
        method: 'get'
      })
    }
  }
}

export default issueApi
