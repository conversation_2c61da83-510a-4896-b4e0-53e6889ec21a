import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 推广相关类型定义
export interface BrokerageInfo {
  id?: number
  userId: number
  type: number
  amount: number
  settlementAmount: number
  frozenAmount: number
  status: number
  sourceType: number
  sourceId: number
  orderId?: number
  settlementTime?: string
  remark?: string
  addTime?: string
  updateTime?: string
  deleted?: boolean
  // 关联数据
  user?: {
    id: number
    username: string
    nickname: string
    mobile: string
    avatar: string
  }
  order?: {
    id: number
    orderSn: string
    actualPrice: number
    payTime: string
  }
}

export interface BrokerageCreateParams {
  userId: number
  type: number
  amount: number
  sourceType: number
  sourceId: number
  orderId?: number
  remark?: string
}

export interface BrokerageUpdateParams {
  id: number
  status?: number
  remark?: string
}

export interface BrokerageListParams extends PaginationParams {
  userId?: number
  type?: number
  status?: number
  sourceType?: number
  startTime?: string
  endTime?: string
  username?: string
  mobile?: string
}

export interface BrokerageListResponse extends PaginationResponse {
  list: BrokerageInfo[]
}

export interface BrokerageAccountInfo {
  userId: number
  totalIncome: number
  totalSettled: number
  totalFrozen: number
  availableAmount: number
  pendingAmount: number
  todayIncome: number
  monthIncome: number
  // 关联数据
  user?: {
    id: number
    username: string
    nickname: string
    mobile: string
    avatar: string
  }
}

export interface BrokerageSettlementParams {
  userIds: number[]
  remark?: string
  settlementType?: number
}

export interface BrokerageWithdrawParams {
  userId: number
  amount: number
  type: number
  account: string
  remark?: string
}

// 推广类型枚举
export enum BrokerageType {
  COMMISSION = 1, // 佣金收入
  BONUS = 2, // 奖励收入
  REBATE = 3, // 返利收入
  REFERRAL = 4 // 推荐奖励
}

// 推广状态枚举
export enum BrokerageStatus {
  PENDING = 0, // 待结算
  SETTLED = 1, // 已结算
  FROZEN = 2, // 已冻结
  CANCELLED = 3 // 已取消
}

// 来源类型枚举
export enum BrokerageSourceType {
  ORDER = 1, // 订单
  ACTIVITY = 2, // 活动
  MANUAL = 3, // 手动调整
  SYSTEM = 4 // 系统奖励
}

// 推广管理 API
export const brokerageApi = {
  // 获取推广记录列表
  list: (params: BrokerageListParams): Promise<BaseResponse<BrokerageListResponse>> => {
    return request({
      url: '/admin/brokerage/list',
      method: 'get',
      params
    })
  },

  // 获取推广记录详情
  detail: (id: number): Promise<BaseResponse<BrokerageInfo>> => {
    return request({
      url: `/admin/brokerage/detail/${id}`,
      method: 'get'
    })
  },

  // 创建推广记录
  create: (data: BrokerageCreateParams): Promise<BaseResponse<BrokerageInfo>> => {
    return request({
      url: '/admin/brokerage/create',
      method: 'post',
      data
    })
  },

  // 更新推广记录
  update: (data: BrokerageUpdateParams): Promise<BaseResponse<BrokerageInfo>> => {
    return request({
      url: '/admin/brokerage/update',
      method: 'post',
      data
    })
  },

  // 删除推广记录
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/brokerage/delete',
      method: 'post',
      data: { id }
    })
  },

  // 获取用户推广账户信息
  getAccount: (userId: number): Promise<BaseResponse<BrokerageAccountInfo>> => {
    return request({
      url: `/admin/brokerage/account/${userId}`,
      method: 'get'
    })
  },

  // 获取所有用户推广账户列表
  getAccountList: (params?: {
    page?: number
    limit?: number
    username?: string
    mobile?: string
    minAmount?: number
  }): Promise<BaseResponse<PaginationResponse & {
    list: BrokerageAccountInfo[]
  }>> => {
    return request({
      url: '/admin/brokerage/account/list',
      method: 'get',
      params
    })
  },

  // 结算推广佣金
  settlement: (data: BrokerageSettlementParams): Promise<BaseResponse<{
    success: number
    failed: number
    totalAmount: number
    results: {
      userId: number
      success: boolean
      amount?: number
      error?: string
    }[]
  }>> => {
    return request({
      url: '/admin/brokerage/settlement',
      method: 'post',
      data
    })
  },

  // 冻结推广佣金
  freeze: (ids: number[], remark?: string): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/brokerage/freeze',
      method: 'post',
      data: { ids, remark }
    })
  },

  // 解冻推广佣金
  unfreeze: (ids: number[], remark?: string): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/brokerage/unfreeze',
      method: 'post',
      data: { ids, remark }
    })
  },

  // 取消推广佣金
  cancel: (ids: number[], remark?: string): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/brokerage/cancel',
      method: 'post',
      data: { ids, remark }
    })
  },

  // 手动调整推广佣金
  adjust: (data: {
    userId: number
    type: number
    amount: number
    remark: string
  }): Promise<BaseResponse<BrokerageInfo>> => {
    return request({
      url: '/admin/brokerage/adjust',
      method: 'post',
      data
    })
  },

  // 处理提现申请
  processWithdraw: (data: BrokerageWithdrawParams): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/brokerage/withdraw',
      method: 'post',
      data
    })
  },

  // 获取推广统计
  getStatistics: (params?: {
    startTime?: string
    endTime?: string
    userId?: number
  }): Promise<BaseResponse<{
    totalBrokerage: number
    totalSettled: number
    totalFrozen: number
    totalPending: number
    totalUsers: number
    activeUsers: number
    typeDistribution: { [key: number]: { count: number; amount: number } }
    dailyTrend: { date: string; count: number; amount: number }[]
    topUsers: {
      userId: number
      username: string
      nickname: string
      totalAmount: number
      settledAmount: number
    }[]
  }>> => {
    return request({
      url: '/admin/brokerage/statistics',
      method: 'get',
      params
    })
  },

  // 根据用户获取推广记录
  getByUser: (userId: number, params?: {
    type?: number
    status?: number
    startTime?: string
    endTime?: string
    page?: number
    limit?: number
  }): Promise<BaseResponse<BrokerageListResponse>> => {
    return request({
      url: `/admin/brokerage/user/${userId}`,
      method: 'get',
      params
    })
  },

  // 根据订单获取推广记录
  getByOrder: (orderId: number): Promise<BaseResponse<BrokerageInfo[]>> => {
    return request({
      url: `/admin/brokerage/order/${orderId}`,
      method: 'get'
    })
  },

  // 导出推广数据
  export: (params: BrokerageListParams & {
    format?: 'excel' | 'csv'
  }): Promise<Blob> => {
    return request({
      url: '/admin/brokerage/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  },

  // 批量操作推广记录
  batchOperation: (data: {
    ids: number[]
    operation: 'settle' | 'freeze' | 'unfreeze' | 'cancel'
    remark?: string
  }): Promise<BaseResponse<{
    success: number
    failed: number
    results: {
      id: number
      success: boolean
      error?: string
    }[]
  }>> => {
    return request({
      url: '/admin/brokerage/batch/operation',
      method: 'post',
      data
    })
  },

  // 获取推广配置
  getConfig: (): Promise<BaseResponse<{
    enabled: boolean
    settlementMode: number
    minSettlementAmount: number
    maxDailyAmount: number
    freezeDays: number
    withdrawEnabled: boolean
    withdrawFeeRate: number
    withdrawMinAmount: number
  }>> => {
    return request({
      url: '/admin/brokerage/config',
      method: 'get'
    })
  },

  // 更新推广配置
  updateConfig: (config: {
    enabled?: boolean
    settlementMode?: number
    minSettlementAmount?: number
    maxDailyAmount?: number
    freezeDays?: number
    withdrawEnabled?: boolean
    withdrawFeeRate?: number
    withdrawMinAmount?: number
  }): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/brokerage/config',
      method: 'post',
      data: config
    })
  }
}

export default brokerageApi
