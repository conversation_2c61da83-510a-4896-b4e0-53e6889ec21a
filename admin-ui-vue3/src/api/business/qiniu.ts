import type { BaseResponse } from '@/api/types'
import request from '@/utils/request'

// 七牛云相关类型定义
export interface QiniuConfig {
  accessKey: string
  secretKey: string
  bucket: string
  domain: string
  region: string
  isSSL: boolean
}

export interface QiniuToken {
  token: string
  domain: string
  expires: number
}

export interface QiniuUploadResponse {
  url: string
  key: string
  hash: string
  size: number
  mimeType: string
}

export interface QiniuFileInfo {
  key: string
  size: number
  hash: string
  mimeType: string
  putTime: number
  type: number
  status: number
}

export interface QiniuBucketInfo {
  name: string
  region: string
  private: boolean
  source: string
  host: string
  createTime: number
  fileCount: number
  size: number
}

export interface QiniuStats {
  space: number
  count: number
  hit: number
  bandwidth: number
}

// 七牛云管理 API
export const qiniuApi = {
  // 获取上传token
  getUploadToken: (params?: {
    key?: string
    expires?: number
    policy?: Record<string, any>
  }): Promise<BaseResponse<QiniuToken>> => {
    return request({
      url: '/admin/qiniu/token',
      method: 'get',
      params
    })
  },

  // 直接上传文件到七牛云
  upload: (file: File, token: string, key?: string): Promise<QiniuUploadResponse> => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('token', token)
    if (key) {
      formData.append('key', key)
    }

    // 直接请求七牛云上传接口
    return fetch('https://upload.qiniup.com', {
      method: 'POST',
      body: formData
    }).then(response => response.json())
  },

  // 获取文件信息
  getFileInfo: (key: string): Promise<BaseResponse<QiniuFileInfo>> => {
    return request({
      url: '/admin/qiniu/file/info',
      method: 'get',
      params: { key }
    })
  },

  // 删除文件
  deleteFile: (key: string): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/qiniu/file/delete',
      method: 'post',
      data: { key }
    })
  },

  // 批量删除文件
  batchDeleteFiles: (keys: string[]): Promise<BaseResponse<{
    success: string[]
    failed: string[]
  }>> => {
    return request({
      url: '/admin/qiniu/file/batch/delete',
      method: 'post',
      data: { keys }
    })
  },

  // 移动/重命名文件
  moveFile: (fromKey: string, toKey: string): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/qiniu/file/move',
      method: 'post',
      data: { fromKey, toKey }
    })
  },

  // 复制文件
  copyFile: (fromKey: string, toKey: string): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/qiniu/file/copy',
      method: 'post',
      data: { fromKey, toKey }
    })
  },

  // 获取文件列表
  listFiles: (params?: {
    prefix?: string
    marker?: string
    limit?: number
  }): Promise<BaseResponse<{
    items: QiniuFileInfo[]
    marker?: string
    commonPrefixes?: string[]
  }>> => {
    return request({
      url: '/admin/qiniu/file/list',
      method: 'get',
      params
    })
  },

  // 获取空间（bucket）信息
  getBucketInfo: (): Promise<BaseResponse<QiniuBucketInfo>> => {
    return request({
      url: '/admin/qiniu/bucket/info',
      method: 'get'
    })
  },

  // 获取空间域名
  getBucketDomains: (): Promise<BaseResponse<string[]>> => {
    return request({
      url: '/admin/qiniu/bucket/domains',
      method: 'get'
    })
  },

  // 获取存储统计
  getStats: (params?: {
    begin?: string
    end?: string
    granularity?: 'day' | 'month'
  }): Promise<BaseResponse<QiniuStats[]>> => {
    return request({
      url: '/admin/qiniu/stats',
      method: 'get',
      params
    })
  },

  // 刷新CDN缓存
  refreshCdn: (urls: string[]): Promise<BaseResponse<{
    code: number
    error: string
    requestId: string
    taskId: string
  }>> => {
    return request({
      url: '/admin/qiniu/cdn/refresh',
      method: 'post',
      data: { urls }
    })
  },

  // 预取CDN资源
  prefetchCdn: (urls: string[]): Promise<BaseResponse<{
    code: number
    error: string
    requestId: string
    taskId: string
  }>> => {
    return request({
      url: '/admin/qiniu/cdn/prefetch',
      method: 'post',
      data: { urls }
    })
  },

  // 获取七牛云配置
  getConfig: (): Promise<BaseResponse<QiniuConfig>> => {
    return request({
      url: '/admin/qiniu/config',
      method: 'get'
    })
  },

  // 更新七牛云配置
  updateConfig: (config: Partial<QiniuConfig>): Promise<BaseResponse<QiniuConfig>> => {
    return request({
      url: '/admin/qiniu/config',
      method: 'post',
      data: config
    })
  },

  // 测试七牛云配置
  testConfig: (config: QiniuConfig): Promise<BaseResponse<{
    success: boolean
    message: string
  }>> => {
    return request({
      url: '/admin/qiniu/config/test',
      method: 'post',
      data: config
    })
  },

  // 生成下载链接
  generateDownloadUrl: (key: string, expires?: number): Promise<BaseResponse<{
    url: string
    expires: number
  }>> => {
    return request({
      url: '/admin/qiniu/download/url',
      method: 'get',
      params: { key, expires }
    })
  }
}

export default qiniuApi
