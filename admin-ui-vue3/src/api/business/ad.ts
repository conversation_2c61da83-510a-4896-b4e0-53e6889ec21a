import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 广告相关类型定义
export interface AdInfo {
  id?: number
  name: string
  link: string
  url: string
  position: number
  content?: string
  startTime?: string
  endTime?: string
  enabled: boolean
  sortOrder: number
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface AdCreateParams {
  name: string
  link: string
  url: string
  position: number
  content?: string
  startTime?: string
  endTime?: string
  enabled: boolean
  sortOrder: number
}

export interface AdUpdateParams extends AdCreateParams {
  id: number
}

export interface AdListParams extends PaginationParams {
  name?: string
  position?: number
  enabled?: boolean
}

export interface AdListResponse extends PaginationResponse {
  list: AdInfo[]
}

// 广告位置枚举
export enum AdPosition {
  HOME_BANNER = 1, // 首页轮播图
  HOME_CHANNEL = 2, // 首页频道
  HOME_FLOOR = 3, // 首页楼层
  CATEGORY_BANNER = 4, // 分类页轮播图
  SEARCH_BANNER = 5, // 搜索页轮播图
  GOODS_DETAIL = 6, // 商品详情页
  CART = 7, // 购物车页
  USER_CENTER = 8 // 个人中心
}

// 广告管理 API
export const adApi = {
  // 获取广告列表
  list: (params: AdListParams): Promise<BaseResponse<AdListResponse>> => {
    return request({
      url: '/admin/ad/list',
      method: 'get',
      params
    })
  },

  // 获取所有广告（不分页）
  all: (): Promise<BaseResponse<AdInfo[]>> => {
    return request({
      url: '/admin/ad/all',
      method: 'get'
    })
  },

  // 获取广告详情
  detail: (id: number): Promise<BaseResponse<AdInfo>> => {
    return request({
      url: `/admin/ad/detail/${id}`,
      method: 'get'
    })
  },

  // 创建广告
  create: (data: AdCreateParams): Promise<BaseResponse<AdInfo>> => {
    return request({
      url: '/admin/ad/create',
      method: 'post',
      data
    })
  },

  // 更新广告
  update: (data: AdUpdateParams): Promise<BaseResponse<AdInfo>> => {
    return request({
      url: '/admin/ad/update',
      method: 'post',
      data
    })
  },

  // 删除广告
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/ad/delete',
      method: 'post',
      data: { id }
    })
  },

  // 批量删除广告
  batchDelete: (ids: number[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/ad/batch/delete',
      method: 'post',
      data: { ids }
    })
  },

  // 启用/禁用广告
  toggleEnabled: (id: number, enabled: boolean): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/ad/enabled',
      method: 'post',
      data: { id, enabled }
    })
  },

  // 批量更新排序
  updateSort: (ads: { id: number; sortOrder: number }[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/ad/sort',
      method: 'post',
      data: { ads }
    })
  },

  // 根据位置获取广告
  getByPosition: (position: number): Promise<BaseResponse<AdInfo[]>> => {
    return request({
      url: `/admin/ad/position/${position}`,
      method: 'get'
    })
  },

  // 获取广告统计
  statistics: (): Promise<BaseResponse<{
    totalAds: number
    enabledAds: number
    disabledAds: number
    expiredAds: number
    positionDistribution: { [key: number]: number }
  }>> => {
    return request({
      url: '/admin/ad/statistics',
      method: 'get'
    })
  }
}

export default adApi
