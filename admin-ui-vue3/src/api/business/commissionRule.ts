import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 佣金规则相关类型定义
export interface CommissionRuleInfo {
  id?: number
  name: string
  type: number
  level: number
  rate: number
  fixedAmount?: number
  minOrderAmount?: number
  maxCommission?: number
  categoryIds?: number[]
  goodsIds?: number[]
  userLevel?: number
  timeLimit?: {
    startTime?: string
    endTime?: string
  }
  enabled: boolean
  priority: number
  description?: string
  addTime?: string
  updateTime?: string
  deleted?: boolean
}

export interface CommissionRuleCreateParams {
  name: string
  type: number
  level: number
  rate: number
  fixedAmount?: number
  minOrderAmount?: number
  maxCommission?: number
  categoryIds?: number[]
  goodsIds?: number[]
  userLevel?: number
  timeLimit?: {
    startTime?: string
    endTime?: string
  }
  enabled: boolean
  priority: number
  description?: string
}

export interface CommissionRuleUpdateParams extends CommissionRuleCreateParams {
  id: number
}

export interface CommissionRuleListParams extends PaginationParams {
  name?: string
  type?: number
  level?: number
  enabled?: boolean
}

export interface CommissionRuleListResponse extends PaginationResponse {
  list: CommissionRuleInfo[]
}

export interface CommissionCalculateParams {
  userId: number
  orderId: number
  goodsId: number
  amount: number
  level: number
}

export interface CommissionCalculateResult {
  ruleId: number
  ruleName: string
  rate: number
  amount: number
  commission: number
  level: number
}

// 佣金类型枚举
export enum CommissionType {
  PERCENTAGE = 1, // 百分比佣金
  FIXED = 2, // 固定金额佣金
  TIERED = 3 // 阶梯佣金
}

// 佣金级别枚举
export enum CommissionLevel {
  LEVEL_1 = 1, // 一级佣金
  LEVEL_2 = 2, // 二级佣金
  LEVEL_3 = 3 // 三级佣金
}

// 佣金规则管理 API
export const commissionRuleApi = {
  // 获取佣金规则列表
  list: (params: CommissionRuleListParams): Promise<BaseResponse<CommissionRuleListResponse>> => {
    return request({
      url: '/admin/commission/rule/list',
      method: 'get',
      params
    })
  },

  // 获取所有佣金规则（不分页）
  all: (): Promise<BaseResponse<CommissionRuleInfo[]>> => {
    return request({
      url: '/admin/commission/rule/all',
      method: 'get'
    })
  },

  // 获取佣金规则详情
  detail: (id: number): Promise<BaseResponse<CommissionRuleInfo>> => {
    return request({
      url: `/admin/commission/rule/detail/${id}`,
      method: 'get'
    })
  },

  // 创建佣金规则
  create: (data: CommissionRuleCreateParams): Promise<BaseResponse<CommissionRuleInfo>> => {
    return request({
      url: '/admin/commission/rule/create',
      method: 'post',
      data
    })
  },

  // 更新佣金规则
  update: (data: CommissionRuleUpdateParams): Promise<BaseResponse<CommissionRuleInfo>> => {
    return request({
      url: '/admin/commission/rule/update',
      method: 'post',
      data
    })
  },

  // 删除佣金规则
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/commission/rule/delete',
      method: 'post',
      data: { id }
    })
  },

  // 批量删除佣金规则
  batchDelete: (ids: number[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/commission/rule/batch/delete',
      method: 'post',
      data: { ids }
    })
  },

  // 启用/禁用佣金规则
  toggleEnabled: (id: number, enabled: boolean): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/commission/rule/enabled',
      method: 'post',
      data: { id, enabled }
    })
  },

  // 更新佣金规则优先级
  updatePriority: (rules: { id: number; priority: number }[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/commission/rule/priority',
      method: 'post',
      data: { rules }
    })
  },

  // 计算佣金
  calculate: (params: CommissionCalculateParams): Promise<BaseResponse<CommissionCalculateResult[]>> => {
    return request({
      url: '/admin/commission/rule/calculate',
      method: 'post',
      data: params
    })
  },

  // 根据条件获取适用的佣金规则
  getApplicableRules: (params: {
    goodsId?: number
    categoryId?: number
    userLevel?: number
    orderAmount?: number
  }): Promise<BaseResponse<CommissionRuleInfo[]>> => {
    return request({
      url: '/admin/commission/rule/applicable',
      method: 'get',
      params
    })
  },

  // 复制佣金规则
  copy: (id: number, name: string): Promise<BaseResponse<CommissionRuleInfo>> => {
    return request({
      url: '/admin/commission/rule/copy',
      method: 'post',
      data: { id, name }
    })
  },

  // 获取佣金规则统计
  statistics: (): Promise<BaseResponse<{
    totalRules: number
    enabledRules: number
    disabledRules: number
    typeDistribution: { [key: number]: number }
    levelDistribution: { [key: number]: number }
  }>> => {
    return request({
      url: '/admin/commission/rule/statistics',
      method: 'get'
    })
  },

  // 验证佣金规则
  validate: (data: CommissionRuleCreateParams): Promise<BaseResponse<{
    valid: boolean
    errors: string[]
    warnings: string[]
  }>> => {
    return request({
      url: '/admin/commission/rule/validate',
      method: 'post',
      data
    })
  }
}

export default commissionRuleApi
