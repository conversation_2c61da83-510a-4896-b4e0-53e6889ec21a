import type { BaseResponse, PaginationParams, PaginationResponse } from '@/api/types'
import request from '@/utils/request'

// 分类相关类型定义
export interface CategoryInfo {
  id?: number
  name: string
  keywords: string
  desc?: string
  pid: number
  iconUrl?: string
  picUrl?: string
  level: string
  sortOrder: number
  addTime?: string
  updateTime?: string
  deleted?: boolean
  children?: CategoryInfo[]
}

export interface CategoryCreateParams {
  name: string
  keywords: string
  desc?: string
  pid: number
  iconUrl?: string
  picUrl?: string
  level: string
  sortOrder: number
}

export interface CategoryUpdateParams extends CategoryCreateParams {
  id: number
}

export interface CategoryListParams extends PaginationParams {
  name?: string
  pid?: number
  level?: string
}

export interface CategoryListResponse extends PaginationResponse {
  list: CategoryInfo[]
}

export interface CategoryTreeNode {
  id: number
  name: string
  level: string
  children?: CategoryTreeNode[]
}

// 分类管理 API
export const categoryApi = {
  // 获取分类列表
  list: (params: CategoryListParams): Promise<BaseResponse<CategoryListResponse>> => {
    return request({
      url: '/admin/category/list',
      method: 'get',
      params
    })
  },

  // 获取分类树
  tree: (): Promise<BaseResponse<CategoryTreeNode[]>> => {
    return request({
      url: '/admin/category/tree',
      method: 'get'
    })
  },

  // 获取分类详情
  detail: (id: number): Promise<BaseResponse<CategoryInfo>> => {
    return request({
      url: `/admin/category/detail/${id}`,
      method: 'get'
    })
  },

  // 创建分类
  create: (data: CategoryCreateParams): Promise<BaseResponse<CategoryInfo>> => {
    return request({
      url: '/admin/category/create',
      method: 'post',
      data
    })
  },

  // 更新分类
  update: (data: CategoryUpdateParams): Promise<BaseResponse<CategoryInfo>> => {
    return request({
      url: '/admin/category/update',
      method: 'post',
      data
    })
  },

  // 删除分类
  delete: (id: number): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/category/delete',
      method: 'post',
      data: { id }
    })
  },

  // 获取一级分类
  getL1Categories: (): Promise<BaseResponse<CategoryInfo[]>> => {
    return request({
      url: '/admin/category/l1',
      method: 'get'
    })
  },

  // 根据父级ID获取子分类
  getChildren: (pid: number): Promise<BaseResponse<CategoryInfo[]>> => {
    return request({
      url: `/admin/category/children/${pid}`,
      method: 'get'
    })
  },

  // 批量更新排序
  updateSort: (categories: { id: number; sortOrder: number }[]): Promise<BaseResponse<null>> => {
    return request({
      url: '/admin/category/sort',
      method: 'post',
      data: { categories }
    })
  }
}

export default categoryApi
