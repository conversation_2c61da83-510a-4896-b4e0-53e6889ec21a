import type { AxiosRequestConfig, AxiosResponse } from 'axios'

// 缓存配置接口
export interface CacheConfig {
  ttl?: number // 缓存时间，毫秒
  key?: string // 自定义缓存key
  enabled?: boolean // 是否启用缓存
  storage?: 'memory' | 'localStorage' | 'sessionStorage' // 缓存存储方式
}

// 扩展的请求配置接口
export interface CachedAxiosRequestConfig extends AxiosRequestConfig {
  cache?: CacheConfig
  __cacheConfig__?: CacheConfig
  __cacheKey__?: string
}

// 扩展的响应配置接口  
export interface CachedAxiosResponse extends Omit<AxiosResponse, 'config'> {
  config: CachedAxiosRequestConfig
}

// 缓存项接口
export interface CacheItem {
  data: any
  timestamp: number
  ttl: number
  key: string
}

// 内存缓存存储
const memoryCache = new Map<string, CacheItem>()

// 生成缓存key
export function generateCacheKey(config: AxiosRequestConfig): string {
  const { method = 'GET', url = '', params = {}, data = {} } = config
  const baseKey = `${method.toUpperCase()}:${url}`
  
  // 对参数进行排序以确保一致性
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((result, key) => {
      result[key] = params[key]
      return result
    }, {} as Record<string, any>)
  
  const paramsStr = JSON.stringify(sortedParams)
  const dataStr = method.toUpperCase() === 'GET' ? '' : JSON.stringify(data)
  
  return `${baseKey}:${btoa(paramsStr + dataStr)}`
}

// 获取缓存
export function getCache(key: string, storage: CacheConfig['storage'] = 'memory'): CacheItem | null {
  try {
    let item: CacheItem | null = null
    
    switch (storage) {
      case 'memory':
        item = memoryCache.get(key) || null
        break
      case 'localStorage':
        const localData = localStorage.getItem(`cache:${key}`)
        item = localData ? JSON.parse(localData) : null
        break
      case 'sessionStorage':
        const sessionData = sessionStorage.getItem(`cache:${key}`)
        item = sessionData ? JSON.parse(sessionData) : null
        break
    }
    
    if (!item) return null
    
    // 检查是否过期
    const now = Date.now()
    if (now - item.timestamp > item.ttl) {
      removeCache(key, storage)
      return null
    }
    
    return item
  } catch (error) {
    console.warn('获取缓存失败:', error)
    return null
  }
}

// 设置缓存
export function setCache(
  key: string, 
  data: any, 
  ttl: number = 5 * 60 * 1000, // 默认5分钟
  storage: CacheConfig['storage'] = 'memory'
): void {
  try {
    const item: CacheItem = {
      data,
      timestamp: Date.now(),
      ttl,
      key
    }
    
    switch (storage) {
      case 'memory':
        memoryCache.set(key, item)
        break
      case 'localStorage':
        localStorage.setItem(`cache:${key}`, JSON.stringify(item))
        break
      case 'sessionStorage':
        sessionStorage.setItem(`cache:${key}`, JSON.stringify(item))
        break
    }
  } catch (error) {
    console.warn('设置缓存失败:', error)
  }
}

// 删除缓存
export function removeCache(key: string, storage: CacheConfig['storage'] = 'memory'): void {
  try {
    switch (storage) {
      case 'memory':
        memoryCache.delete(key)
        break
      case 'localStorage':
        localStorage.removeItem(`cache:${key}`)
        break
      case 'sessionStorage':
        sessionStorage.removeItem(`cache:${key}`)
        break
    }
  } catch (error) {
    console.warn('删除缓存失败:', error)
  }
}

// 清空所有缓存
export function clearCache(storage: CacheConfig['storage'] = 'memory'): void {
  try {
    switch (storage) {
      case 'memory':
        memoryCache.clear()
        break
      case 'localStorage':
        Object.keys(localStorage)
          .filter(key => key.startsWith('cache:'))
          .forEach(key => localStorage.removeItem(key))
        break
      case 'sessionStorage':
        Object.keys(sessionStorage)
          .filter(key => key.startsWith('cache:'))
          .forEach(key => sessionStorage.removeItem(key))
        break
    }
  } catch (error) {
    console.warn('清空缓存失败:', error)
  }
}

// 清理过期缓存
export function cleanExpiredCache(storage: CacheConfig['storage'] = 'memory'): void {
  try {
    const now = Date.now()
    
    switch (storage) {
      case 'memory':
        for (const [key, item] of memoryCache.entries()) {
          if (now - item.timestamp > item.ttl) {
            memoryCache.delete(key)
          }
        }
        break
      case 'localStorage':
        Object.keys(localStorage)
          .filter(key => key.startsWith('cache:'))
          .forEach(key => {
            try {
              const data = localStorage.getItem(key)
              if (data) {
                const item: CacheItem = JSON.parse(data)
                if (now - item.timestamp > item.ttl) {
                  localStorage.removeItem(key)
                }
              }
            } catch (error) {
              localStorage.removeItem(key)
            }
          })
        break
      case 'sessionStorage':
        Object.keys(sessionStorage)
          .filter(key => key.startsWith('cache:'))
          .forEach(key => {
            try {
              const data = sessionStorage.getItem(key)
              if (data) {
                const item: CacheItem = JSON.parse(data)
                if (now - item.timestamp > item.ttl) {
                  sessionStorage.removeItem(key)
                }
              }
            } catch (error) {
              sessionStorage.removeItem(key)
            }
          })
        break
    }
  } catch (error) {
    console.warn('清理过期缓存失败:', error)
  }
}

// 获取缓存统计信息
export function getCacheStats(storage: CacheConfig['storage'] = 'memory'): {
  total: number
  expired: number
  active: number
  size: number
} {
  const stats = {
    total: 0,
    expired: 0,
    active: 0,
    size: 0
  }
  
  try {
    const now = Date.now()
    
    switch (storage) {
      case 'memory':
        for (const [, item] of memoryCache.entries()) {
          stats.total++
          if (now - item.timestamp > item.ttl) {
            stats.expired++
          } else {
            stats.active++
          }
          stats.size += JSON.stringify(item).length
        }
        break
      case 'localStorage':
      case 'sessionStorage':
        const storageObj = storage === 'localStorage' ? localStorage : sessionStorage
        Object.keys(storageObj)
          .filter(key => key.startsWith('cache:'))
          .forEach(key => {
            try {
              const data = storageObj.getItem(key)
              if (data) {
                stats.total++
                stats.size += data.length
                const item: CacheItem = JSON.parse(data)
                if (now - item.timestamp > item.ttl) {
                  stats.expired++
                } else {
                  stats.active++
                }
              }
            } catch (error) {
              // 忽略解析错误的项
            }
          })
        break
    }
  } catch (error) {
    console.warn('获取缓存统计失败:', error)
  }
  
  return stats
}

// 定期清理过期缓存
let cleanupTimer: NodeJS.Timeout | null = null

export function startCacheCleanup(interval: number = 10 * 60 * 1000): void {
  if (cleanupTimer) {
    clearInterval(cleanupTimer)
  }
  
  cleanupTimer = setInterval(() => {
    cleanExpiredCache('memory')
    cleanExpiredCache('localStorage')
    cleanExpiredCache('sessionStorage')
  }, interval)
}

export function stopCacheCleanup(): void {
  if (cleanupTimer) {
    clearInterval(cleanupTimer)
    cleanupTimer = null
  }
}

// 缓存拦截器
export function createCacheInterceptor() {
  return {
    request: (config: CachedAxiosRequestConfig) => {
      const cacheConfig = config.cache
      
      if (!cacheConfig?.enabled) {
        return config
      }
      
      const cacheKey = cacheConfig.key || generateCacheKey(config)
      const cached = getCache(cacheKey, cacheConfig.storage)
      
      if (cached) {
        // 返回缓存的数据，通过特殊标记表示这是缓存的响应
        return Promise.reject({
          __cached__: true,
          data: cached.data,
          status: 200,
          statusText: 'OK',
          headers: {},
          config
        })
      }
      
      // 将缓存配置添加到请求配置中，供响应拦截器使用
      config.__cacheConfig__ = cacheConfig
      config.__cacheKey__ = cacheKey
      
      return config
    },
    
    response: (response: CachedAxiosResponse) => {
      const cacheConfig = response.config.__cacheConfig__
      const cacheKey = response.config.__cacheKey__
      
      if (cacheConfig?.enabled && cacheKey) {
        setCache(
          cacheKey,
          response.data,
          cacheConfig.ttl,
          cacheConfig.storage
        )
      }
      
      return response
    },
    
    responseError: (error: any) => {
      // 处理缓存命中的情况
      if (error.__cached__) {
        return Promise.resolve(error)
      }
      
      return Promise.reject(error)
    }
  }
}

// 缓存配置预设
export const CachePresets = {
  // 短期缓存（1分钟）
  SHORT: {
    enabled: true,
    ttl: 60 * 1000,
    storage: 'memory' as const
  },
  
  // 中期缓存（5分钟）
  MEDIUM: {
    enabled: true,
    ttl: 5 * 60 * 1000,
    storage: 'memory' as const
  },
  
  // 长期缓存（30分钟）
  LONG: {
    enabled: true,
    ttl: 30 * 60 * 1000,
    storage: 'localStorage' as const
  },
  
  // 会话级缓存
  SESSION: {
    enabled: true,
    ttl: 24 * 60 * 60 * 1000, // 24小时
    storage: 'sessionStorage' as const
  },
  
  // 禁用缓存
  DISABLED: {
    enabled: false
  }
} as const

export default {
  generateCacheKey,
  getCache,
  setCache,
  removeCache,
  clearCache,
  cleanExpiredCache,
  getCacheStats,
  startCacheCleanup,
  stopCacheCleanup,
  createCacheInterceptor,
  CachePresets
}
