/**
 * URL处理工具函数
 * URL utilities
 */
import type { ExternalLinkChecker, ParamObject, QueryObject } from './types'

/**
 * 解析URL查询参数
 * @param {string} url - URL字符串，默认为当前页面URL
 * @returns {QueryObject} 查询参数对象
 */
export function getQueryObject(url?: string): QueryObject {
  const targetUrl = url == null ? window.location.href : url
  const search = targetUrl.substring(targetUrl.lastIndexOf('?') + 1)
  const obj: QueryObject = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  
  return obj
}

/**
 * 将对象转换为URL参数字符串
 * @param {ParamObject} json - 参数对象
 * @returns {string} URL参数字符串
 */
export function param(json: ParamObject): string {
  if (!json) return ''
  
  return cleanArray(
    Object.keys(json).map(key => {
      if (json[key] === undefined) return ''
      return encodeURIComponent(key) + '=' + encodeURIComponent(String(json[key]))
    })
  ).join('&')
}

/**
 * 将URL参数字符串转换为对象
 * @param {string} url - 包含参数的URL
 * @returns {QueryObject} 参数对象
 */
export function param2Obj(url: string): QueryObject {
  const search = url.split('?')[1]
  if (!search) {
    return {}
  }
  
  try {
    return JSON.parse(
      '{"' +
        decodeURIComponent(search)
          .replace(/"/g, '\\"')
          .replace(/&/g, '","')
          .replace(/=/g, '":"') +
        '"}'
    )
  } catch (e) {
    console.error('Failed to parse URL parameters:', e)
    return {}
  }
}

/**
 * 清理数组，移除空值
 * @param {any[]} actual - 原始数组
 * @returns {any[]} 清理后的数组
 */
function cleanArray(actual: any[]): any[] {
  const newArray = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i])
    }
  }
  return newArray
}

/**
 * 检查是否为外部链接
 * @param {string} path - 路径或URL
 * @returns {boolean} 是否为外部链接
 */
export const isExternal: ExternalLinkChecker = (path: string): boolean => {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * 构建完整URL
 * @param {string} baseUrl - 基础URL
 * @param {string} path - 路径
 * @param {ParamObject} params - 查询参数
 * @returns {string} 完整URL
 */
export function buildUrl(baseUrl: string, path?: string, params?: ParamObject): string {
  let url = baseUrl
  
  if (path) {
    url = url.endsWith('/') ? url + path : url + '/' + path
  }
  
  if (params && Object.keys(params).length > 0) {
    const queryString = param(params)
    if (queryString) {
      url += (url.includes('?') ? '&' : '?') + queryString
    }
  }
  
  return url
}

/**
 * 获取URL的基础路径
 * @param {string} url - 完整URL
 * @returns {string} 基础路径
 */
export function getBasePath(url: string): string {
  try {
    const urlObj = new URL(url)
    return urlObj.origin
  } catch (e) {
    return ''
  }
}

/**
 * 获取文件扩展名
 * @param {string} url - URL或文件名
 * @returns {string} 文件扩展名
 */
export function getFileExtension(url: string): string {
  const path = url.split('?')[0] // 移除查询参数
  const parts = path.split('.')
  return parts.length > 1 ? parts.pop()?.toLowerCase() || '' : ''
}

/**
 * 获取文件名（不含扩展名）
 * @param {string} url - URL或文件路径
 * @returns {string} 文件名
 */
export function getFileName(url: string): string {
  const path = url.split('?')[0] // 移除查询参数
  const fileName = path.split('/').pop() || ''
  const lastDotIndex = fileName.lastIndexOf('.')
  return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName
}

/**
 * 获取完整文件名（含扩展名）
 * @param {string} url - URL或文件路径
 * @returns {string} 完整文件名
 */
export function getFullFileName(url: string): string {
  const path = url.split('?')[0] // 移除查询参数
  return path.split('/').pop() || ''
}

/**
 * 添加查询参数到URL
 * @param {string} url - 原始URL
 * @param {ParamObject} params - 要添加的参数
 * @returns {string} 新URL
 */
export function addQueryParams(url: string, params: ParamObject): string {
  if (!params || Object.keys(params).length === 0) {
    return url
  }
  
  const separator = url.includes('?') ? '&' : '?'
  const queryString = param(params)
  
  return queryString ? url + separator + queryString : url
}

/**
 * 移除URL中的查询参数
 * @param {string} url - 包含查询参数的URL
 * @param {string[]} paramsToRemove - 要移除的参数名数组
 * @returns {string} 处理后的URL
 */
export function removeQueryParams(url: string, paramsToRemove: string[]): string {
  const [baseUrl, queryString] = url.split('?')
  
  if (!queryString) {
    return url
  }
  
  const currentParams = getQueryObject(url)
  
  // 移除指定参数
  paramsToRemove.forEach(param => {
    delete currentParams[param]
  })
  
  // 重新构建URL
  const newQueryString = param(currentParams)
  return newQueryString ? baseUrl + '?' + newQueryString : baseUrl
}

/**
 * 更新URL中的查询参数
 * @param {string} url - 原始URL
 * @param {ParamObject} updates - 要更新的参数
 * @returns {string} 更新后的URL
 */
export function updateQueryParams(url: string, updates: ParamObject): string {
  const [baseUrl] = url.split('?')
  const currentParams = getQueryObject(url)
  
  // 合并参数
  const newParams = { ...currentParams, ...updates }
  
  // 移除值为undefined的参数
  Object.keys(newParams).forEach(key => {
    if (newParams[key] === undefined) {
      delete newParams[key]
    }
  })
  
  const queryString = param(newParams)
  return queryString ? baseUrl + '?' + queryString : baseUrl
}

/**
 * 验证URL格式
 * @param {string} url - 要验证的URL
 * @returns {boolean} 是否为有效URL
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 获取当前页面的查询参数
 * @returns {QueryObject} 当前页面的查询参数
 */
export function getCurrentQueryParams(): QueryObject {
  return getQueryObject(window.location.href)
}

/**
 * 获取当前页面的hash值
 * @returns {string} hash值（不含#）
 */
export function getCurrentHash(): string {
  return window.location.hash.substring(1)
}

/**
 * 设置当前页面的hash值
 * @param {string} hash - 新的hash值
 */
export function setCurrentHash(hash: string): void {
  window.location.hash = hash
}

/**
 * 获取域名
 * @param {string} url - URL
 * @returns {string} 域名
 */
export function getDomain(url: string): string {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch {
    return ''
  }
}

/**
 * 检查两个URL是否同域
 * @param {string} url1 - 第一个URL
 * @param {string} url2 - 第二个URL
 * @returns {boolean} 是否同域
 */
export function isSameDomain(url1: string, url2: string): boolean {
  return getDomain(url1) === getDomain(url2)
}
