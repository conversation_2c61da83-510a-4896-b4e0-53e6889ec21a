/**
 * 字符串处理工具函数
 * String utilities
 */
import type { ByteLengthFunction, HtmlToTextFunction } from './types'

/**
 * 将HTML转换为纯文本
 * @param {string} val - HTML字符串
 * @returns {string} 纯文本
 */
export const html2Text: HtmlToTextFunction = (val: string): string => {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText || ''
}

/**
 * 获取字符串字节长度
 * @param {string} val - 输入字符串
 * @returns {number} 字节长度
 */
export const getByteLen: ByteLengthFunction = (val: string): number => {
  let len = 0
  for (let i = 0; i < val.length; i++) {
    const char = val[i]
    // 中文字符算1个字节，其他字符算0.5个字节
    if (char.match(/[^\x00-\xff]/gi) != null) {
      len += 1
    } else {
      len += 0.5
    }
  }
  return Math.floor(len)
}

/**
 * 字符串首字母大写
 * @param {string} str - 输入字符串
 * @returns {string} 首字母大写的字符串
 */
export function capitalize(str: string): string {
  if (!str) return str
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

/**
 * 驼峰命名转换
 * @param {string} str - 输入字符串
 * @returns {string} 驼峰命名字符串
 */
export function toCamelCase(str: string): string {
  return str.replace(/-(.)/g, (_, char) => char.toUpperCase())
}

/**
 * 连字符命名转换
 * @param {string} str - 输入字符串
 * @returns {string} 连字符命名字符串
 */
export function toKebabCase(str: string): string {
  return str.replace(/([A-Z])/g, '-$1').toLowerCase()
}

/**
 * 下划线命名转换
 * @param {string} str - 输入字符串
 * @returns {string} 下划线命名字符串
 */
export function toSnakeCase(str: string): string {
  return str.replace(/([A-Z])/g, '_$1').toLowerCase()
}

/**
 * 移除字符串中的HTML标签
 * @param {string} str - 输入字符串
 * @returns {string} 移除HTML标签后的字符串
 */
export function stripHtml(str: string): string {
  return str.replace(/<[^>]*>/g, '')
}

/**
 * 截断字符串并添加省略号
 * @param {string} str - 输入字符串
 * @param {number} length - 最大长度
 * @param {string} suffix - 后缀，默认为'...'
 * @returns {string} 截断后的字符串
 */
export function truncate(str: string, length: number, suffix: string = '...'): string {
  if (str.length <= length) return str
  return str.substring(0, length) + suffix
}

/**
 * 按字节截断字符串
 * @param {string} str - 输入字符串
 * @param {number} maxBytes - 最大字节数
 * @param {string} suffix - 后缀
 * @returns {string} 截断后的字符串
 */
export function truncateByBytes(str: string, maxBytes: number, suffix: string = '...'): string {
  let bytes = 0
  let result = ''
  
  for (let i = 0; i < str.length; i++) {
    const char = str[i]
    const charBytes = char.match(/[^\x00-\xff]/gi) ? 2 : 1
    
    if (bytes + charBytes > maxBytes) {
      break
    }
    
    bytes += charBytes
    result += char
  }
  
  return result.length < str.length ? result + suffix : result
}

/**
 * 生成随机字符串
 * @param {number} length - 长度
 * @param {string} chars - 字符集
 * @returns {string} 随机字符串
 */
export function randomString(
  length: number = 8,
  chars: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
): string {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成随机数字字符串
 * @param {number} length - 长度
 * @returns {string} 随机数字字符串
 */
export function randomNumericString(length: number = 6): string {
  return randomString(length, '0123456789')
}

/**
 * 填充字符串
 * @param {string} str - 原字符串
 * @param {number} length - 目标长度
 * @param {string} padChar - 填充字符
 * @param {'start' | 'end'} position - 填充位置
 * @returns {string} 填充后的字符串
 */
export function padString(
  str: string,
  length: number,
  padChar: string = ' ',
  position: 'start' | 'end' = 'start'
): string {
  if (str.length >= length) return str
  
  const padLength = length - str.length
  const padding = padChar.repeat(Math.ceil(padLength / padChar.length)).substring(0, padLength)
  
  return position === 'start' ? padding + str : str + padding
}

/**
 * 移除字符串前后空白
 * @param {string} str - 输入字符串
 * @returns {string} 移除空白后的字符串
 */
export function trim(str: string): string {
  return str.trim()
}

/**
 * 移除字符串中的所有空白
 * @param {string} str - 输入字符串
 * @returns {string} 移除所有空白后的字符串
 */
export function removeAllSpaces(str: string): string {
  return str.replace(/\s/g, '')
}

/**
 * 替换字符串中的多个连续空格为单个空格
 * @param {string} str - 输入字符串
 * @returns {string} 处理后的字符串
 */
export function normalizeSpaces(str: string): string {
  return str.replace(/\s+/g, ' ').trim()
}

/**
 * 反转字符串
 * @param {string} str - 输入字符串
 * @returns {string} 反转后的字符串
 */
export function reverse(str: string): string {
  return str.split('').reverse().join('')
}

/**
 * 计算字符串出现次数
 * @param {string} str - 主字符串
 * @param {string} searchStr - 搜索字符串
 * @returns {number} 出现次数
 */
export function countOccurrences(str: string, searchStr: string): number {
  if (!searchStr) return 0
  
  let count = 0
  let position = 0
  
  while ((position = str.indexOf(searchStr, position)) !== -1) {
    count++
    position += searchStr.length
  }
  
  return count
}

/**
 * 检查字符串是否包含中文字符
 * @param {string} str - 输入字符串
 * @returns {boolean} 是否包含中文
 */
export function containsChinese(str: string): boolean {
  return /[\u4e00-\u9fa5]/.test(str)
}

/**
 * 检查字符串是否只包含数字
 * @param {string} str - 输入字符串
 * @returns {boolean} 是否只包含数字
 */
export function isNumeric(str: string): boolean {
  return /^\d+$/.test(str)
}

/**
 * 检查字符串是否为空白
 * @param {string} str - 输入字符串
 * @returns {boolean} 是否为空白
 */
export function isBlank(str: string): boolean {
  return !str || /^\s*$/.test(str)
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i]
}

/**
 * 格式化数字为千分位
 * @param {number} num - 数字
 * @returns {string} 格式化后的字符串
 */
export function formatNumber(num: number): string {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * 掩码处理（隐藏敏感信息）
 * @param {string} str - 输入字符串
 * @param {number} start - 开始位置
 * @param {number} end - 结束位置
 * @param {string} mask - 掩码字符
 * @returns {string} 掩码后的字符串
 */
export function maskString(str: string, start: number, end: number, mask: string = '*'): string {
  if (start >= str.length || end <= start) return str
  
  const before = str.substring(0, start)
  const after = str.substring(end)
  const maskLength = end - start
  
  return before + mask.repeat(maskLength) + after
}

/**
 * 手机号掩码
 * @param {string} phone - 手机号
 * @returns {string} 掩码后的手机号
 */
export function maskPhone(phone: string): string {
  if (phone.length !== 11) return phone
  return maskString(phone, 3, 7)
}

/**
 * 身份证号掩码
 * @param {string} idCard - 身份证号
 * @returns {string} 掩码后的身份证号
 */
export function maskIdCard(idCard: string): string {
  if (idCard.length < 8) return idCard
  return maskString(idCard, 6, idCard.length - 4)
}

/**
 * 邮箱掩码
 * @param {string} email - 邮箱
 * @returns {string} 掩码后的邮箱
 */
export function maskEmail(email: string): string {
  const atIndex = email.indexOf('@')
  if (atIndex <= 0) return email
  
  const username = email.substring(0, atIndex)
  const domain = email.substring(atIndex)
  
  if (username.length <= 2) return email
  
  const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1)
  return maskedUsername + domain
}

/**
 * 模板字符串替换
 * @param {string} template - 模板字符串
 * @param {Record<string, any>} data - 数据对象
 * @returns {string} 替换后的字符串
 */
export function template(template: string, data: Record<string, any>): string {
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return data[key] !== undefined ? String(data[key]) : match
  })
}
