/**
 * 对象处理工具函数
 * Object utilities
 */
import type {
    CleanArrayFunction,
    DeepCloneFunction,
    ObjectMergeFunction,
    UniqueArrayFunction
} from './types'

/**
 * 对象合并 - 深度合并两个对象
 * @param {T} target - 目标对象
 * @param {U} source - 源对象
 * @returns {T & U} 合并后的对象
 */
export const objectMerge: ObjectMergeFunction = <T, U>(target: T, source: U): T & U => {
  /* 合并两个对象，后者优先 */
  let result: any = target

  if (typeof result !== 'object') {
    result = {}
  }
  
  if (Array.isArray(source)) {
    return source.slice() as T & U
  }
  
  if (source && typeof source === 'object') {
    Object.keys(source).forEach(property => {
      const sourceProperty = (source as any)[property]
      if (typeof sourceProperty === 'object' && sourceProperty !== null) {
        result[property] = objectMerge(result[property], sourceProperty)
      } else {
        result[property] = sourceProperty
      }
    })
  }
  
  return result as T & U
}

/**
 * 深度克隆
 * 注意：这是一个简化版本的深克隆，有一些边界情况的bug
 * 如果需要完美的深克隆，建议使用lodash的_.cloneDeep
 * @param {T} source - 源对象
 * @returns {T} 克隆后的对象
 */
export const deepClone: DeepCloneFunction = <T>(source: T): T => {
  if (!source || typeof source !== 'object') {
    throw new Error('error arguments for deepClone')
  }
  
  const targetObj: any = (source as any).constructor === Array ? [] : {}
  
  Object.keys(source as any).forEach(keys => {
    const sourceValue = (source as any)[keys]
    if (sourceValue && typeof sourceValue === 'object') {
      targetObj[keys] = deepClone(sourceValue)
    } else {
      targetObj[keys] = sourceValue
    }
  })
  
  return targetObj as T
}

/**
 * 清理数组 - 移除falsy值
 * @param {T[]} actual - 原始数组
 * @returns {T[]} 清理后的数组
 */
export const cleanArray: CleanArrayFunction = <T>(
  actual: (T | null | undefined | false | '')[]
): T[] => {
  const newArray: T[] = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i] as T)
    }
  }
  return newArray
}

/**
 * 数组去重
 * @param {T[]} arr - 原始数组
 * @returns {T[]} 去重后的数组
 */
export const uniqueArr: UniqueArrayFunction = <T>(arr: T[]): T[] => {
  return Array.from(new Set(arr))
}

/**
 * 检查对象是否为空
 * @param {any} obj - 要检查的对象
 * @returns {boolean} 是否为空
 */
export function isEmpty(obj: any): boolean {
  if (obj == null) return true
  if (Array.isArray(obj)) return obj.length === 0
  if (typeof obj === 'object') return Object.keys(obj).length === 0
  if (typeof obj === 'string') return obj.length === 0
  return false
}

/**
 * 检查是否为对象
 * @param {any} value - 要检查的值
 * @returns {boolean} 是否为对象
 */
export function isObject(value: any): value is object {
  return value !== null && typeof value === 'object' && !Array.isArray(value)
}

/**
 * 检查是否为数组
 * @param {any} value - 要检查的值
 * @returns {boolean} 是否为数组
 */
export function isArray(value: any): value is any[] {
  return Array.isArray(value)
}

/**
 * 获取对象的值数组
 * @param {Record<string, T>} obj - 对象
 * @returns {T[]} 值数组
 */
export function getObjectValues<T>(obj: Record<string, T>): T[] {
  return Object.values(obj)
}

/**
 * 获取对象的键数组
 * @param {Record<string, any>} obj - 对象
 * @returns {string[]} 键数组
 */
export function getObjectKeys(obj: Record<string, any>): string[] {
  return Object.keys(obj)
}

/**
 * 获取对象的键值对数组
 * @param {Record<string, T>} obj - 对象
 * @returns {[string, T][]} 键值对数组
 */
export function getObjectEntries<T>(obj: Record<string, T>): [string, T][] {
  return Object.entries(obj)
}

/**
 * 根据键路径获取对象的嵌套值
 * @param {any} obj - 对象
 * @param {string} path - 键路径，如 'a.b.c'
 * @param {any} defaultValue - 默认值
 * @returns {any} 获取到的值
 */
export function getNestedValue(obj: any, path: string, defaultValue?: any): any {
  const keys = path.split('.')
  let result = obj
  
  for (const key of keys) {
    if (result == null || typeof result !== 'object') {
      return defaultValue
    }
    result = result[key]
  }
  
  return result !== undefined ? result : defaultValue
}

/**
 * 根据键路径设置对象的嵌套值
 * @param {any} obj - 对象
 * @param {string} path - 键路径，如 'a.b.c'
 * @param {any} value - 要设置的值
 */
export function setNestedValue(obj: any, path: string, value: any): void {
  const keys = path.split('.')
  let current = obj
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i]
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {}
    }
    current = current[key]
  }
  
  current[keys[keys.length - 1]] = value
}

/**
 * 删除对象中的指定键
 * @param {Record<string, any>} obj - 对象
 * @param {string[]} keys - 要删除的键数组
 * @returns {Record<string, any>} 新对象
 */
export function omit(obj: Record<string, any>, keys: string[]): Record<string, any> {
  const result = { ...obj }
  keys.forEach(key => {
    delete result[key]
  })
  return result
}

/**
 * 只保留对象中的指定键
 * @param {Record<string, any>} obj - 对象
 * @param {string[]} keys - 要保留的键数组
 * @returns {Record<string, any>} 新对象
 */
export function pick(obj: Record<string, any>, keys: string[]): Record<string, any> {
  const result: Record<string, any> = {}
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key]
    }
  })
  return result
}

/**
 * 过滤对象的值
 * @param {Record<string, T>} obj - 对象
 * @param {function} predicate - 判断函数
 * @returns {Record<string, T>} 过滤后的对象
 */
export function filterObject<T>(
  obj: Record<string, T>,
  predicate: (value: T, key: string) => boolean
): Record<string, T> {
  const result: Record<string, T> = {}
  Object.entries(obj).forEach(([key, value]) => {
    if (predicate(value, key)) {
      result[key] = value
    }
  })
  return result
}

/**
 * 映射对象的值
 * @param {Record<string, T>} obj - 对象
 * @param {function} mapper - 映射函数
 * @returns {Record<string, U>} 映射后的对象
 */
export function mapObject<T, U>(
  obj: Record<string, T>,
  mapper: (value: T, key: string) => U
): Record<string, U> {
  const result: Record<string, U> = {}
  Object.entries(obj).forEach(([key, value]) => {
    result[key] = mapper(value, key)
  })
  return result
}

/**
 * 扁平化嵌套对象
 * @param {Record<string, any>} obj - 嵌套对象
 * @param {string} prefix - 键前缀
 * @returns {Record<string, any>} 扁平化后的对象
 */
export function flattenObject(
  obj: Record<string, any>,
  prefix: string = ''
): Record<string, any> {
  const result: Record<string, any> = {}
  
  Object.keys(obj).forEach(key => {
    const newKey = prefix ? `${prefix}.${key}` : key
    const value = obj[key]
    
    if (isObject(value) && !isArray(value)) {
      Object.assign(result, flattenObject(value, newKey))
    } else {
      result[newKey] = value
    }
  })
  
  return result
}

/**
 * 比较两个对象是否相等（浅比较）
 * @param {any} obj1 - 对象1
 * @param {any} obj2 - 对象2
 * @returns {boolean} 是否相等
 */
export function shallowEqual(obj1: any, obj2: any): boolean {
  if (obj1 === obj2) return true
  
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object') {
    return false
  }
  
  if (obj1 == null || obj2 == null) {
    return obj1 === obj2
  }
  
  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)
  
  if (keys1.length !== keys2.length) {
    return false
  }
  
  for (const key of keys1) {
    if (obj1[key] !== obj2[key]) {
      return false
    }
  }
  
  return true
}

/**
 * 数组分组
 * @param {T[]} array - 数组
 * @param {function} keyGetter - 获取分组键的函数
 * @returns {Record<string, T[]>} 分组后的对象
 */
export function groupBy<T>(
  array: T[],
  keyGetter: (item: T) => string
): Record<string, T[]> {
  const result: Record<string, T[]> = {}
  
  array.forEach(item => {
    const key = keyGetter(item)
    if (!result[key]) {
      result[key] = []
    }
    result[key].push(item)
  })
  
  return result
}

/**
 * 安全的JSON解析
 * @param {string} jsonString - JSON字符串
 * @param {T} defaultValue - 默认值
 * @returns {T} 解析结果
 */
export function safeJsonParse<T>(jsonString: string, defaultValue: T): T {
  try {
    return JSON.parse(jsonString)
  } catch {
    return defaultValue
  }
}

/**
 * 安全的JSON序列化
 * @param {any} obj - 要序列化的对象
 * @param {string} defaultValue - 默认值
 * @returns {string} 序列化结果
 */
export function safeJsonStringify(obj: any, defaultValue: string = '{}'): string {
  try {
    return JSON.stringify(obj)
  } catch {
    return defaultValue
  }
}
