/**
 * 验证工具函数
 * Validation utilities
 */
import type { ValidationResult, ValidatorFunction } from './types'

/**
 * 验证用户名是否有效
 * @param {string} str - 用户名
 * @returns {boolean} 是否有效
 */
export function isValidUsername(str: string): ValidationResult {
  const validMap = ['admin', 'editor']
  return validMap.indexOf(str.trim()) >= 0
}

/**
 * 验证URL是否合法
 * @param {string} textval - URL字符串
 * @returns {boolean} 是否合法
 */
export function validateURL(textval: string): ValidationResult {
  const urlregex = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return urlregex.test(textval)
}

/**
 * 验证小写字母
 * @param {string} str - 字符串
 * @returns {boolean} 是否为小写字母
 */
export function validateLowerCase(str: string): ValidationResult {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/**
 * 验证大写字母
 * @param {string} str - 字符串
 * @returns {boolean} 是否为大写字母
 */
export function validateUpperCase(str: string): ValidationResult {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/**
 * 验证大小写字母
 * @param {string} str - 字符串
 * @returns {boolean} 是否为字母
 */
export function validateAlphabets(str: string): ValidationResult {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

/**
 * 验证邮箱地址
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否为有效邮箱
 */
export function validateEmail(email: string): ValidationResult {
  const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return re.test(email)
}

/**
 * 验证手机号码
 * @param {string} phone - 手机号码
 * @returns {boolean} 是否为有效手机号
 */
export function validatePhone(phone: string): ValidationResult {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证身份证号码
 * @param {string} idCard - 身份证号码
 * @returns {boolean} 是否为有效身份证号
 */
export function validateIdCard(idCard: string): ValidationResult {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardRegex.test(idCard)
}

/**
 * 验证密码强度
 * @param {string} password - 密码
 * @returns {boolean} 是否为强密码
 */
export function validateStrongPassword(password: string): ValidationResult {
  // 至少8位，包含大小写字母、数字和特殊字符
  const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
  return strongPasswordRegex.test(password)
}

/**
 * 验证中文字符
 * @param {string} str - 字符串
 * @returns {boolean} 是否为中文字符
 */
export function validateChinese(str: string): ValidationResult {
  const chineseRegex = /^[\u4e00-\u9fa5]+$/
  return chineseRegex.test(str)
}

/**
 * 验证数字
 * @param {string} str - 字符串
 * @returns {boolean} 是否为数字
 */
export function validateNumber(str: string): ValidationResult {
  const numberRegex = /^\d+$/
  return numberRegex.test(str)
}

/**
 * 验证正整数
 * @param {string} str - 字符串
 * @returns {boolean} 是否为正整数
 */
export function validatePositiveInteger(str: string): ValidationResult {
  const positiveIntegerRegex = /^[1-9]\d*$/
  return positiveIntegerRegex.test(str)
}

/**
 * 验证IP地址
 * @param {string} ip - IP地址
 * @returns {boolean} 是否为有效IP地址
 */
export function validateIP(ip: string): ValidationResult {
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  return ipRegex.test(ip)
}

/**
 * 创建自定义验证器
 * @param {RegExp} regex - 正则表达式
 * @returns {ValidatorFunction} 验证函数
 */
export function createValidator(regex: RegExp): ValidatorFunction {
  return (value: string): ValidationResult => regex.test(value)
}

// 导出旧版兼容函数
export const isvalidUsername = isValidUsername
