# Utils 工具函数迁移指南

## 迁移概述

本次迁移将原有的 JavaScript 工具函数库完全重构为 TypeScript，并适配 Vue 3 生态系统。

## 迁移完成的模块

### 1. 基础模块（直接迁移）

- ✅ `auth.ts` - 认证相关工具函数
- ✅ `createUniqueString.ts` - 唯一字符串生成
- ✅ `openWindow.ts` - 新窗口打开工具
- ✅ `validate.ts` - 验证工具函数
- ✅ `scrollTo.ts` - 滚动动画工具

### 2. Vue 3 适配模块

- ✅ `clipboard.ts` - 剪贴板操作（适配 Shadcn UI Toast）
- ✅ `permission.ts` - 权限检查（适配 Pinia store）
- ✅ `i18n.ts` - 国际化工具（适配 Vue 3 i18n）

### 3. 重构模块（拆分自原 index.js）

- ✅ `time.ts` - 时间处理工具
- ✅ `url.ts` - URL 处理工具
- ✅ `object.ts` - 对象操作工具
- ✅ `string.ts` - 字符串处理工具
- ✅ `async.ts` - 异步工具函数
- ✅ `dom.ts` - DOM 操作工具

### 4. HTTP 相关模块（项目已有）

- ✅ `cache.ts` - HTTP 缓存工具
- ✅ `cancel.ts` - 请求取消工具
- ✅ `request.ts` - HTTP 请求工具
- ✅ `retry.ts` - 重试机制工具

### 5. 支撑模块

- ✅ `types/index.ts` - 完整的 TypeScript 类型定义
- ✅ `index.ts` - 统一导出入口

## 主要改进

### 1. TypeScript 支持

- 完整的类型定义和类型安全
- 更好的 IDE 支持和代码提示
- 编译时错误检查

### 2. 模块化设计

- 按功能拆分模块，便于按需导入
- 清晰的模块边界和职责
- 支持 Tree Shaking

### 3. Vue 3 生态适配

- 适配 Pinia 状态管理
- 适配 Vue 3 i18n
- 适配 Shadcn UI 组件库

### 4. 向后兼容

- 保持原有 API 兼容性
- 支持依赖注入模式
- 渐进式迁移支持

### 5. HTTP 工具增强

- 完整的缓存机制
- 请求取消支持
- 智能重试策略
- 拦截器支持

## 使用方式

### 1. 全量导入（向后兼容）

```typescript
import * as utils from '@/utils'

// 使用原有方式
utils.parseTime(new Date())
utils.checkPermission(['GET /api/users'])
```

### 2. 按需导入（推荐）

```typescript
import { parseTime, formatTime } from '@/utils/time'
import { checkPermission } from '@/utils/permission'
import { copyToClipboard } from '@/utils/clipboard'
import { createRetryInterceptor } from '@/utils/retry'
import { createCacheInterceptor } from '@/utils/cache'
```

### 3. 模块导入

```typescript
import { TimeUtils, UrlUtils } from '@/utils'

TimeUtils.parseTime(new Date())
UrlUtils.getQueryObject()
```

### 4. HTTP 工具使用

```typescript
import { 
  CachePresets, 
  RetryPresets, 
  createCacheInterceptor,
  createRetryInterceptor 
} from '@/utils'

// 配置 Axios 拦截器
axios.interceptors.request.use(createCacheInterceptor().request)
axios.interceptors.response.use(
  createRetryInterceptor().response,
  createRetryInterceptor().responseError
)
```

## 初始化配置

```typescript
import { initUtils } from '@/utils'
import { useI18n } from 'vue-i18n'
import { usePermissionStore } from '@/stores/permission'
import { toast } from '@/components/ui/toast'

// 在应用启动时初始化
initUtils({
  i18n: useI18n(),
  permissionStore: usePermissionStore(),
  toastHandlers: {
    success: () => toast.success('复制成功'),
    error: () => toast.error('复制失败')
  }
})
```

## 迁移检查清单

### ✅ 已完成

1. [x] 创建完整的 TypeScript 类型定义
2. [x] 迁移所有基础工具函数
3. [x] 适配 Vue 3 相关依赖
4. [x] 重构 index.js 为多个专门模块
5. [x] 创建统一导出入口
6. [x] 保持向后兼容性
7. [x] 添加依赖注入支持
8. [x] 集成 HTTP 相关工具模块
9. [x] 解决导出冲突问题

### 📋 后续任务

1. [ ] 编写单元测试
2. [ ] 添加 JSDoc 文档
3. [ ] 性能优化和测试
4. [ ] 创建使用示例
5. [ ] 更新项目文档

## 依赖关系

### 新增依赖

- `clipboard` - 剪贴板操作库
- `@types/clipboard` - clipboard 类型定义
- `axios` - HTTP 请求库（HTTP 模块依赖）

### 移除的依赖

- 无（保持最小化变更）

## 文件结构

```
src/utils/
├── types/
│   └── index.ts          # 类型定义
├── auth.ts               # 认证工具
├── clipboard.ts          # 剪贴板工具
├── createUniqueString.ts # 唯一字符串生成
├── i18n.ts               # 国际化工具
├── openWindow.ts         # 窗口操作
├── permission.ts         # 权限检查
├── scrollTo.ts           # 滚动动画
├── validate.ts           # 验证工具
├── time.ts               # 时间处理
├── url.ts                # URL 处理
├── object.ts             # 对象操作
├── string.ts             # 字符串处理
├── async.ts              # 异步工具
├── dom.ts                # DOM 操作
├── cache.ts              # HTTP 缓存
├── cancel.ts             # 请求取消
├── request.ts            # HTTP 请求
├── retry.ts              # 重试机制
├── index.ts              # 统一导出
└── MIGRATION_GUIDE.md    # 本文档
```

## 注意事项

1. **类型安全**: 所有函数都有完整的类型定义，使用时会有类型检查
2. **依赖注入**: 部分工具函数需要在应用启动时进行初始化配置
3. **兼容性**: 保持了与原有代码的完全兼容性
4. **性能**: 支持按需导入，减少打包体积
5. **导出冲突**: 已解决 delay 函数冲突，重试模块的 delay 重命名为 retryDelay

## 常见问题

### Q: 如何迁移现有代码？

A: 现有代码无需修改，可以继续使用原有的导入方式。建议逐步迁移到按需导入。

### Q: 类型定义在哪里？

A: 所有类型定义都在 `types/index.ts` 中，会自动导出到 `index.ts`。

### Q: 如何处理依赖注入？

A: 在应用的 `main.ts` 中调用 `initUtils()` 进行初始化配置。

### Q: 是否支持 Tree Shaking？

A: 是的，新的模块化设计完全支持 Tree Shaking，只会打包实际使用的代码。

### Q: HTTP 工具如何配置？

A: 可以使用预设配置（如 CachePresets.MEDIUM, RetryPresets.DEFAULT）或自定义配置。

### Q: 导出冲突如何处理？

A: 项目中有些函数名冲突（如 delay），已通过重命名导出解决（retryDelay）。

## 总结

本次迁移成功地将 JavaScript 工具函数库升级为现代化的 TypeScript 库，提供了：

- 完整的类型安全
- 模块化设计
- Vue 3 生态适配
- 向后兼容性
- 更好的开发体验
- 强大的 HTTP 工具支持
- 智能缓存和重试机制

所有迁移工作已完成，包括基础工具函数和 HTTP 相关模块，可以立即在项目中使用。
