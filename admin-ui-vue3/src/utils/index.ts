/**
 * 工具函数统一导出入口
 * Utils index - unified export entry
 * 
 * 这个文件提供了所有工具函数的统一导出入口
 * 支持按需导入和批量导入两种方式
 */

// 类型定义导出
export * from './types'

// 认证相关
export * from './auth'

// 剪贴板操作
export * from './clipboard'

// 唯一字符串生成
export * from './createUniqueString'

// 国际化
export * from './i18n'

// 新窗口打开
export * from './openWindow'

// 权限检查
export * from './permission'

// 滚动动画
export * from './scrollTo'

// 验证工具
export * from './validate'

// 时间处理
export * from './time'

// URL处理
export * from './url'

// 对象操作
export * from './object'

// 字符串处理
export * from './string'

// 异步工具
export * from './async'

// DOM操作
export * from './dom'

// HTTP缓存
export * from './cache'

// 请求取消
export * from './cancel'

// HTTP请求
export * from './request'

// 重试机制 - 明确导出以避免冲突
export {
  calculateRetryDelay, createRetryCondition, createRetryInterceptor,
  createRetryInterceptorWithStats, defaultRetryConfig, ErrorConditions, RetryPresets, retryStats, shouldRetry
} from './retry'

// 重试机制的delay函数重命名导出
export { delay as retryDelay } from './retry'

// 默认导出 - 向后兼容
export { default as handleClipboard } from './clipboard'
export { default as createUniqueString } from './createUniqueString'
export { default as generateTitle } from './i18n'
export { default as openWindow } from './openWindow'
export { default as checkPermission } from './permission'
export { default as scrollTo } from './scrollTo'

// 常用工具函数的简化导出 - 保持与原index.js的兼容性
import { debounce } from './async'
import { addClass, hasClass, removeClass, toggleClass } from './dom'
import { cleanArray, deepClone, objectMerge, uniqueArr } from './object'
import { getByteLen, html2Text } from './string'
import { formatTime, getTime, parseTime } from './time'
import { getQueryObject, isExternal, param, param2Obj } from './url'

// 兼容原有的导出方式
export {
  addClass, cleanArray, debounce, deepClone, formatTime, getByteLen, getQueryObject, getTime, hasClass, html2Text, isExternal,
  objectMerge, param,
  param2Obj, parseTime, removeClass,
  toggleClass, uniqueArr
}

// 工具函数分组导出 - 便于按功能导入
export const TimeUtils = {
  parseTime,
  formatTime,
  getTime
}

export const UrlUtils = {
  getQueryObject,
  param,
  param2Obj,
  isExternal
}

export const ObjectUtils = {
  objectMerge,
  deepClone,
  cleanArray,
  uniqueArr
}

export const StringUtils = {
  html2Text,
  getByteLen
}

export const AsyncUtils = {
  debounce
}

export const DomUtils = {
  hasClass,
  addClass,
  removeClass,
  toggleClass
}

/**
 * 工具函数版本信息
 */
export const VERSION = '3.0.0'

/**
 * 工具函数初始化
 * 用于设置各种依赖注入
 */
export interface UtilsConfig {
  i18n?: any
  permissionStore?: any
  toastHandlers?: {
    success: () => void
    error: () => void
  }
}

let isInitialized = false

/**
 * 初始化工具函数库
 * @param {UtilsConfig} config - 配置对象
 */
export function initUtils(config: UtilsConfig = {}): void {
  if (isInitialized) {
    console.warn('Utils already initialized')
    return
  }

  // 初始化i18n
  if (config.i18n) {
    const { setI18nInstance } = require('./i18n')
    setI18nInstance(config.i18n)
  }

  // 初始化权限系统
  if (config.permissionStore) {
    const { setPermissionStore } = require('./permission')
    setPermissionStore(config.permissionStore)
  }

  // 初始化Toast处理器
  if (config.toastHandlers) {
    const { setToastHandlers } = require('./clipboard')
    setToastHandlers(config.toastHandlers.success, config.toastHandlers.error)
  }

  isInitialized = true
  console.log(`Utils library v${VERSION} initialized`)
}

/**
 * 检查是否已初始化
 * @returns {boolean} 是否已初始化
 */
export function isUtilsInitialized(): boolean {
  return isInitialized
}

/**
 * 重置初始化状态（主要用于测试）
 */
export function resetUtils(): void {
  isInitialized = false
}

// 工具函数库信息
export const UtilsInfo = {
  version: VERSION,
  modules: [
    'auth',
    'clipboard', 
    'createUniqueString',
    'i18n',
    'openWindow',
    'permission',
    'scrollTo',
    'validate',
    'time',
    'url',
    'object',
    'string',
    'async',
    'dom'
  ],
  description: 'Vue 3 Admin UI Utils Library',
  compatibility: {
    vue: '3.x',
    typescript: '>=4.0.0',
    node: '>=16.0.0'
  }
}

/**
 * 打印工具库信息
 */
export function printUtilsInfo(): void {
  console.group('🛠️ Vue 3 Admin Utils Library')
  console.log(`Version: ${UtilsInfo.version}`)
  console.log(`Modules: ${UtilsInfo.modules.length}`)
  console.log('Available modules:', UtilsInfo.modules)
  console.log('Compatibility:', UtilsInfo.compatibility)
  console.groupEnd()
}

// 在开发模式下自动打印信息
if (process.env.NODE_ENV === 'development') {
  printUtilsInfo()
}
