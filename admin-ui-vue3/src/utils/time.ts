/**
 * 时间处理工具函数
 * Time utilities
 */
import type { TimeFormatTemplate, TimeInput } from './types'

/**
 * 格式化时间
 * @param {TimeInput} time - 时间输入
 * @param {string} cFormat - 格式模板
 * @returns {string | null} 格式化后的时间字符串
 */
export function parseTime(time: TimeInput, cFormat?: TimeFormatTemplate): string | null {
  if (arguments.length === 0) {
    return null
  }
  
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date: Date
  
  if (typeof time === 'object' && time instanceof Date) {
    date = time
  } else {
    let timestamp = time
    if (typeof timestamp === 'string') {
      timestamp = parseInt(timestamp, 10)
    }
    
    // 如果是10位时间戳，转换为13位
    if (String(timestamp).length === 10) {
      timestamp = timestamp * 1000
    }
    
    date = new Date(timestamp)
  }
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return null
  }
  
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  
  const timeStr = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key as keyof typeof formatObj]
    
    // 星期几的处理
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    
    // 补零处理
    if (result.length > 0 && value < 10) {
      return '0' + value
    }
    
    return String(value || 0)
  })
  
  return timeStr
}

/**
 * 相对时间格式化（多久前）
 * @param {TimeInput} time - 时间输入
 * @param {string} option - 可选的格式化选项
 * @returns {string} 相对时间字符串
 */
export function formatTime(time: TimeInput, option?: TimeFormatTemplate): string {
  let timestamp: number
  
  if (typeof time === 'string') {
    timestamp = parseInt(time, 10) * 1000
  } else if (typeof time === 'number') {
    timestamp = time * 1000
  } else {
    timestamp = time.getTime()
  }
  
  const d = new Date(timestamp)
  const now = Date.now()
  const diff = (now - timestamp) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // 小于1小时
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  
  if (option) {
    return parseTime(timestamp, option) || ''
  } else {
    return (
      (d.getMonth() + 1) +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * 获取时间范围
 * @param {'start' | 'end'} type - 时间类型
 * @returns {number} 时间戳
 */
export function getTime(type: 'start' | 'end'): number {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString()).getTime()
  }
}

/**
 * 格式化日期为ISO字符串
 * @param {TimeInput} time - 时间输入
 * @returns {string} ISO格式字符串
 */
export function toISOString(time: TimeInput): string {
  const date = new Date(time)
  return date.toISOString()
}

/**
 * 格式化日期为本地字符串
 * @param {TimeInput} time - 时间输入
 * @param {string} locale - 地区代码
 * @param {Intl.DateTimeFormatOptions} options - 格式化选项
 * @returns {string} 本地化格式字符串
 */
export function toLocaleString(
  time: TimeInput, 
  locale: string = 'zh-CN',
  options?: Intl.DateTimeFormatOptions
): string {
  const date = new Date(time)
  return date.toLocaleString(locale, options)
}

/**
 * 计算两个日期之间的差值
 * @param {TimeInput} startTime - 开始时间
 * @param {TimeInput} endTime - 结束时间
 * @returns {object} 时间差对象
 */
export function timeDiff(startTime: TimeInput, endTime: TimeInput): {
  days: number
  hours: number
  minutes: number
  seconds: number
  totalSeconds: number
} {
  const start = new Date(startTime).getTime()
  const end = new Date(endTime).getTime()
  const diff = Math.abs(end - start) / 1000
  
  const days = Math.floor(diff / (24 * 3600))
  const hours = Math.floor((diff % (24 * 3600)) / 3600)
  const minutes = Math.floor((diff % 3600) / 60)
  const seconds = Math.floor(diff % 60)
  
  return {
    days,
    hours,
    minutes,
    seconds,
    totalSeconds: diff
  }
}

/**
 * 添加时间
 * @param {TimeInput} time - 基础时间
 * @param {number} amount - 添加的数量
 * @param {'days' | 'hours' | 'minutes' | 'seconds'} unit - 时间单位
 * @returns {Date} 新的日期对象
 */
export function addTime(
  time: TimeInput, 
  amount: number, 
  unit: 'days' | 'hours' | 'minutes' | 'seconds'
): Date {
  const date = new Date(time)
  
  switch (unit) {
    case 'days':
      date.setDate(date.getDate() + amount)
      break
    case 'hours':
      date.setHours(date.getHours() + amount)
      break
    case 'minutes':
      date.setMinutes(date.getMinutes() + amount)
      break
    case 'seconds':
      date.setSeconds(date.getSeconds() + amount)
      break
  }
  
  return date
}

/**
 * 获取时间的开始和结束
 * @param {TimeInput} time - 时间输入
 * @param {'day' | 'week' | 'month' | 'year'} unit - 时间单位
 * @returns {object} 开始和结束时间
 */
export function getTimeRange(
  time: TimeInput, 
  unit: 'day' | 'week' | 'month' | 'year'
): { start: Date; end: Date } {
  const date = new Date(time)
  let start: Date
  let end: Date
  
  switch (unit) {
    case 'day':
      start = new Date(date.getFullYear(), date.getMonth(), date.getDate())
      end = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1)
      break
    case 'week':
      const dayOfWeek = date.getDay()
      start = new Date(date.getFullYear(), date.getMonth(), date.getDate() - dayOfWeek)
      end = new Date(date.getFullYear(), date.getMonth(), date.getDate() - dayOfWeek + 7)
      break
    case 'month':
      start = new Date(date.getFullYear(), date.getMonth(), 1)
      end = new Date(date.getFullYear(), date.getMonth() + 1, 1)
      break
    case 'year':
      start = new Date(date.getFullYear(), 0, 1)
      end = new Date(date.getFullYear() + 1, 0, 1)
      break
  }
  
  return { start, end }
}

/**
 * 检查是否为今天
 * @param {TimeInput} time - 时间输入
 * @returns {boolean} 是否为今天
 */
export function isToday(time: TimeInput): boolean {
  const date = new Date(time)
  const today = new Date()
  
  return date.getDate() === today.getDate() &&
         date.getMonth() === today.getMonth() &&
         date.getFullYear() === today.getFullYear()
}

/**
 * 检查是否为有效日期
 * @param {any} time - 任意输入
 * @returns {boolean} 是否为有效日期
 */
export function isValidDate(time: any): boolean {
  const date = new Date(time)
  return !isNaN(date.getTime())
}

/**
 * 获取当前时间戳
 * @returns {number} 当前时间戳（毫秒）
 */
export function now(): number {
  return Date.now()
}

/**
 * 获取当前时间戳（秒）
 * @returns {number} 当前时间戳（秒）
 */
export function nowInSeconds(): number {
  return Math.floor(Date.now() / 1000)
}
