/**
 * 创建唯一字符串工具函数
 * Create unique string utility
 */

/**
 * 创建唯一字符串
 * 基于时间戳和随机数生成32进制的唯一字符串
 * @returns {string} 唯一字符串
 */
export default function createUniqueString(): string {
  const timestamp = Date.now().toString()
  const randomNum = Math.floor((1 + Math.random()) * 65536).toString()
  return (+(randomNum + timestamp)).toString(32)
}

/**
 * 创建带前缀的唯一字符串
 * @param {string} prefix - 前缀
 * @returns {string} 带前缀的唯一字符串
 */
export function createUniqueStringWithPrefix(prefix: string = ''): string {
  const uniqueString = createUniqueString()
  return prefix ? `${prefix}_${uniqueString}` : uniqueString
}

/**
 * 创建UUID格式的唯一字符串
 * @returns {string} UUID格式字符串
 */
export function createUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 创建简短的唯一ID
 * @param {number} length - ID长度，默认8位
 * @returns {string} 简短唯一ID
 */
export function createShortId(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}
