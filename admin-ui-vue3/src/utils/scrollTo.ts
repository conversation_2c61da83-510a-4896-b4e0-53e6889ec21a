/**
 * 滚动动画工具函数
 * Scroll animation utilities
 */
import type { ScrollToOptions } from './types'

// 缓动函数 - 二次缓入缓出
const easeInOutQuad = (t: number, b: number, c: number, d: number): number => {
  t /= d / 2
  if (t < 1) {
    return (c / 2) * t * t + b
  }
  t--
  return (-c / 2) * (t * (t - 2) - 1) + b
}

// 获取requestAnimationFrame函数，兼容旧浏览器
const getRequestAnimationFrame = (): ((callback: FrameRequestCallback) => number) => {
  return (
    window.requestAnimationFrame ||
    (window as any).webkitRequestAnimationFrame ||
    (window as any).mozRequestAnimationFrame ||
    function (callback: FrameRequestCallback) {
      return window.setTimeout(callback, 1000 / 60)
    }
  )
}

// 设置滚动位置，兼容不同浏览器
function setScrollTop(amount: number): void {
  document.documentElement.scrollTop = amount
  document.body.parentNode && ((document.body.parentNode as Element).scrollTop = amount)
  document.body.scrollTop = amount
}

// 获取当前滚动位置
function getScrollTop(): number {
  return (
    document.documentElement.scrollTop ||
    (document.body.parentNode as Element)?.scrollTop ||
    document.body.scrollTop ||
    0
  )
}

/**
 * 平滑滚动到指定位置
 * @param {number} to - 目标滚动位置
 * @param {number} duration - 动画持续时间（毫秒），默认500
 * @param {Function} callback - 动画完成回调函数
 */
export function scrollTo(to: number, duration: number = 500, callback?: () => void): void {
  const start = getScrollTop()
  const change = to - start
  const increment = 20
  let currentTime = 0
  const requestAnimFrame = getRequestAnimationFrame()

  const animateScroll = (): void => {
    // 增加时间
    currentTime += increment
    // 使用二次缓入缓出缓动函数计算值
    const val = easeInOutQuad(currentTime, start, change, duration)
    // 移动滚动条
    setScrollTop(val)
    // 如果动画未完成，继续执行
    if (currentTime < duration) {
      requestAnimFrame(animateScroll)
    } else {
      // 动画完成，执行回调
      if (callback && typeof callback === 'function') {
        callback()
      }
    }
  }

  animateScroll()
}

/**
 * 使用选项对象的滚动函数
 * @param {ScrollToOptions} options - 滚动选项
 */
export function scrollToWithOptions(options: ScrollToOptions): void {
  const { to, duration = 500, callback } = options
  scrollTo(to, duration, callback)
}

/**
 * 滚动到页面顶部
 * @param {number} duration - 动画持续时间，默认500
 * @param {Function} callback - 动画完成回调
 */
export function scrollToTop(duration: number = 500, callback?: () => void): void {
  scrollTo(0, duration, callback)
}

/**
 * 滚动到页面底部
 * @param {number} duration - 动画持续时间，默认500
 * @param {Function} callback - 动画完成回调
 */
export function scrollToBottom(duration: number = 500, callback?: () => void): void {
  const documentHeight = Math.max(
    document.body.scrollHeight,
    document.body.offsetHeight,
    document.documentElement.clientHeight,
    document.documentElement.scrollHeight,
    document.documentElement.offsetHeight
  )
  const windowHeight = window.innerHeight
  const scrollTarget = documentHeight - windowHeight
  scrollTo(scrollTarget, duration, callback)
}

/**
 * 滚动到指定元素
 * @param {string | Element} element - 元素选择器或元素对象
 * @param {number} duration - 动画持续时间，默认500
 * @param {number} offset - 偏移量，默认0
 * @param {Function} callback - 动画完成回调
 */
export function scrollToElement(
  element: string | Element,
  duration: number = 500,
  offset: number = 0,
  callback?: () => void
): void {
  const targetElement = typeof element === 'string' 
    ? document.querySelector(element) 
    : element

  if (!targetElement) {
    console.warn('ScrollTo: Element not found')
    return
  }

  const elementTop = targetElement.getBoundingClientRect().top + getScrollTop()
  const scrollTarget = elementTop + offset

  scrollTo(scrollTarget, duration, callback)
}

/**
 * 使用现代浏览器的平滑滚动
 * @param {number} to - 目标位置
 * @param {'smooth' | 'auto'} behavior - 滚动行为
 */
export function scrollToModern(to: number, behavior: 'smooth' | 'auto' = 'smooth'): void {
  window.scrollTo({
    top: to,
    behavior
  })
}

/**
 * 检查是否支持现代滚动API
 * @returns {boolean} 是否支持
 */
export function supportsModernScroll(): boolean {
  return 'scrollBehavior' in document.documentElement.style
}

/**
 * 智能滚动 - 优先使用现代API，降级到动画
 * @param {number} to - 目标位置
 * @param {number} duration - 动画持续时间（仅在降级时使用）
 * @param {Function} callback - 完成回调
 */
export function smartScrollTo(to: number, duration: number = 500, callback?: () => void): void {
  if (supportsModernScroll()) {
    scrollToModern(to)
    // 现代API没有完成回调，需要模拟
    if (callback) {
      setTimeout(callback, duration)
    }
  } else {
    scrollTo(to, duration, callback)
  }
}

// 默认导出
export default scrollTo
