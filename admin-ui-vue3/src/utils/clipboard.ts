/**
 * 剪贴板工具函数
 * Clipboard utilities
 */
import type { ClipboardOptions } from './types'

/**
 * 成功提示函数 - 适配Shadcn UI Toast
 * 注意：这个函数需要在实际使用时注入Toast实例
 */
let showSuccessToast: (() => void) | null = null
let showErrorToast: (() => void) | null = null

/**
 * 设置Toast提示函数
 * @param {Function} successFn - 成功提示函数
 * @param {Function} errorFn - 错误提示函数
 */
export function setToastHandlers(successFn: () => void, errorFn: () => void): void {
  showSuccessToast = successFn
  showErrorToast = errorFn
}

/**
 * 显示复制成功提示
 */
function clipboardSuccess(): void {
  if (showSuccessToast) {
    showSuccessToast()
  } else {
    console.log('Copy successfully')
  }
}

/**
 * 显示复制失败提示
 */
function clipboardError(): void {
  if (showErrorToast) {
    showErrorToast()
  } else {
    console.error('Copy failed')
  }
}

/**
 * 使用现代Clipboard API复制文本
 * @param {string} text - 要复制的文本
 * @returns {Promise<boolean>} 复制是否成功
 */
export async function copyToClipboardModern(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text)
    clipboardSuccess()
    return true
  } catch (err) {
    console.error('Modern clipboard copy failed:', err)
    clipboardError()
    return false
  }
}

/**
 * 使用传统方法复制文本（降级方案）
 * @param {string} text - 要复制的文本
 * @returns {boolean} 复制是否成功
 */
export function copyToClipboardFallback(text: string): boolean {
  try {
    // 创建临时文本域
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    
    // 选择并复制
    textArea.focus()
    textArea.select()
    const result = document.execCommand('copy')
    
    // 清理
    document.body.removeChild(textArea)
    
    if (result) {
      clipboardSuccess()
    } else {
      clipboardError()
    }
    
    return result
  } catch (err) {
    console.error('Fallback clipboard copy failed:', err)
    clipboardError()
    return false
  }
}

/**
 * 使用clipboard.js库的处理函数（兼容旧代码）
 * @param {string} text - 要复制的文本
 * @param {Event} event - 事件对象
 */
export function handleClipboardLegacy(text: string, event: Event): void {
  // 动态导入clipboard库
  import('clipboard').then(({ default: Clipboard }) => {
    const clipboard = new Clipboard(event.target as Element, {
      text: () => text
    })
    
    clipboard.on('success', () => {
      clipboardSuccess()
      clipboard.destroy()
    })
    
    clipboard.on('error', () => {
      clipboardError()
      clipboard.destroy()
    })
  }).catch(err => {
    console.error('Failed to load clipboard library:', err)
    // 降级到现代API或传统方法
    copyToClipboard(text)
  })
}

/**
 * 检查是否支持现代Clipboard API
 * @returns {boolean} 是否支持
 */
export function supportsModernClipboard(): boolean {
  return !!(navigator.clipboard && navigator.clipboard.writeText)
}

/**
 * 智能复制函数 - 优先使用现代API，降级到传统方法
 * @param {string} text - 要复制的文本
 * @returns {Promise<boolean>} 复制是否成功
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  if (supportsModernClipboard()) {
    return await copyToClipboardModern(text)
  } else {
    return copyToClipboardFallback(text)
  }
}

/**
 * 使用选项对象的复制函数
 * @param {ClipboardOptions} options - 复制选项
 * @returns {Promise<boolean>} 复制是否成功
 */
export async function copyWithOptions(options: ClipboardOptions): Promise<boolean> {
  const { text, onSuccess, onError } = options
  
  try {
    const success = await copyToClipboard(text)
    if (success && onSuccess) {
      onSuccess()
    } else if (!success && onError) {
      onError()
    }
    return success
  } catch (err) {
    console.error('Copy with options failed:', err)
    if (onError) {
      onError()
    }
    return false
  }
}

/**
 * 读取剪贴板内容
 * @returns {Promise<string>} 剪贴板文本内容
 */
export async function readFromClipboard(): Promise<string> {
  try {
    if (navigator.clipboard && navigator.clipboard.readText) {
      return await navigator.clipboard.readText()
    } else {
      throw new Error('Clipboard read not supported')
    }
  } catch (err) {
    console.error('Read clipboard failed:', err)
    return ''
  }
}

/**
 * 检查剪贴板权限
 * @returns {Promise<boolean>} 是否有权限
 */
export async function checkClipboardPermission(): Promise<boolean> {
  try {
    if (navigator.permissions) {
      const permission = await navigator.permissions.query({ name: 'clipboard-write' as PermissionName })
      return permission.state === 'granted'
    }
    return true // 假设有权限
  } catch (err) {
    console.error('Check clipboard permission failed:', err)
    return true // 假设有权限
  }
}

// 默认导出（兼容旧代码）
export default function handleClipboard(text: string, event: Event): void {
  if (supportsModernClipboard()) {
    copyToClipboard(text)
  } else {
    handleClipboardLegacy(text, event)
  }
}
