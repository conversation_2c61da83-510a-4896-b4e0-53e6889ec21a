/**
 * 工具函数通用类型定义
 * Utils Common Type Definitions
 */

// 时间相关类型
export interface TimeFormat {
  y: number
  m: number
  d: number
  h: number
  i: number
  s: number
  a: number
}

export type TimeFormatString = string
export type TimeInput = string | number | Date
export type TimeFormatTemplate = string

// URL和参数相关类型
export interface QueryObject {
  [key: string]: string
}

export interface ParamObject {
  [key: string]: string | number | boolean | undefined
}

// DOM操作相关类型
export interface WindowOptions {
  url: string
  title: string
  width: number
  height: number
}

export interface ScrollToOptions {
  to: number
  duration?: number
  callback?: () => void
}

// 验证相关类型
export type ValidationResult = boolean
export type ValidatorFunction = (value: string) => boolean

// 权限相关类型
export type Permission = string
export type PermissionArray = Permission[]

// 剪贴板相关类型
export interface ClipboardOptions {
  text: string
  event: Event
  onSuccess?: () => void
  onError?: () => void
}

// 认证相关类型
export interface AuthToken {
  value: string
  expires?: Date
}

// 防抖函数类型
export interface DebounceFunction<T extends (...args: any[]) => any> {
  (...args: Parameters<T>): void
  cancel: () => void
}

// 日期选择器类型
export interface DatePickerOption {
  text: string
  onClick: (picker: any) => void
}

export type DatePickerOptions = DatePickerOption[]

// 通用工具函数类型
export type DeepCloneFunction = <T>(source: T) => T
export type ObjectMergeFunction = <T, U>(target: T, source: U) => T & U
export type UniqueArrayFunction = <T>(arr: T[]) => T[]
export type CleanArrayFunction = <T>(arr: (T | null | undefined | false | '')[]) => T[]

// HTML处理类型
export type HtmlToTextFunction = (html: string) => string
export type ByteLengthFunction = (text: string) => number

// 外部链接检测类型
export type ExternalLinkChecker = (path: string) => boolean

// CSS类操作类型
export interface ToggleClassOptions {
  element: Element
  className: string
}

// 国际化相关类型
export interface I18nContext {
  $te: (key: string) => boolean
  $t: (key: string) => string
}

export type TitleGenerator = (title: string) => string
