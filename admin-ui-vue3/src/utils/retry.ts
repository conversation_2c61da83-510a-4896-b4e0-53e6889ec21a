import type { AxiosError, AxiosRequestConfig } from 'axios'

// 重试配置接口
export interface RetryConfig {
  retries?: number // 重试次数
  retryDelay?: number // 重试延迟（毫秒）
  retryCondition?: (error: AxiosError) => boolean // 重试条件
  exponentialBackoff?: boolean // 是否使用指数退避
  maxRetryDelay?: number // 最大重试延迟
  onRetry?: (retryCount: number, error: AxiosError) => void // 重试回调
  enabled?: boolean // 是否启用重试
}

// 扩展的请求配置接口
export interface RetryAxiosRequestConfig extends AxiosRequestConfig {
  retry?: RetryConfig
  __retryCount__?: number
}

// 默认重试配置
export const defaultRetryConfig: Required<Omit<RetryConfig, 'onRetry'>> = {
  retries: 3,
  retryDelay: 1000,
  retryCondition: (error: AxiosError) => {
    // 默认重试条件：网络错误或 5xx 服务器错误
    return !error.response || (error.response.status >= 500 && error.response.status <= 599)
  },
  exponentialBackoff: true,
  maxRetryDelay: 30000,
  enabled: true
}

// 计算重试延迟
export function calculateRetryDelay(
  retryCount: number,
  baseDelay: number,
  exponentialBackoff: boolean,
  maxDelay: number
): number {
  if (!exponentialBackoff) {
    return baseDelay
  }
  
  // 指数退避算法：delay = baseDelay * (2 ^ retryCount) + jitter
  const exponentialDelay = baseDelay * Math.pow(2, retryCount)
  
  // 添加随机抖动（jitter）避免惊群效应
  const jitter = Math.random() * 0.1 * exponentialDelay
  
  const delay = exponentialDelay + jitter
  
  return Math.min(delay, maxDelay)
}

// 延迟函数
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 检查是否应该重试
export function shouldRetry(error: AxiosError, config: RetryAxiosRequestConfig): boolean {
  const retryConfig = { ...defaultRetryConfig, ...config.retry }
  
  if (!retryConfig.enabled) {
    return false
  }
  
  const retryCount = config.__retryCount__ || 0
  
  // 检查重试次数
  if (retryCount >= retryConfig.retries) {
    return false
  }
  
  // 检查重试条件
  return retryConfig.retryCondition(error)
}

// 创建重试拦截器
export function createRetryInterceptor() {
  return {
    responseError: async (error: AxiosError) => {
      const config = error.config as RetryAxiosRequestConfig
      
      if (!config || !shouldRetry(error, config)) {
        return Promise.reject(error)
      }
      
      // 增加重试计数
      config.__retryCount__ = (config.__retryCount__ || 0) + 1
      
      const retryConfig = { ...defaultRetryConfig, ...config.retry }
      
      // 计算延迟
      const retryDelay = calculateRetryDelay(
        config.__retryCount__,
        retryConfig.retryDelay,
        retryConfig.exponentialBackoff,
        retryConfig.maxRetryDelay
      )
      
      // 调用重试回调
      if (retryConfig.onRetry) {
        retryConfig.onRetry(config.__retryCount__, error)
      }
      
      console.warn(
        `请求失败，${retryDelay}ms 后进行第 ${config.__retryCount__} 次重试`,
        {
          url: config.url,
          method: config.method,
          error: error.message,
          retryCount: config.__retryCount__,
          maxRetries: retryConfig.retries
        }
      )
      
      // 等待延迟后重试
      await delay(retryDelay)
      
      // 重新发起请求
      const axios = error.config?.adapter ? 
        // 如果有 adapter，使用原始的 axios 实例
        (error as any).request?.axios || 
        // 否则从全局导入
        (await import('axios')).default
        : (await import('axios')).default
      
      return axios.request(config)
    }
  }
}

// 重试策略预设
export const RetryPresets = {
  // 默认策略
  DEFAULT: {
    enabled: true,
    retries: 3,
    retryDelay: 1000,
    exponentialBackoff: true,
    maxRetryDelay: 30000,
    retryCondition: (error: AxiosError) => {
      return !error.response || (error.response.status >= 500 && error.response.status <= 599)
    }
  },
  
  // 快速重试（适用于轻量级请求）
  FAST: {
    enabled: true,
    retries: 2,
    retryDelay: 500,
    exponentialBackoff: false,
    maxRetryDelay: 1000,
    retryCondition: (error: AxiosError) => {
      return !error.response || error.response.status >= 500
    }
  },
  
  // 慢速重试（适用于重要请求）
  SLOW: {
    enabled: true,
    retries: 5,
    retryDelay: 2000,
    exponentialBackoff: true,
    maxRetryDelay: 60000,
    retryCondition: (error: AxiosError) => {
      return !error.response || 
             error.response.status >= 500 || 
             error.response.status === 429 // Too Many Requests
    }
  },
  
  // 网络错误重试
  NETWORK_ONLY: {
    enabled: true,
    retries: 3,
    retryDelay: 1000,
    exponentialBackoff: true,
    maxRetryDelay: 10000,
    retryCondition: (error: AxiosError) => {
      return !error.response // 只重试网络错误
    }
  },
  
  // 禁用重试
  DISABLED: {
    enabled: false
  }
} as const

// 常见错误类型判断
export const ErrorConditions = {
  // 网络错误
  isNetworkError: (error: AxiosError): boolean => {
    return !error.response
  },
  
  // 服务器错误
  isServerError: (error: AxiosError): boolean => {
    return error.response?.status ? error.response.status >= 500 : false
  },
  
  // 客户端错误
  isClientError: (error: AxiosError): boolean => {
    return error.response?.status ? 
      error.response.status >= 400 && error.response.status < 500 : false
  },
  
  // 超时错误
  isTimeoutError: (error: AxiosError): boolean => {
    return error.code === 'ECONNABORTED' || error.message.includes('timeout')
  },
  
  // 限流错误
  isRateLimitError: (error: AxiosError): boolean => {
    return error.response?.status === 429
  },
  
  // 认证错误
  isAuthError: (error: AxiosError): boolean => {
    return error.response?.status === 401 || error.response?.status === 403
  },
  
  // 资源不存在
  isNotFoundError: (error: AxiosError): boolean => {
    return error.response?.status === 404
  }
}

// 创建自定义重试条件
export function createRetryCondition(
  conditions: ((error: AxiosError) => boolean)[]
): (error: AxiosError) => boolean {
  return (error: AxiosError) => {
    return conditions.some(condition => condition(error))
  }
}

// 重试统计
export interface RetryStats {
  totalRequests: number
  retriedRequests: number
  successfulRetries: number
  failedRetries: number
  averageRetries: number
}

class RetryStatsCollector {
  private stats: RetryStats = {
    totalRequests: 0,
    retriedRequests: 0,
    successfulRetries: 0,
    failedRetries: 0,
    averageRetries: 0
  }
  
  private retryCountSum = 0
  
  recordRequest(): void {
    this.stats.totalRequests++
  }
  
  recordRetry(success: boolean, retryCount: number): void {
    this.stats.retriedRequests++
    this.retryCountSum += retryCount
    
    if (success) {
      this.stats.successfulRetries++
    } else {
      this.stats.failedRetries++
    }
    
    this.stats.averageRetries = this.retryCountSum / this.stats.retriedRequests
  }
  
  getStats(): RetryStats {
    return { ...this.stats }
  }
  
  reset(): void {
    this.stats = {
      totalRequests: 0,
      retriedRequests: 0,
      successfulRetries: 0,
      failedRetries: 0,
      averageRetries: 0
    }
    this.retryCountSum = 0
  }
}

export const retryStats = new RetryStatsCollector()

// 带统计的重试拦截器
export function createRetryInterceptorWithStats() {
  return {
    request: (config: RetryAxiosRequestConfig) => {
      retryStats.recordRequest()
      return config
    },
    
    responseError: async (error: AxiosError) => {
      const config = error.config as RetryAxiosRequestConfig
      
      if (!config || !shouldRetry(error, config)) {
        if (config?.__retryCount__) {
          retryStats.recordRetry(false, config.__retryCount__)
        }
        return Promise.reject(error)
      }
      
      // 增加重试计数
      config.__retryCount__ = (config.__retryCount__ || 0) + 1
      
      const retryConfig = { ...defaultRetryConfig, ...config.retry }
      
      // 计算延迟
      const retryDelay = calculateRetryDelay(
        config.__retryCount__,
        retryConfig.retryDelay,
        retryConfig.exponentialBackoff,
        retryConfig.maxRetryDelay
      )
      
      // 调用重试回调
      if (retryConfig.onRetry) {
        retryConfig.onRetry(config.__retryCount__, error)
      }
      
      console.warn(
        `请求失败，${retryDelay}ms 后进行第 ${config.__retryCount__} 次重试`,
        {
          url: config.url,
          method: config.method,
          error: error.message,
          retryCount: config.__retryCount__,
          maxRetries: retryConfig.retries
        }
      )
      
      // 等待延迟后重试
      await delay(retryDelay)
      
      try {
        // 重新发起请求
        const axios = (await import('axios')).default
        const response = await axios.request(config)
        
        // 记录成功重试
        retryStats.recordRetry(true, config.__retryCount__)
        
        return response
      } catch (retryError) {
        // 如果是最后一次重试失败，记录失败
        if (config.__retryCount__ >= retryConfig.retries) {
          retryStats.recordRetry(false, config.__retryCount__)
        }
        throw retryError
      }
    }
  }
}

export default {
  defaultRetryConfig,
  calculateRetryDelay,
  delay,
  shouldRetry,
  createRetryInterceptor,
  createRetryInterceptorWithStats,
  RetryPresets,
  ErrorConditions,
  createRetryCondition,
  retryStats
}
