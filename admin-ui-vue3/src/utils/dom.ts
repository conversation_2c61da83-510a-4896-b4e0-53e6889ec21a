/**
 * DOM操作工具函数
 * DOM utilities
 */
import type { ToggleClassOptions } from './types'

/**
 * 检查元素是否有指定class
 * @param {Element} elm - DOM元素
 * @param {string} cls - class名称
 * @returns {boolean} 是否包含class
 */
export function hasClass(elm: Element, cls: string): boolean {
  return elm.classList.contains(cls)
}

/**
 * 为元素添加class
 * @param {Element} elm - DOM元素
 * @param {string} cls - class名称
 */
export function addClass(elm: Element, cls: string): void {
  elm.classList.add(cls)
}

/**
 * 移除元素的class
 * @param {Element} elm - DOM元素
 * @param {string} cls - class名称
 */
export function removeClass(elm: Element, cls: string): void {
  elm.classList.remove(cls)
}

/**
 * 切换元素的class
 * @param {Element} element - DOM元素
 * @param {string} className - class名称
 */
export function toggleClass(element: Element, className: string): void {
  element.classList.toggle(className)
}

/**
 * 使用选项切换class
 * @param {ToggleClassOptions} options - 切换选项
 */
export function toggleClassWithOptions(options: ToggleClassOptions): void {
  const { element, className } = options
  toggleClass(element, className)
}

/**
 * 获取元素的样式
 * @param {Element} element - DOM元素
 * @param {string} styleName - 样式名称
 * @returns {string} 样式值
 */
export function getStyle(element: Element, styleName: string): string {
  if (!element || !styleName) return ''
  
  styleName = styleName.replace(/([A-Z])/g, '-$1').toLowerCase()
  
  const computedStyle = window.getComputedStyle(element as HTMLElement)
  return computedStyle.getPropertyValue(styleName) || ''
}

/**
 * 设置元素的样式
 * @param {HTMLElement} element - DOM元素
 * @param {string} styleName - 样式名称
 * @param {string} value - 样式值
 */
export function setStyle(element: HTMLElement, styleName: string, value: string): void {
  if (!element || !styleName) return
  
  if (typeof styleName === 'object') {
    Object.keys(styleName).forEach(prop => {
      setStyle(element, prop, (styleName as any)[prop])
    })
  } else {
    element.style.setProperty(styleName, value)
  }
}

/**
 * 批量设置样式
 * @param {HTMLElement} element - DOM元素
 * @param {Record<string, string>} styles - 样式对象
 */
export function setStyles(element: HTMLElement, styles: Record<string, string>): void {
  Object.keys(styles).forEach(styleName => {
    setStyle(element, styleName, styles[styleName])
  })
}

/**
 * 获取元素的位置信息
 * @param {Element} element - DOM元素
 * @returns {DOMRect} 位置信息
 */
export function getRect(element: Element): DOMRect {
  return element.getBoundingClientRect()
}

/**
 * 获取元素相对于文档的偏移
 * @param {Element} element - DOM元素
 * @returns {object} 偏移信息
 */
export function getOffset(element: Element): { top: number; left: number } {
  const rect = element.getBoundingClientRect()
  return {
    top: rect.top + window.pageYOffset,
    left: rect.left + window.pageXOffset
  }
}

/**
 * 获取元素的尺寸
 * @param {Element} element - DOM元素
 * @returns {object} 尺寸信息
 */
export function getSize(element: Element): { width: number; height: number } {
  const rect = element.getBoundingClientRect()
  return {
    width: rect.width,
    height: rect.height
  }
}

/**
 * 检查元素是否在视口中
 * @param {Element} element - DOM元素
 * @returns {boolean} 是否在视口中
 */
export function isInViewport(element: Element): boolean {
  const rect = element.getBoundingClientRect()
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  )
}

/**
 * 获取元素到视口顶部的距离
 * @param {Element} element - DOM元素
 * @returns {number} 距离
 */
export function getDistanceToViewportTop(element: Element): number {
  return element.getBoundingClientRect().top
}

/**
 * 查找最近的父元素
 * @param {Element} element - 开始元素
 * @param {string} selector - 选择器
 * @returns {Element | null} 找到的元素
 */
export function closest(element: Element, selector: string): Element | null {
  return element.closest(selector)
}

/**
 * 查找所有匹配的子元素
 * @param {Element} element - 父元素
 * @param {string} selector - 选择器
 * @returns {Element[]} 匹配的元素数组
 */
export function findAll(element: Element, selector: string): Element[] {
  return Array.from(element.querySelectorAll(selector))
}

/**
 * 查找第一个匹配的子元素
 * @param {Element} element - 父元素
 * @param {string} selector - 选择器
 * @returns {Element | null} 找到的元素
 */
export function findOne(element: Element, selector: string): Element | null {
  return element.querySelector(selector)
}

/**
 * 创建DOM元素
 * @param {string} tagName - 标签名
 * @param {Record<string, string>} attributes - 属性对象
 * @param {string} textContent - 文本内容
 * @returns {HTMLElement} 创建的元素
 */
export function createElement(
  tagName: string,
  attributes?: Record<string, string>,
  textContent?: string
): HTMLElement {
  const element = document.createElement(tagName)
  
  if (attributes) {
    Object.keys(attributes).forEach(key => {
      element.setAttribute(key, attributes[key])
    })
  }
  
  if (textContent) {
    element.textContent = textContent
  }
  
  return element
}

/**
 * 移除DOM元素
 * @param {Element} element - 要移除的元素
 */
export function removeElement(element: Element): void {
  if (element && element.parentNode) {
    element.parentNode.removeChild(element)
  }
}

/**
 * 在指定元素后插入新元素
 * @param {Element} newElement - 新元素
 * @param {Element} targetElement - 目标元素
 */
export function insertAfter(newElement: Element, targetElement: Element): void {
  const parent = targetElement.parentNode
  if (parent) {
    if (targetElement.nextSibling) {
      parent.insertBefore(newElement, targetElement.nextSibling)
    } else {
      parent.appendChild(newElement)
    }
  }
}

/**
 * 在指定元素前插入新元素
 * @param {Element} newElement - 新元素
 * @param {Element} targetElement - 目标元素
 */
export function insertBefore(newElement: Element, targetElement: Element): void {
  const parent = targetElement.parentNode
  if (parent) {
    parent.insertBefore(newElement, targetElement)
  }
}

/**
 * 获取元素的文本内容
 * @param {Element} element - DOM元素
 * @returns {string} 文本内容
 */
export function getText(element: Element): string {
  return element.textContent || (element as HTMLElement).innerText || ''
}

/**
 * 设置元素的文本内容
 * @param {Element} element - DOM元素
 * @param {string} text - 文本内容
 */
export function setText(element: Element, text: string): void {
  element.textContent = text
}

/**
 * 获取元素的HTML内容
 * @param {Element} element - DOM元素
 * @returns {string} HTML内容
 */
export function getHtml(element: Element): string {
  return element.innerHTML
}

/**
 * 设置元素的HTML内容
 * @param {Element} element - DOM元素
 * @param {string} html - HTML内容
 */
export function setHtml(element: Element, html: string): void {
  element.innerHTML = html
}

/**
 * 获取元素的属性值
 * @param {Element} element - DOM元素
 * @param {string} name - 属性名
 * @returns {string | null} 属性值
 */
export function getAttribute(element: Element, name: string): string | null {
  return element.getAttribute(name)
}

/**
 * 设置元素的属性
 * @param {Element} element - DOM元素
 * @param {string} name - 属性名
 * @param {string} value - 属性值
 */
export function setAttribute(element: Element, name: string, value: string): void {
  element.setAttribute(name, value)
}

/**
 * 移除元素的属性
 * @param {Element} element - DOM元素
 * @param {string} name - 属性名
 */
export function removeAttribute(element: Element, name: string): void {
  element.removeAttribute(name)
}

/**
 * 检查元素是否有指定属性
 * @param {Element} element - DOM元素
 * @param {string} name - 属性名
 * @returns {boolean} 是否有属性
 */
export function hasAttribute(element: Element, name: string): boolean {
  return element.hasAttribute(name)
}

/**
 * 获取表单数据
 * @param {HTMLFormElement} form - 表单元素
 * @returns {FormData} 表单数据
 */
export function getFormData(form: HTMLFormElement): FormData {
  return new FormData(form)
}

/**
 * 将表单数据转换为对象
 * @param {HTMLFormElement} form - 表单元素
 * @returns {Record<string, any>} 数据对象
 */
export function formToObject(form: HTMLFormElement): Record<string, any> {
  const formData = new FormData(form)
  const obj: Record<string, any> = {}
  
  formData.forEach((value, key) => {
    obj[key] = value
  })
  
  return obj
}

/**
 * 监听DOM元素的大小变化
 * @param {Element} element - DOM元素
 * @param {function} callback - 回调函数
 * @returns {ResizeObserver | null} ResizeObserver实例
 */
export function observeResize(
  element: Element,
  callback: (entry: ResizeObserverEntry) => void
): ResizeObserver | null {
  if (!window.ResizeObserver) {
    console.warn('ResizeObserver is not supported')
    return null
  }
  
  const observer = new ResizeObserver(entries => {
    callback(entries[0])
  })
  
  observer.observe(element)
  return observer
}

/**
 * 停止监听元素大小变化
 * @param {ResizeObserver} observer - ResizeObserver实例
 */
export function unobserveResize(observer: ResizeObserver): void {
  if (observer) {
    observer.disconnect()
  }
}

/**
 * 获取视口尺寸
 * @returns {object} 视口尺寸
 */
export function getViewportSize(): { width: number; height: number } {
  return {
    width: window.innerWidth || document.documentElement.clientWidth,
    height: window.innerHeight || document.documentElement.clientHeight
  }
}

/**
 * 获取页面滚动位置
 * @returns {object} 滚动位置
 */
export function getScrollPosition(): { x: number; y: number } {
  return {
    x: window.pageXOffset || document.documentElement.scrollLeft,
    y: window.pageYOffset || document.documentElement.scrollTop
  }
}

/**
 * 检查是否支持触摸
 * @returns {boolean} 是否支持触摸
 */
export function isTouchDevice(): boolean {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

/**
 * 检查是否为移动设备
 * @returns {boolean} 是否为移动设备
 */
export function isMobileDevice(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  )
}
