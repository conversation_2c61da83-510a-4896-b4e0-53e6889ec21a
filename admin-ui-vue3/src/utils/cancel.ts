import type { AxiosRequestConfig, Canceler } from 'axios'
import axios from 'axios'

// 取消配置接口
export interface CancelConfig {
  enabled?: boolean // 是否启用取消
  timeout?: number // 自动取消超时时间（毫秒）
  cancelDuplicates?: boolean // 是否取消重复请求
  key?: string // 自定义取消key
  onCancel?: (reason: string) => void // 取消回调
}

// 扩展的请求配置接口
export interface CancelAxiosRequestConfig extends AxiosRequestConfig {
  cancel?: CancelConfig
  __cancelKey__?: string
  __cancelToken__?: any
}

// 请求取消器存储
const cancelTokenMap = new Map<string, Canceler>()
const pendingRequests = new Map<string, AbortController>()

// 生成请求key
export function generateRequestKey(config: AxiosRequestConfig, customKey?: string): string {
  if (customKey) {
    return customKey
  }
  
  const { method = 'GET', url = '', params = {}, data = {} } = config
  const baseKey = `${method.toUpperCase()}:${url}`
  
  // 对参数进行排序以确保一致性
  const sortedParams = Object.keys(params || {})
    .sort()
    .reduce((result, key) => {
      result[key] = params[key]
      return result
    }, {} as Record<string, any>)
  
  const paramsStr = JSON.stringify(sortedParams)
  const dataStr = method.toUpperCase() === 'GET' ? '' : JSON.stringify(data || {})
  
  return `${baseKey}:${btoa(unescape(encodeURIComponent(paramsStr + dataStr)))}`
}

// 取消单个请求
export function cancelRequest(key: string, reason = '请求被取消'): boolean {
  const canceler = cancelTokenMap.get(key)
  if (canceler) {
    canceler(reason)
    cancelTokenMap.delete(key)
    return true
  }
  
  const controller = pendingRequests.get(key)
  if (controller) {
    controller.abort()
    pendingRequests.delete(key)
    return true
  }
  
  return false
}

// 取消所有请求
export function cancelAllRequests(reason = '批量取消请求'): number {
  let canceledCount = 0
  
  // 取消使用 CancelToken 的请求
  for (const [key, canceler] of cancelTokenMap.entries()) {
    canceler(reason)
    canceledCount++
  }
  cancelTokenMap.clear()
  
  // 取消使用 AbortController 的请求
  for (const [key, controller] of pendingRequests.entries()) {
    controller.abort()
    canceledCount++
  }
  pendingRequests.clear()
  
  return canceledCount
}

// 取消匹配的请求
export function cancelRequestsByPattern(
  pattern: string | RegExp, 
  reason = '模式匹配取消'
): number {
  let canceledCount = 0
  const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern
  
  // 取消匹配的 CancelToken 请求
  for (const [key, canceler] of cancelTokenMap.entries()) {
    if (regex.test(key)) {
      canceler(reason)
      cancelTokenMap.delete(key)
      canceledCount++
    }
  }
  
  // 取消匹配的 AbortController 请求
  for (const [key, controller] of pendingRequests.entries()) {
    if (regex.test(key)) {
      controller.abort()
      pendingRequests.delete(key)
      canceledCount++
    }
  }
  
  return canceledCount
}

// 检查请求是否已被取消
export function isRequestCanceled(key: string): boolean {
  return !cancelTokenMap.has(key) && !pendingRequests.has(key)
}

// 获取正在进行的请求数量
export function getPendingRequestCount(): number {
  return cancelTokenMap.size + pendingRequests.size
}

// 获取正在进行的请求列表
export function getPendingRequests(): string[] {
  return [...cancelTokenMap.keys(), ...pendingRequests.keys()]
}

// 创建取消拦截器
export function createCancelInterceptor() {
  return {
    request: (config: CancelAxiosRequestConfig) => {
      const cancelConfig = config.cancel
      
      if (!cancelConfig?.enabled) {
        return config
      }
      
      const requestKey = generateRequestKey(config, cancelConfig.key)
      config.__cancelKey__ = requestKey
      
      // 如果启用了取消重复请求，先取消之前的请求
      if (cancelConfig.cancelDuplicates) {
        cancelRequest(requestKey, '重复请求被取消')
      }
      
      // 创建取消令牌
      const source = axios.CancelToken.source()
      config.cancelToken = source.token
      config.__cancelToken__ = source
      
      // 存储取消器
      cancelTokenMap.set(requestKey, source.cancel)
      
      // 设置超时自动取消
      if (cancelConfig.timeout) {
        setTimeout(() => {
          if (cancelTokenMap.has(requestKey)) {
            const reason = `请求超时自动取消 (${cancelConfig.timeout}ms)`
            cancelRequest(requestKey, reason)
            if (cancelConfig.onCancel) {
              cancelConfig.onCancel(reason)
            }
          }
        }, cancelConfig.timeout)
      }
      
      return config
    },
    
    response: (response: any) => {
      const config = response.config as CancelAxiosRequestConfig
      const requestKey = config.__cancelKey__
      
      if (requestKey) {
        cancelTokenMap.delete(requestKey)
        pendingRequests.delete(requestKey)
      }
      
      return response
    },
    
    responseError: (error: any) => {
      const config = error.config as CancelAxiosRequestConfig
      const requestKey = config?.__cancelKey__
      
      if (requestKey) {
        cancelTokenMap.delete(requestKey)
        pendingRequests.delete(requestKey)
      }
      
      // 如果是取消错误，调用取消回调
      if (axios.isCancel(error) && config?.cancel?.onCancel) {
        config.cancel.onCancel(error.message || '请求被取消')
      }
      
      return Promise.reject(error)
    }
  }
}

// 请求组管理
export class RequestGroup {
  private groupKey: string
  private requests: Set<string> = new Set()
  
  constructor(groupKey: string) {
    this.groupKey = groupKey
  }
  
  // 添加请求到组
  addRequest(requestKey: string): void {
    this.requests.add(requestKey)
  }
  
  // 从组中移除请求
  removeRequest(requestKey: string): void {
    this.requests.delete(requestKey)
  }
  
  // 取消组内所有请求
  cancelAll(reason = `取消请求组: ${this.groupKey}`): number {
    let canceledCount = 0
    
    for (const requestKey of this.requests) {
      if (cancelRequest(requestKey, reason)) {
        canceledCount++
      }
    }
    
    this.requests.clear()
    return canceledCount
  }
  
  // 获取组内请求数量
  getRequestCount(): number {
    return this.requests.size
  }
  
  // 获取组内请求列表
  getRequests(): string[] {
    return Array.from(this.requests)
  }
  
  // 清空组
  clear(): void {
    this.requests.clear()
  }
}

// 全局请求组管理器
class RequestGroupManager {
  private groups = new Map<string, RequestGroup>()
  
  // 创建或获取请求组
  getGroup(groupKey: string): RequestGroup {
    if (!this.groups.has(groupKey)) {
      this.groups.set(groupKey, new RequestGroup(groupKey))
    }
    return this.groups.get(groupKey)!
  }
  
  // 删除请求组
  deleteGroup(groupKey: string): boolean {
    const group = this.groups.get(groupKey)
    if (group) {
      group.cancelAll()
      this.groups.delete(groupKey)
      return true
    }
    return false
  }
  
  // 取消所有组的请求
  cancelAllGroups(reason = '取消所有请求组'): number {
    let totalCanceled = 0
    
    for (const group of this.groups.values()) {
      totalCanceled += group.cancelAll(reason)
    }
    
    this.groups.clear()
    return totalCanceled
  }
  
  // 获取所有组
  getGroups(): Map<string, RequestGroup> {
    return new Map(this.groups)
  }
  
  // 获取组数量
  getGroupCount(): number {
    return this.groups.size
  }
}

export const requestGroupManager = new RequestGroupManager()

// 取消配置预设
export const CancelPresets = {
  // 默认配置
  DEFAULT: {
    enabled: true,
    cancelDuplicates: false
  },
  
  // 取消重复请求
  CANCEL_DUPLICATES: {
    enabled: true,
    cancelDuplicates: true
  },
  
  // 带超时的取消
  WITH_TIMEOUT: (timeout: number) => ({
    enabled: true,
    timeout,
    cancelDuplicates: false
  }),
  
  // 严格模式（取消重复 + 超时）
  STRICT: (timeout: number) => ({
    enabled: true,
    timeout,
    cancelDuplicates: true
  }),
  
  // 禁用取消
  DISABLED: {
    enabled: false
  }
} as const

// 取消统计
export interface CancelStats {
  totalCanceled: number
  duplicateCanceled: number
  timeoutCanceled: number
  manualCanceled: number
  activeCancelTokens: number
}

class CancelStatsCollector {
  private stats: CancelStats = {
    totalCanceled: 0,
    duplicateCanceled: 0,
    timeoutCanceled: 0,
    manualCanceled: 0,
    activeCancelTokens: 0
  }
  
  recordCancel(type: 'duplicate' | 'timeout' | 'manual'): void {
    this.stats.totalCanceled++
    
    switch (type) {
      case 'duplicate':
        this.stats.duplicateCanceled++
        break
      case 'timeout':
        this.stats.timeoutCanceled++
        break
      case 'manual':
        this.stats.manualCanceled++
        break
    }
  }
  
  updateActiveCancelTokens(): void {
    this.stats.activeCancelTokens = getPendingRequestCount()
  }
  
  getStats(): CancelStats {
    this.updateActiveCancelTokens()
    return { ...this.stats }
  }
  
  reset(): void {
    this.stats = {
      totalCanceled: 0,
      duplicateCanceled: 0,
      timeoutCanceled: 0,
      manualCanceled: 0,
      activeCancelTokens: 0
    }
  }
}

export const cancelStats = new CancelStatsCollector()

// 创建带统计的取消拦截器
export function createCancelInterceptorWithStats() {
  return {
    request: (config: CancelAxiosRequestConfig) => {
      const cancelConfig = config.cancel
      
      if (!cancelConfig?.enabled) {
        return config
      }
      
      const requestKey = generateRequestKey(config, cancelConfig.key)
      config.__cancelKey__ = requestKey
      
      // 如果启用了取消重复请求，先取消之前的请求
      if (cancelConfig.cancelDuplicates) {
        if (cancelRequest(requestKey, '重复请求被取消')) {
          cancelStats.recordCancel('duplicate')
        }
      }
      
      // 创建取消令牌
      const source = axios.CancelToken.source()
      config.cancelToken = source.token
      config.__cancelToken__ = source
      
      // 存储取消器
      cancelTokenMap.set(requestKey, source.cancel)
      
      // 设置超时自动取消
      if (cancelConfig.timeout) {
        setTimeout(() => {
          if (cancelTokenMap.has(requestKey)) {
            const reason = `请求超时自动取消 (${cancelConfig.timeout}ms)`
            cancelRequest(requestKey, reason)
            cancelStats.recordCancel('timeout')
            
            if (cancelConfig.onCancel) {
              cancelConfig.onCancel(reason)
            }
          }
        }, cancelConfig.timeout)
      }
      
      return config
    },
    
    response: (response: any) => {
      const config = response.config as CancelAxiosRequestConfig
      const requestKey = config.__cancelKey__
      
      if (requestKey) {
        cancelTokenMap.delete(requestKey)
        pendingRequests.delete(requestKey)
      }
      
      return response
    },
    
    responseError: (error: any) => {
      const config = error.config as CancelAxiosRequestConfig
      const requestKey = config?.__cancelKey__
      
      if (requestKey) {
        cancelTokenMap.delete(requestKey)
        pendingRequests.delete(requestKey)
      }
      
      // 如果是取消错误，调用取消回调并记录统计
      if (axios.isCancel(error)) {
        if (config?.cancel?.onCancel) {
          config.cancel.onCancel(error.message || '请求被取消')
        }
        
        // 根据取消原因记录统计
        const message = error.message || ''
        if (message.includes('重复请求')) {
          // 已在请求拦截器中记录
        } else if (message.includes('超时')) {
          // 已在超时处理中记录
        } else {
          cancelStats.recordCancel('manual')
        }
      }
      
      return Promise.reject(error)
    }
  }
}

export default {
  generateRequestKey,
  cancelRequest,
  cancelAllRequests,
  cancelRequestsByPattern,
  isRequestCanceled,
  getPendingRequestCount,
  getPendingRequests,
  createCancelInterceptor,
  createCancelInterceptorWithStats,
  RequestGroup,
  requestGroupManager,
  CancelPresets,
  cancelStats
}
