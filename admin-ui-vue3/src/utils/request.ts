import { ApiResponse, ErrorCode } from '@/api/types/index';
import { useToast } from '@/hooks/use-toast';
import axios, { AxiosError, AxiosResponse } from 'axios';
import { useRouter } from 'vue-router';

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API, // API 的 base_url
  timeout: 5000 // 请求超时时间
});

// Toast 实例
const { toast } = useToast();

// 获取 token 的函数（需要实现）
function getToken(): string | null {
  return localStorage.getItem('X-Dts-Admin-Token');
}

// 登出函数（需要实现）
function logout() {
  localStorage.removeItem('X-Dts-Admin-Token');
  const router = useRouter();
  router.push('/login');
}

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 在请求发送前添加 token
    const token = getToken();
    if (token) {
      // 自定义 token header 名称
      config.headers['X-Dts-Admin-Token'] = token;
    }
    return config;
  },
  (error: AxiosError) => {
    // 请求错误处理
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const res = response.data;

    if (res.errno === ErrorCode.UNAUTHORIZED) {
      // 系统未登录，重新登录
      toast({
        title: '错误',
        description: '系统未登录，请重新登录',
        variant: 'destructive',
      });
      
      logout();
      return Promise.reject(new Error('未登录'));
    } else if (res.errno === ErrorCode.INTERNAL_ERROR) {
      // 系统内部错误
      toast({
        title: '错误',
        description: '系统内部错误，请联系管理员维护',
        variant: 'destructive',
      });
      return Promise.reject(new Error('系统内部错误'));
    } else if (res.errno === ErrorCode.UNSUPPORTED) {
      // 请求业务目前未支持
      toast({
        title: '警告',
        description: '请求业务目前未支持',
        variant: 'destructive',
      });
      return Promise.reject(new Error('业务未支持'));
    } else if (res.errno === ErrorCode.DATA_EXPIRED) {
      // 更新数据已经失效
      toast({
        title: '警告',
        description: '更新数据已经失效，请刷新页面重新操作',
        variant: 'destructive',
      });
      return Promise.reject(new Error('数据已失效'));
    } else if (res.errno === ErrorCode.UPDATE_FAILED) {
      // 更新失败
      toast({
        title: '警告',
        description: '更新失败，请再尝试一次',
        variant: 'destructive',
      });
      return Promise.reject(new Error('更新失败'));
    } else if (res.errno === ErrorCode.NO_PERMISSION) {
      // 没有操作权限
      toast({
        title: '错误',
        description: '没有操作权限，请联系管理员授权',
        variant: 'destructive',
      });
      return Promise.reject(new Error('无权限'));
    } else if (res.errno !== ErrorCode.SUCCESS) {
      // 其他业务错误，留给具体页面处理
      return Promise.reject(response);
    } else {
      // 请求成功
      return response;
    }
  },
  (error: AxiosError) => {
    console.error('响应错误:', error);
    
    // 网络错误或连接超时
    toast({
      title: '网络错误',
      description: '登录连接超时（后台不能连接，请联系系统管理员）',
      variant: 'destructive',
      duration: 5000,
    });
    
    return Promise.reject(error);
  }
);

export default service;

// 导出常用的请求方法
export const request = {
  get<T = any>(url: string, params?: any): Promise<AxiosResponse<ApiResponse<T>>> {
    return service.get(url, { params });
  },
  
  post<T = any>(url: string, data?: any): Promise<AxiosResponse<ApiResponse<T>>> {
    return service.post(url, data);
  },
  
  put<T = any>(url: string, data?: any): Promise<AxiosResponse<ApiResponse<T>>> {
    return service.put(url, data);
  },
  
  delete<T = any>(url: string, params?: any): Promise<AxiosResponse<ApiResponse<T>>> {
    return service.delete(url, { params });
  },
  
  patch<T = any>(url: string, data?: any): Promise<AxiosResponse<ApiResponse<T>>> {
    return service.patch(url, data);
  }
};
