/**
 * 国际化工具函数
 * Internationalization utilities
 */
import type { I18nContext, TitleGenerator } from './types'

/**
 * 国际化实例接口
 */
interface I18nInstance {
  te: (key: string) => boolean
  t: (key: string) => string
  locale?: {
    value: string
  }
}

// 存储i18n实例的引用
let i18nInstance: I18nInstance | null = null

/**
 * 设置i18n实例
 * @param {I18nInstance} instance - i18n实例
 */
export function setI18nInstance(instance: I18nInstance): void {
  i18nInstance = instance
}

/**
 * 获取当前i18n实例
 * @returns {I18nInstance | null} i18n实例
 */
export function getI18nInstance(): I18nInstance | null {
  return i18nInstance
}

/**
 * 翻译路由标题 - 用于面包屑、侧边栏、标签页
 * @param {string} title - 标题键值
 * @returns {string} 翻译后的标题
 */
export function generateTitle(title: string): string {
  if (!i18nInstance) {
    console.warn('i18n instance not set, returning original title')
    return title
  }

  const routeKey = `route.${title}`
  const hasKey = i18nInstance.te(routeKey)

  if (hasKey) {
    const translatedTitle = i18nInstance.t(routeKey)
    return translatedTitle
  }
  
  return title
}

/**
 * 翻译文本
 * @param {string} key - 翻译键值
 * @param {string} defaultText - 默认文本（当翻译不存在时使用）
 * @returns {string} 翻译后的文本
 */
export function translateText(key: string, defaultText?: string): string {
  if (!i18nInstance) {
    return defaultText || key
  }

  const hasKey = i18nInstance.te(key)
  if (hasKey) {
    return i18nInstance.t(key)
  }
  
  return defaultText || key
}

/**
 * 检查翻译键是否存在
 * @param {string} key - 翻译键值
 * @returns {boolean} 是否存在
 */
export function hasTranslation(key: string): boolean {
  if (!i18nInstance) {
    return false
  }
  
  return i18nInstance.te(key)
}

/**
 * 获取当前语言
 * @returns {string} 当前语言代码
 */
export function getCurrentLocale(): string {
  if (!i18nInstance || !i18nInstance.locale) {
    return 'zh-CN' // 默认中文
  }
  
  return i18nInstance.locale.value || 'zh-CN'
}

/**
 * 翻译菜单标题
 * @param {string} menuKey - 菜单键值
 * @returns {string} 翻译后的菜单标题
 */
export function translateMenuTitle(menuKey: string): string {
  return translateText(`menu.${menuKey}`, menuKey)
}

/**
 * 翻译按钮文本
 * @param {string} buttonKey - 按钮键值
 * @returns {string} 翻译后的按钮文本
 */
export function translateButton(buttonKey: string): string {
  return translateText(`button.${buttonKey}`, buttonKey)
}

/**
 * 翻译消息文本
 * @param {string} messageKey - 消息键值
 * @returns {string} 翻译后的消息文本
 */
export function translateMessage(messageKey: string): string {
  return translateText(`message.${messageKey}`, messageKey)
}

/**
 * 翻译表单标签
 * @param {string} labelKey - 标签键值
 * @returns {string} 翻译后的标签文本
 */
export function translateLabel(labelKey: string): string {
  return translateText(`label.${labelKey}`, labelKey)
}

/**
 * 翻译表格列标题
 * @param {string} columnKey - 列键值
 * @returns {string} 翻译后的列标题
 */
export function translateColumn(columnKey: string): string {
  return translateText(`column.${columnKey}`, columnKey)
}

/**
 * 翻译验证错误信息
 * @param {string} errorKey - 错误键值
 * @returns {string} 翻译后的错误信息
 */
export function translateError(errorKey: string): string {
  return translateText(`error.${errorKey}`, errorKey)
}

/**
 * 批量翻译对象的属性
 * @param {Record<string, string>} obj - 要翻译的对象
 * @param {string} prefix - 翻译键前缀
 * @returns {Record<string, string>} 翻译后的对象
 */
export function translateObject(
  obj: Record<string, string>, 
  prefix: string = ''
): Record<string, string> {
  const result: Record<string, string> = {}
  
  Object.keys(obj).forEach(key => {
    const translationKey = prefix ? `${prefix}.${key}` : key
    result[key] = translateText(translationKey, obj[key])
  })
  
  return result
}

/**
 * 创建翻译函数的工厂函数
 * @param {string} namespace - 命名空间
 * @returns {function} 翻译函数
 */
export function createTranslator(namespace: string): (key: string, defaultText?: string) => string {
  return (key: string, defaultText?: string): string => {
    const fullKey = `${namespace}.${key}`
    return translateText(fullKey, defaultText)
  }
}

/**
 * 创建i18n上下文对象 - 兼容Vue 2写法
 * @returns {I18nContext} i18n上下文
 */
export function createI18nContext(): I18nContext {
  return {
    $te: (key: string): boolean => hasTranslation(key),
    $t: (key: string): string => translateText(key)
  }
}

/**
 * 带参数的翻译函数
 * @param {string} key - 翻译键值
 * @param {Record<string, any>} params - 参数对象
 * @returns {string} 翻译后的文本
 */
export function translateWithParams(key: string, params: Record<string, any>): string {
  let text = translateText(key)
  
  // 简单的参数替换
  Object.keys(params).forEach(paramKey => {
    const regex = new RegExp(`{${paramKey}}`, 'g')
    text = text.replace(regex, String(params[paramKey]))
  })
  
  return text
}

/**
 * 检查是否为RTL语言
 * @returns {boolean} 是否为RTL语言
 */
export function isRTL(): boolean {
  const locale = getCurrentLocale()
  const rtlLanguages = ['ar', 'he', 'fa', 'ur']
  return rtlLanguages.some(lang => locale.startsWith(lang))
}

/**
 * 初始化国际化系统
 * @param {I18nInstance} instance - i18n实例
 */
export function initI18nSystem(instance: I18nInstance): void {
  setI18nInstance(instance)
  console.log('i18n system initialized with locale:', getCurrentLocale())
}

// 默认导出（兼容旧代码）
export default generateTitle

// 创建标题生成器类型的实现
export const titleGenerator: TitleGenerator = generateTitle
