/**
 * 异步工具函数
 * Async utilities
 */
import type { DebounceFunction } from './types'

/**
 * 防抖函数
 * @param {function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否立即执行
 * @returns {DebounceFunction} 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number = 300,
  immediate: boolean = false
): DebounceFunction<T> {
  let timeout: NodeJS.Timeout | null = null
  let result: any

  const debounced = function (this: any, ...args: Parameters<T>) {
    const context = this
    const later = function () {
      timeout = null
      if (!immediate) result = func.apply(context, args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) result = func.apply(context, args)
    
    return result
  } as DebounceFunction<T>

  debounced.cancel = function () {
    if (timeout) {
      clearTimeout(timeout)
      timeout = null
    }
  }

  return debounced
}

/**
 * 节流函数
 * @param {function} func - 要节流的函数
 * @param {number} limit - 时间限制（毫秒）
 * @returns {function} 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number = 300
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return function (this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * 延迟执行
 * @param {number} ms - 延迟时间（毫秒）
 * @returns {Promise<void>} Promise对象
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 超时处理
 * @param {Promise<T>} promise - Promise对象
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise<T>} 带超时的Promise
 */
export function withTimeout<T>(promise: Promise<T>, timeout: number): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error('Timeout')), timeout)
    )
  ])
}

/**
 * 重试函数
 * @param {function} fn - 要重试的函数
 * @param {number} times - 重试次数
 * @param {number} interval - 重试间隔（毫秒）
 * @returns {Promise<T>} Promise对象
 */
export async function retry<T>(
  fn: () => Promise<T>,
  times: number = 3,
  interval: number = 1000
): Promise<T> {
  let lastError: Error

  for (let i = 0; i < times; i++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      if (i < times - 1) {
        await delay(interval)
      }
    }
  }

  throw lastError!
}

/**
 * 并发限制
 * @param {function[]} tasks - 任务数组
 * @param {number} limit - 并发限制数
 * @returns {Promise<T[]>} Promise对象
 */
export async function concurrentLimit<T>(
  tasks: (() => Promise<T>)[],
  limit: number = 5
): Promise<T[]> {
  const results: T[] = []
  const executing: Promise<void>[] = []

  for (let i = 0; i < tasks.length; i++) {
    const task = tasks[i]
    const promise = task().then(result => {
      results[i] = result
    })

    executing.push(promise)

    if (executing.length >= limit) {
      await Promise.race(executing)
      const index = executing.findIndex(p => p === promise)
      if (index !== -1) {
        executing.splice(index, 1)
      }
    }
  }

  await Promise.all(executing)
  return results
}

/**
 * 批量处理
 * @param {T[]} items - 数据数组
 * @param {function} processor - 处理函数
 * @param {number} batchSize - 批次大小
 * @param {number} interval - 批次间隔（毫秒）
 * @returns {Promise<U[]>} Promise对象
 */
export async function batchProcess<T, U>(
  items: T[],
  processor: (item: T) => Promise<U>,
  batchSize: number = 10,
  interval: number = 100
): Promise<U[]> {
  const results: U[] = []

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize)
    const batchResults = await Promise.all(batch.map(processor))
    results.push(...batchResults)

    if (i + batchSize < items.length) {
      await delay(interval)
    }
  }

  return results
}

/**
 * 缓存函数结果
 * @param {function} fn - 要缓存的函数
 * @param {function} keyGenerator - 键生成函数
 * @returns {function} 缓存后的函数
 */
export function memoize<T extends (...args: any[]) => any>(
  fn: T,
  keyGenerator?: (...args: Parameters<T>) => string
): T {
  const cache = new Map()

  return ((...args: Parameters<T>) => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args)

    if (cache.has(key)) {
      return cache.get(key)
    }

    const result = fn(...args)

    if (result instanceof Promise) {
      return result.catch(error => {
        cache.delete(key)
        throw error
      })
    }

    cache.set(key, result)
    return result
  }) as T
}

/**
 * 异步队列
 */
export class AsyncQueue {
  private queue: (() => Promise<any>)[] = []
  private running = false

  /**
   * 添加任务到队列
   * @param {function} task - 异步任务
   * @returns {Promise<T>} Promise对象
   */
  add<T>(task: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(() =>
        task()
          .then(resolve)
          .catch(reject)
      )
      this.process()
    })
  }

  /**
   * 处理队列
   */
  private async process(): Promise<void> {
    if (this.running || this.queue.length === 0) {
      return
    }

    this.running = true

    while (this.queue.length > 0) {
      const task = this.queue.shift()!
      try {
        await task()
      } catch (error) {
        console.error('Queue task failed:', error)
      }
    }

    this.running = false
  }

  /**
   * 清空队列
   */
  clear(): void {
    this.queue = []
  }

  /**
   * 获取队列长度
   */
  get length(): number {
    return this.queue.length
  }
}

/**
 * 创建可取消的Promise
 * @param {function} executor - 执行函数
 * @returns {object} 包含promise和cancel方法的对象
 */
export function createCancelablePromise<T>(
  executor: (
    resolve: (value: T | PromiseLike<T>) => void,
    reject: (reason?: any) => void,
    isCanceled: () => boolean
  ) => void
): { promise: Promise<T>; cancel: () => void } {
  let isCanceled = false

  const promise = new Promise<T>((resolve, reject) => {
    executor(
      value => {
        if (!isCanceled) resolve(value)
      },
      reason => {
        if (!isCanceled) reject(reason)
      },
      () => isCanceled
    )
  })

  return {
    promise,
    cancel: () => {
      isCanceled = true
    }
  }
}

/**
 * 轮询函数
 * @param {function} fn - 轮询函数
 * @param {number} interval - 轮询间隔（毫秒）
 * @param {function} condition - 停止条件
 * @returns {object} 包含stop方法的对象
 */
export function poll(
  fn: () => Promise<any> | any,
  interval: number = 1000,
  condition?: () => boolean
): { stop: () => void } {
  let timeoutId: NodeJS.Timeout | null = null
  let stopped = false

  const execute = async (): Promise<void> => {
    if (stopped) return

    try {
      await fn()
    } catch (error) {
      console.error('Poll function error:', error)
    }

    if (!stopped && (!condition || !condition())) {
      timeoutId = setTimeout(execute, interval)
    }
  }

  execute()

  return {
    stop: () => {
      stopped = true
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
    }
  }
}

/**
 * 安全的异步执行
 * @param {function} fn - 异步函数
 * @param {T} fallback - 失败时的fallback值
 * @returns {Promise<T>} Promise对象
 */
export async function safeAsync<T>(
  fn: () => Promise<T>,
  fallback: T
): Promise<T> {
  try {
    return await fn()
  } catch (error) {
    console.error('Async function failed:', error)
    return fallback
  }
}

/**
 * 锁机制
 */
export class AsyncLock {
  private locks = new Map<string, Promise<void>>()

  /**
   * 获取锁
   * @param {string} key - 锁的键
   * @param {function} fn - 要执行的函数
   * @returns {Promise<T>} Promise对象
   */
  async acquire<T>(key: string, fn: () => Promise<T>): Promise<T> {
    while (this.locks.has(key)) {
      await this.locks.get(key)
    }

    let release: () => void
    const lock = new Promise<void>(resolve => {
      release = resolve
    })

    this.locks.set(key, lock)

    try {
      return await fn()
    } finally {
      this.locks.delete(key)
      release!()
    }
  }
}
