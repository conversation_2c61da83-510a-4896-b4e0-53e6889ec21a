/**
 * 权限检查工具函数
 * Permission utilities
 */
import type { PermissionArray } from './types'

/**
 * 权限store接口（需要在使用时注入实际的store）
 */
interface PermissionStore {
  perms: string[]
}

// 存储权限store的引用
let permissionStore: PermissionStore | null = null

/**
 * 设置权限store
 * @param {PermissionStore} store - 权限store实例
 */
export function setPermissionStore(store: PermissionStore): void {
  permissionStore = store
}

/**
 * 获取当前用户权限列表
 * @returns {string[]} 权限列表
 */
export function getCurrentPermissions(): string[] {
  if (permissionStore) {
    return permissionStore.perms || []
  }
  
  // 如果没有设置store，尝试从其他地方获取
  console.warn('Permission store not set, unable to get permissions')
  return []
}

/**
 * 检查权限
 * @param {PermissionArray} value - 需要检查的权限数组
 * @returns {boolean} 是否有权限
 */
export default function checkPermission(value: PermissionArray): boolean {
  if (!value || !Array.isArray(value) || value.length === 0) {
    console.error(`Permission check failed: Invalid permission array. Expected format: ['GET /api/example', 'POST /api/example']`)
    return false
  }

  const perms = getCurrentPermissions()
  
  // 检查是否有超级管理员权限
  if (perms.includes('*')) {
    return true
  }

  // 检查是否包含所需权限中的任意一个
  const hasPermission = perms.some(perm => value.includes(perm))
  
  return hasPermission
}

/**
 * 检查是否有所有指定权限
 * @param {PermissionArray} permissions - 权限数组
 * @returns {boolean} 是否拥有所有权限
 */
export function hasAllPermissions(permissions: PermissionArray): boolean {
  if (!permissions || !Array.isArray(permissions) || permissions.length === 0) {
    return true // 空权限数组默认通过
  }

  const userPerms = getCurrentPermissions()
  
  // 超级管理员拥有所有权限
  if (userPerms.includes('*')) {
    return true
  }

  // 检查是否拥有所有指定权限
  return permissions.every(permission => userPerms.includes(permission))
}

/**
 * 检查是否有任意一个指定权限
 * @param {PermissionArray} permissions - 权限数组
 * @returns {boolean} 是否拥有任意一个权限
 */
export function hasAnyPermission(permissions: PermissionArray): boolean {
  return checkPermission(permissions)
}

/**
 * 检查单个权限
 * @param {string} permission - 权限字符串
 * @returns {boolean} 是否有权限
 */
export function hasSinglePermission(permission: string): boolean {
  if (!permission || typeof permission !== 'string') {
    return false
  }
  
  return checkPermission([permission])
}

/**
 * 检查是否为超级管理员
 * @returns {boolean} 是否为超级管理员
 */
export function isSuperAdmin(): boolean {
  const perms = getCurrentPermissions()
  return perms.includes('*')
}

/**
 * 获取用户权限信息
 * @returns {object} 权限信息对象
 */
export function getPermissionInfo(): {
  permissions: string[]
  isSuperAdmin: boolean
  hasPermissions: boolean
} {
  const permissions = getCurrentPermissions()
  
  return {
    permissions,
    isSuperAdmin: permissions.includes('*'),
    hasPermissions: permissions.length > 0
  }
}

/**
 * 权限过滤器 - 根据权限过滤数组
 * @param {T[]} items - 要过滤的数组
 * @param {function} getPermission - 获取权限的函数
 * @returns {T[]} 过滤后的数组
 */
export function filterByPermission<T>(
  items: T[],
  getPermission: (item: T) => string | string[]
): T[] {
  return items.filter(item => {
    const permission = getPermission(item)
    if (typeof permission === 'string') {
      return hasSinglePermission(permission)
    } else if (Array.isArray(permission)) {
      return hasAnyPermission(permission)
    }
    return true
  })
}

/**
 * 权限指令工厂函数 - 用于Vue指令
 * @param {PermissionArray} permissions - 权限数组
 * @returns {boolean} 是否显示元素
 */
export function createPermissionDirective(permissions: PermissionArray): boolean {
  return checkPermission(permissions)
}

/**
 * 权限路由守卫工厂函数
 * @param {PermissionArray} requiredPermissions - 需要的权限
 * @returns {boolean} 是否允许访问
 */
export function createPermissionGuard(requiredPermissions: PermissionArray): boolean {
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return true // 没有权限要求，允许访问
  }
  
  return checkPermission(requiredPermissions)
}

/**
 * 权限中间件 - 用于API请求
 * @param {PermissionArray} permissions - 所需权限
 * @returns {Promise<boolean>} 是否有权限
 */
export async function permissionMiddleware(permissions: PermissionArray): Promise<boolean> {
  return Promise.resolve(checkPermission(permissions))
}

/**
 * 初始化权限系统
 * @param {PermissionStore} store - 权限store
 */
export function initPermissionSystem(store: PermissionStore): void {
  setPermissionStore(store)
  console.log('Permission system initialized')
}

// 向后兼容的导出
export { checkPermission }
