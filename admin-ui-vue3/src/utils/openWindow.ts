/**
 * 打开窗口工具函数
 * Open window utility
 */
import type { WindowOptions } from './types'

/**
 * 打开新窗口
 * @param {string} url - 要打开的URL
 * @param {string} title - 窗口标题
 * @param {number} w - 窗口宽度
 * @param {number} h - 窗口高度
 * @returns {Window | null} 新窗口对象
 */
export default function openWindow(
  url: string, 
  title: string, 
  w: number, 
  h: number
): Window | null {
  // 修复双屏位置 - 大多数浏览器 和 Firefox
  const dualScreenLeft = window.screenLeft !== undefined ? window.screenLeft : (screen as any).left
  const dualScreenTop = window.screenTop !== undefined ? window.screenTop : (screen as any).top

  const width = window.innerWidth 
    ? window.innerWidth 
    : document.documentElement.clientWidth 
    ? document.documentElement.clientWidth 
    : screen.width

  const height = window.innerHeight 
    ? window.innerHeight 
    : document.documentElement.clientHeight 
    ? document.documentElement.clientHeight 
    : screen.height

  const left = ((width / 2) - (w / 2)) + dualScreenLeft
  const top = ((height / 2) - (h / 2)) + dualScreenTop

  const features = [
    'toolbar=no',
    'location=no', 
    'directories=no',
    'status=no',
    'menubar=no',
    'scrollbars=no',
    'resizable=yes',
    'copyhistory=no',
    `width=${w}`,
    `height=${h}`,
    `top=${top}`,
    `left=${left}`
  ].join(', ')

  const newWindow = window.open(url, title, features)

  // 将焦点放在新窗口上
  if (newWindow) {
    newWindow.focus()
  }

  return newWindow
}

/**
 * 使用选项对象打开新窗口
 * @param {WindowOptions} options - 窗口选项
 * @returns {Window | null} 新窗口对象
 */
export function openWindowWithOptions(options: WindowOptions): Window | null {
  const { url, title, width, height } = options
  return openWindow(url, title, width, height)
}

/**
 * 在新标签页中打开URL
 * @param {string} url - 要打开的URL
 * @returns {Window | null} 新窗口对象
 */
export function openInNewTab(url: string): Window | null {
  return window.open(url, '_blank')
}

/**
 * 打开居中的弹出窗口
 * @param {string} url - URL
 * @param {string} title - 标题
 * @param {number} width - 宽度，默认800
 * @param {number} height - 高度，默认600
 * @returns {Window | null} 新窗口对象
 */
export function openCenteredPopup(
  url: string,
  title: string = 'popup',
  width: number = 800,
  height: number = 600
): Window | null {
  return openWindow(url, title, width, height)
}

/**
 * 打开模态窗口
 * @param {string} url - URL
 * @param {string} title - 标题
 * @param {number} width - 宽度
 * @param {number} height - 高度
 * @returns {Window | null} 新窗口对象
 */
export function openModalWindow(
  url: string,
  title: string,
  width: number,
  height: number
): Window | null {
  const features = [
    'toolbar=no',
    'location=no',
    'directories=no',
    'status=no',
    'menubar=no',
    'scrollbars=yes',
    'resizable=no',
    'modal=yes',
    `width=${width}`,
    `height=${height}`
  ].join(', ')

  return window.open(url, title, features)
}
