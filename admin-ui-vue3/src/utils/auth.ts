/**
 * 认证相关工具函数
 * Authentication utilities
 */
import Cookies from 'js-cookie'

const TokenKey = 'X-Dts-Admin-Token'

/**
 * 获取认证Token
 * @returns {string | undefined} Token值
 */
export function getToken(): string | undefined {
  return Cookies.get(TokenKey)
}

/**
 * 设置认证Token
 * @param {string} token - Token值
 * @returns {string | undefined} 设置结果
 */
export function setToken(token: string): string | undefined {
  return Cookies.set(TokenKey, token)
}

/**
 * 移除认证Token
 */
export function removeToken(): void {
  return Cookies.remove(TokenKey)
}

/**
 * 检查是否已登录
 * @returns {boolean} 是否已登录
 */
export function isAuthenticated(): boolean {
  const token = getToken()
  return !!token
}

/**
 * 获取Token信息
 * @returns {object | null} Token信息对象
 */
export function getTokenInfo(): { value: string } | null {
  const token = getToken()
  if (!token) return null
  
  return {
    value: token
  }
}
