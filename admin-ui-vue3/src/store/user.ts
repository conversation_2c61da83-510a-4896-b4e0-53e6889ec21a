import { defineStore } from 'pinia'
import { useRouter } from 'vue-router'

export interface UserInfo {
  id: number
  username: string
  email: string
  avatar: string
  roles: string[]
}

export interface UserState {
  token: string
  userInfo: UserInfo | null
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: '',
    userInfo: null
  }),
  getters: {
    isLoggedIn: (state) => !!state.token,
    isAdmin: (state) => state.userInfo?.roles.includes('admin') || false,
    displayName: (state) => state.userInfo?.username || '未登录'
  },
  actions: {
    setToken(token: string) {
      this.token = token
    },
    setUserInfo(userInfo: UserInfo) {
      this.userInfo = userInfo
    },
    logout() {
      this.token = ''
      this.userInfo = null
      
      // 跳转到登录页
      const router = useRouter()
      router.push('/login')
    }
  },
  // 持久化 token 和用户信息
  persist: {
    pick: ['token', 'userInfo']
  }
})
