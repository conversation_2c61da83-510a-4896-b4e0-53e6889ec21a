import { defineStore } from 'pinia'

export interface SettingsState {
  title: string
  theme: 'light' | 'dark'
  language: string
  showTagsView: boolean
  tagsView: boolean
}

export const useSettingsStore = defineStore('settings', {
  state: (): SettingsState => ({
    title: 'DTS Shop Admin',
    theme: 'light',
    language: 'zh-CN',
    showTagsView: true,
    tagsView: true
  }),
  getters: {
    isDark: (state) => state.theme === 'dark',
    locale: (state) => state.language,
    tagsViewEnabled: (state) => state.showTagsView
  },
  actions: {
    setTitle(title: string) {
      this.title = title
    },
    setTheme(theme: 'light' | 'dark') {
      this.theme = theme
    },
    setLanguage(lang: string) {
      this.language = lang
    },
    setShowTagsView(show: boolean) {
      this.showTagsView = show
      this.tagsView = show
    }
  },
  // 持久化主题和语言
  persist: {
    pick: ['theme', 'language', 'tagsView']
  }
})
