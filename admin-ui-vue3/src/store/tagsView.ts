import { defineStore } from 'pinia'

export interface TagView {
  path: string
  name: string
  title?: string
}

export interface TagsViewState {
  visitedViews: TagView[]
  cachedViews: string[]
}

export const useTagsViewStore = defineStore('tagsView', {
  state: (): TagsViewState => ({
    visitedViews: [],
    cachedViews: []
  }),
  getters: {
    visitedCount: (state) => state.visitedViews.length,
    cachedCount: (state) => state.cachedViews.length,
    hasVisited: (state) => (path: string) => state.visitedViews.some(v => v.path === path)
  },
  actions: {
    addVisitedView(view: TagView) {
      if (this.visitedViews.some(v => v.path === view.path)) return
      this.visitedViews.push(view)
    },
    delVisitedView(path: string) {
      this.visitedViews = this.visitedViews.filter(v => v.path !== path)
    },
    addCachedView(name: string) {
      if (this.cachedViews.includes(name)) return
      this.cachedViews.push(name)
    },
    delCachedView(name: string) {
      this.cachedViews = this.cachedViews.filter(n => n !== name)
    },
    clearAll() {
      this.visitedViews = []
      this.cachedViews = []
    }
  },
  // 持久化标签页
  persist: {
    pick: ['visitedViews', 'cachedViews']
  }
})
