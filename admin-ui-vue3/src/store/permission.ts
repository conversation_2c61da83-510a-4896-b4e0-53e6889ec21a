import router from '@/router'
import { defineStore } from 'pinia'
import type { RouteRecordRaw } from 'vue-router'

export interface PermissionState {
  routes: RouteRecordRaw[]
  dynamicRoutes: string[]
  buttons: string[]
}

export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    routes: [],
    dynamicRoutes: [],
    buttons: []
  }),
  getters: {
    hasRoute: (state) => (route: string) => state.routes.some(r => r.path === route),
    hasButton: (state) => (btn: string) => state.buttons.includes(btn),
    dynamicRouteCount: (state) => state.dynamicRoutes.length,
    // 获取过滤后的菜单路由（排除隐藏的路由）
    menuRoutes: (state) => state.routes.filter(route => !route.meta?.hidden)
  },
  actions: {
    setRoutes(routes: RouteRecordRaw[]) {
      this.routes = routes
    },
    setDynamicRoutes(dynamicRoutes: string[]) {
      this.dynamicRoutes = dynamicRoutes
    },
    setButtons(buttons: string[]) {
      this.buttons = buttons
    },
    // 初始化路由权限
    generateRoutes() {
      // 获取所有路由，过滤掉隐藏的路由
      const accessedRoutes = router.getRoutes().filter(route => {
        return !route.meta?.hidden && route.children && route.children.length > 0
      })
      
      this.routes = accessedRoutes
      return accessedRoutes
    },
    reset() {
      this.routes = []
      this.dynamicRoutes = []
      this.buttons = []
    }
  },
  // 持久化权限相关信息
  persist: {
    pick: ['dynamicRoutes', 'buttons']
  }
})
