import { ref } from 'vue';

// Toast 配置接口
export interface ToastOptions {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive' | 'success';
  duration?: number;
}

// Toast 项接口
export interface ToastItem extends ToastOptions {
  id: string;
}

// Toast 状态管理
const toasts = ref<ToastItem[]>([]);

// 生成唯一 ID
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Toast 显示函数
export function toast(options: ToastOptions) {
  const id = generateId();
  const toastItem: ToastItem = {
    id,
    title: options.title || '',
    description: options.description || '',
    variant: options.variant || 'default',
    duration: options.duration || 3000,
  };

  toasts.value.push(toastItem);

  // 自动移除 Toast
  if (toastItem.duration && toastItem.duration > 0) {
    setTimeout(() => {
      removeToast(id);
    }, toastItem.duration);
  }

  return id;
}

// 移除 Toast
export function removeToast(id: string) {
  const index = toasts.value.findIndex(toast => toast.id === id);
  if (index > -1) {
    toasts.value.splice(index, 1);
  }
}

// 清空所有 Toast
export function clearToasts() {
  toasts.value = [];
}

// useToast hook
export function useToast() {
  return {
    toast,
    toasts: toasts.value,
    removeToast,
    clearToasts,
  };
}

// 默认导出
export default useToast;
