<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { BarChart3, Users, ShoppingCart, TrendingUp } from 'lucide-vue-next'

// 仪表盘数据
const stats = ref([
  {
    title: '总用户数',
    value: '1,234',
    change: '+12%',
    icon: Users,
    color: 'text-blue-600'
  },
  {
    title: '订单总数',
    value: '567',
    change: '+8%',
    icon: ShoppingCart,
    color: 'text-green-600'
  },
  {
    title: '总销售额',
    value: '¥89,123',
    change: '+15%',
    icon: TrendingUp,
    color: 'text-purple-600'
  },
  {
    title: '活跃用户',
    value: '890',
    change: '+5%',
    icon: BarChart3,
    color: 'text-orange-600'
  }
])

onMounted(() => {
  console.log('Dashboard 页面已加载')
})
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-3xl font-bold text-foreground">仪表盘</h1>
      <p class="text-muted-foreground mt-2">欢迎使用 DTS Shop 管理系统</p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div
        v-for="stat in stats"
        :key="stat.title"
        class="bg-card rounded-lg border border-border p-6 shadow-sm hover:shadow-md transition-shadow"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-muted-foreground">{{ stat.title }}</p>
            <p class="text-2xl font-bold text-card-foreground mt-2">{{ stat.value }}</p>
            <Badge variant="secondary" class="mt-2 text-xs">
              {{ stat.change }}
            </Badge>
          </div>
          <div :class="['p-3 rounded-full bg-muted', stat.color]">
            <component :is="stat.icon" class="h-6 w-6" />
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="bg-card rounded-lg border border-border p-6">
      <h2 class="text-xl font-semibold text-card-foreground mb-4">快速操作</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Button class="h-auto p-4 flex flex-col items-center gap-2">
          <Users class="h-8 w-8" />
          <span>用户管理</span>
        </Button>
        <Button variant="outline" class="h-auto p-4 flex flex-col items-center gap-2">
          <ShoppingCart class="h-8 w-8" />
          <span>订单管理</span>
        </Button>
        <Button variant="outline" class="h-auto p-4 flex flex-col items-center gap-2">
          <BarChart3 class="h-8 w-8" />
          <span>数据统计</span>
        </Button>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="bg-card rounded-lg border border-border p-6">
      <h2 class="text-xl font-semibold text-card-foreground mb-4">最近活动</h2>
      <div class="space-y-4">
        <div class="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
          <div class="w-2 h-2 bg-green-500 rounded-full"></div>
          <div class="flex-1">
            <p class="text-sm font-medium">新用户注册</p>
            <p class="text-xs text-muted-foreground">张三 刚刚注册了账号</p>
          </div>
          <span class="text-xs text-muted-foreground">2分钟前</span>
        </div>
        <div class="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
          <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
          <div class="flex-1">
            <p class="text-sm font-medium">新订单</p>
            <p class="text-xs text-muted-foreground">订单 #12345 已创建</p>
          </div>
          <span class="text-xs text-muted-foreground">5分钟前</span>
        </div>
        <div class="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
          <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
          <div class="flex-1">
            <p class="text-sm font-medium">商品上架</p>
            <p class="text-xs text-muted-foreground">新商品 "测试商品" 已上架</p>
          </div>
          <span class="text-xs text-muted-foreground">10分钟前</span>
        </div>
      </div>
    </div>
  </div>
</template>
