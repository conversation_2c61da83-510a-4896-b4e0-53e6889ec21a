<script setup lang="ts">
import { ref } from 'vue'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Users, Plus, Search, Filter } from 'lucide-vue-next'

// 模拟用户数据
const users = ref([
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    role: '管理员',
    status: 'active',
    createTime: '2024-01-01'
  },
  {
    id: 2,
    username: 'user1',
    email: '<EMAIL>',
    role: '普通用户',
    status: 'active',
    createTime: '2024-01-02'
  },
  {
    id: 3,
    username: 'user2',
    email: '<EMAIL>',
    role: '普通用户',
    status: 'inactive',
    createTime: '2024-01-03'
  }
])

const searchQuery = ref('')
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-foreground flex items-center gap-2">
          <Users class="h-6 w-6" />
          用户管理
        </h1>
        <p class="text-muted-foreground mt-1">管理系统用户信息</p>
      </div>
      <Button>
        <Plus class="mr-2 h-4 w-4" />
        新增用户
      </Button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="flex gap-4">
      <div class="flex-1 relative">
        <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索用户名或邮箱..."
          class="w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
        />
      </div>
      <Button variant="outline">
        <Filter class="mr-2 h-4 w-4" />
        筛选
      </Button>
    </div>

    <!-- 用户表格 -->
    <div class="bg-card rounded-lg border border-border overflow-hidden">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-muted/50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                用户信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                角色
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                创建时间
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-border">
            <tr v-for="user in users" :key="user.id" class="hover:bg-muted/25">
              <td class="px-6 py-4">
                <div>
                  <div class="text-sm font-medium text-card-foreground">
                    {{ user.username }}
                  </div>
                  <div class="text-sm text-muted-foreground">
                    {{ user.email }}
                  </div>
                </div>
              </td>
              <td class="px-6 py-4">
                <Badge variant="secondary">{{ user.role }}</Badge>
              </td>
              <td class="px-6 py-4">
                <Badge :variant="user.status === 'active' ? 'default' : 'secondary'">
                  {{ user.status === 'active' ? '激活' : '禁用' }}
                </Badge>
              </td>
              <td class="px-6 py-4 text-sm text-muted-foreground">
                {{ user.createTime }}
              </td>
              <td class="px-6 py-4 text-right space-x-2">
                <Button size="sm" variant="outline">编辑</Button>
                <Button size="sm" variant="destructive">删除</Button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex items-center justify-between">
      <div class="text-sm text-muted-foreground">
        显示 1 到 3 条，共 3 条记录
      </div>
      <div class="flex gap-2">
        <Button size="sm" variant="outline" disabled>上一页</Button>
        <Button size="sm" variant="outline" disabled>下一页</Button>
      </div>
    </div>
  </div>
</template>
