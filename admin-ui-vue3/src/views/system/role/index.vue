<script setup lang="ts">
import { ref } from 'vue'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Shield, Plus } from 'lucide-vue-next'

const roles = ref([
  { id: 1, name: '管理员', code: 'admin', description: '系统管理员，拥有所有权限' },
  { id: 2, name: '普通用户', code: 'user', description: '普通用户，有限权限' }
])
</script>

<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-foreground flex items-center gap-2">
          <Shield class="h-6 w-6" />
          角色管理
        </h1>
        <p class="text-muted-foreground mt-1">管理系统角色和权限</p>
      </div>
      <Button>
        <Plus class="mr-2 h-4 w-4" />
        新增角色
      </Button>
    </div>

    <div class="grid gap-4">
      <div v-for="role in roles" :key="role.id" class="bg-card rounded-lg border border-border p-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold">{{ role.name }}</h3>
            <Badge variant="secondary" class="mt-1">{{ role.code }}</Badge>
            <p class="text-muted-foreground mt-2">{{ role.description }}</p>
          </div>
          <div class="space-x-2">
            <Button size="sm" variant="outline">编辑</Button>
            <Button size="sm" variant="destructive">删除</Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
