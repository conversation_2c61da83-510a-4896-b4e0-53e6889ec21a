<script setup lang="ts">
import { ShoppingCart } from 'lucide-vue-next'
</script>

<template>
  <div class="space-y-6">
    <div>
      <h1 class="text-2xl font-bold text-foreground flex items-center gap-2">
        <ShoppingCart class="h-6 w-6" />
        订单管理
      </h1>
      <p class="text-muted-foreground mt-1">管理商城订单信息</p>
    </div>
    <div class="bg-card rounded-lg border border-border p-8 text-center">
      <p class="text-muted-foreground">订单管理功能开发中...</p>
    </div>
  </div>
</template>
