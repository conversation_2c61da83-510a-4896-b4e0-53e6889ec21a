<script setup lang="ts">
import { ref } from 'vue'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Package, Plus } from 'lucide-vue-next'

const goods = ref([
  { id: 1, name: '测试商品1', price: 99.99, status: 'active', stock: 100 },
  { id: 2, name: '测试商品2', price: 199.99, status: 'inactive', stock: 50 }
])
</script>

<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-foreground flex items-center gap-2">
          <Package class="h-6 w-6" />
          商品管理
        </h1>
        <p class="text-muted-foreground mt-1">管理商城商品信息</p>
      </div>
      <Button>
        <Plus class="mr-2 h-4 w-4" />
        新增商品
      </Button>
    </div>

    <div class="bg-card rounded-lg border border-border overflow-hidden">
      <table class="w-full">
        <thead class="bg-muted/50">
          <tr>
            <th class="px-6 py-3 text-left">商品名称</th>
            <th class="px-6 py-3 text-left">价格</th>
            <th class="px-6 py-3 text-left">库存</th>
            <th class="px-6 py-3 text-left">状态</th>
            <th class="px-6 py-3 text-right">操作</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-border">
          <tr v-for="item in goods" :key="item.id">
            <td class="px-6 py-4">{{ item.name }}</td>
            <td class="px-6 py-4">¥{{ item.price }}</td>
            <td class="px-6 py-4">{{ item.stock }}</td>
            <td class="px-6 py-4">
              <Badge :variant="item.status === 'active' ? 'default' : 'secondary'">
                {{ item.status === 'active' ? '上架' : '下架' }}
              </Badge>
            </td>
            <td class="px-6 py-4 text-right space-x-2">
              <Button size="sm" variant="outline">编辑</Button>
              <Button size="sm" variant="destructive">删除</Button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
