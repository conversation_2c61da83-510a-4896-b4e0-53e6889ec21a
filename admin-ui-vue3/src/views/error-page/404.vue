<script setup lang="ts">
import { useRouter } from 'vue-router'
import { But<PERSON> } from '@/components/ui/button'
import { Home, ArrowLeft } from 'lucide-vue-next'

const router = useRouter()

const goBack = () => {
  router.go(-1)
}

const goHome = () => {
  router.push('/dashboard')
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-background">
    <div class="max-w-md w-full text-center space-y-8">
      <!-- 404 图标 -->
      <div class="space-y-4">
        <div class="text-9xl font-bold text-muted-foreground/20">404</div>
        <h1 class="text-2xl font-bold text-foreground">页面未找到</h1>
        <p class="text-muted-foreground">
          抱歉，您访问的页面不存在或已被移除。
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="flex flex-col sm:flex-row gap-3 justify-center">
        <Button @click="goBack" variant="outline">
          <ArrowLeft class="mr-2 h-4 w-4" />
          返回上页
        </Button>
        <Button @click="goHome">
          <Home class="mr-2 h-4 w-4" />
          回到首页
        </Button>
      </div>

      <!-- 帮助信息 -->
      <div class="text-sm text-muted-foreground">
        如果您认为这是一个错误，请联系系统管理员。
      </div>
    </div>
  </div>
</template>
