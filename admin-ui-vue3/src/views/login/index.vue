<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/user'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Eye, EyeOff, LogIn } from 'lucide-vue-next'

const router = useRouter()
const userStore = useUserStore()

const loginForm = ref({
  username: 'admin',
  password: 'admin123'
})

const showPassword = ref(false)
const loading = ref(false)

const handleLogin = async () => {
  loading.value = true
  try {
    // 模拟登录
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 设置用户信息
    userStore.setUserInfo({
      id: 1,
      username: loginForm.value.username,
      email: '<EMAIL>',
      avatar: '',
      roles: ['admin']
    })
    
    // 设置 token
    userStore.setToken('mock-token-123')
    
    // 跳转到首页
    router.push('/dashboard')
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}

const togglePassword = () => {
  showPassword.value = !showPassword.value
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted">
    <div class="max-w-md w-full space-y-8 p-8">
      <!-- 登录卡片 -->
      <div class="bg-card rounded-lg border border-border p-8 shadow-lg">
        <!-- 头部 -->
        <div class="text-center space-y-4 mb-8">
          <div class="w-16 h-16 bg-primary rounded-lg flex items-center justify-center mx-auto">
            <span class="text-primary-foreground font-bold text-xl">DTS</span>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-card-foreground">欢迎回来</h1>
            <p class="text-muted-foreground">登录到 DTS Shop 管理系统</p>
          </div>
        </div>

        <!-- 登录表单 -->
        <form @submit.prevent="handleLogin" class="space-y-6">
          <!-- 用户名 -->
          <div class="space-y-2">
            <label class="text-sm font-medium text-card-foreground">用户名</label>
            <input
              v-model="loginForm.username"
              type="text"
              class="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
              placeholder="请输入用户名"
              required
            />
          </div>

          <!-- 密码 -->
          <div class="space-y-2">
            <label class="text-sm font-medium text-card-foreground">密码</label>
            <div class="relative">
              <input
                v-model="loginForm.password"
                :type="showPassword ? 'text' : 'password'"
                class="w-full px-3 py-2 pr-10 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                placeholder="请输入密码"
                required
              />
              <button
                type="button"
                @click="togglePassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground"
              >
                <Eye v-if="!showPassword" class="h-4 w-4" />
                <EyeOff v-else class="h-4 w-4" />
              </button>
            </div>
          </div>

          <!-- 登录按钮 -->
          <Button
            type="submit"
            :disabled="loading"
            class="w-full"
          >
            <LogIn v-if="!loading" class="mr-2 h-4 w-4" />
            <div v-else class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
            {{ loading ? '登录中...' : '登录' }}
          </Button>
        </form>

        <!-- 演示信息 -->
        <div class="mt-6 p-4 bg-muted rounded-lg">
          <p class="text-sm text-muted-foreground mb-2">演示账号：</p>
          <div class="flex flex-wrap gap-2">
            <Badge variant="secondary" class="text-xs">
              用户名: admin
            </Badge>
            <Badge variant="secondary" class="text-xs">
              密码: admin123
            </Badge>
          </div>
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="text-center text-sm text-muted-foreground">
        © 2024 DTS Shop. All rights reserved.
      </div>
    </div>
  </div>
</template>
