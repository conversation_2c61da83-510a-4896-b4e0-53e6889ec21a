<script setup lang="ts">
import { computed } from 'vue'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Sun, Moon, Monitor } from 'lucide-vue-next'

// 主题状态管理
const theme = computed({
  get: () => {
    if (typeof window === 'undefined') return 'system'
    return localStorage.getItem('theme') || 'system'
  },
  set: (value: string) => {
    if (typeof window === 'undefined') return
    localStorage.setItem('theme', value)
    applyTheme(value)
  }
})

// 应用主题
const applyTheme = (theme: string) => {
  const root = document.documentElement
  
  if (theme === 'dark') {
    root.classList.add('dark')
  } else if (theme === 'light') {
    root.classList.remove('dark')
  } else {
    // system theme
    const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    if (isDark) {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
  }
}

// 切换主题
const setTheme = (newTheme: string) => {
  theme.value = newTheme
}

// 获取当前主题图标
const getCurrentThemeIcon = () => {
  const currentTheme = theme.value
  if (currentTheme === 'dark') return Moon
  if (currentTheme === 'light') return Sun
  return Monitor
}

// 初始化主题
if (typeof window !== 'undefined') {
  applyTheme(theme.value)
  
  // 监听系统主题变化
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
    if (theme.value === 'system') {
      applyTheme('system')
    }
  })
}
</script>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button variant="ghost" size="icon">
        <component :is="getCurrentThemeIcon()" class="h-5 w-5" />
        <span class="sr-only">切换主题</span>
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end">
      <DropdownMenuItem @click="setTheme('light')">
        <Sun class="mr-2 h-4 w-4" />
        <span>浅色模式</span>
      </DropdownMenuItem>
      <DropdownMenuItem @click="setTheme('dark')">
        <Moon class="mr-2 h-4 w-4" />
        <span>深色模式</span>
      </DropdownMenuItem>
      <DropdownMenuItem @click="setTheme('system')">
        <Monitor class="mr-2 h-4 w-4" />
        <span>跟随系统</span>
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
