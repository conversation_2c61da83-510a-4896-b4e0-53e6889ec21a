<script setup lang="ts">
import { computed, nextTick, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTagsViewStore } from '@/store/tagsView'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { ScrollArea } from '@/components/ui/scroll-area'
import { X, MoreHorizontal, Home, RefreshCw, ArrowLeft, ArrowRight } from 'lucide-vue-next'

const route = useRoute()
const router = useRouter()
const tagsViewStore = useTagsViewStore()

const visitedViews = computed(() => tagsViewStore.visitedViews)
const isActive = (view: any) => view.path === route.path

// 右键菜单相关
const contextMenuVisible = ref(false)
const contextMenuX = ref(0)
const contextMenuY = ref(0)
const selectedTag = ref<any>(null)

// 切换到指定标签
const visitTag = (view: any) => {
  router.push(view.path)
}

// 关闭标签
const closeSelectedTag = (view: any) => {
  tagsViewStore.delView(view).then((views: any) => {
    if (isActive(view)) {
      toLastView(views, view)
    }
  })
}

// 关闭其他标签
const closeOthersTags = (view: any) => {
  tagsViewStore.delOthersViews(view)
}

// 关闭所有标签
const closeAllTags = (view: any) => {
  tagsViewStore.delAllViews().then((views: any) => {
    if (views.some((v: any) => v.path === route.path)) {
      return
    }
    toLastView(views, view)
  })
}

// 刷新当前页面
const refreshSelectedTag = (view: any) => {
  tagsViewStore.delCachedView(view).then(() => {
    const { fullPath } = view
    nextTick(() => {
      router.replace({
        path: '/redirect' + fullPath
      })
    })
  })
}

// 跳转到最后一个视图
const toLastView = (visitedViews: any[], view: any) => {
  const latestView = visitedViews.slice(-1)[0]
  if (latestView) {
    router.push(latestView.fullPath)
  } else {
    // 如果没有其他视图，回到首页
    if (view.name === 'Dashboard') {
      // 重新加载页面
      router.replace({ path: '/redirect' + view.fullPath })
    } else {
      router.push('/dashboard')
    }
  }
}

// 右键菜单处理
const openMenu = (tag: any, e: MouseEvent) => {
  const menuMinWidth = 105
  const offsetLeft = e.currentTarget ? (e.currentTarget as HTMLElement).offsetLeft : 0
  const offsetWidth = e.currentTarget ? (e.currentTarget as HTMLElement).offsetWidth : 0
  const maxLeft = offsetLeft + offsetWidth - menuMinWidth
  const left = Math.min(e.clientX, maxLeft)

  contextMenuX.value = left
  contextMenuY.value = e.clientY
  contextMenuVisible.value = true
  selectedTag.value = tag
}

// 关闭右键菜单
const closeMenu = () => {
  contextMenuVisible.value = false
}

// 是否可以关闭（首页不能关闭）
const isClosable = (view: any) => {
  return view.meta && !view.meta.affix
}
</script>

<template>
  <div class="tags-view-container h-9 bg-background relative">
    <ScrollArea class="h-full">
      <div class="flex items-center gap-1 px-2 py-1">
        <template v-for="tag in visitedViews" :key="tag.path">
          <Badge
            :variant="isActive(tag) ? 'default' : 'secondary'"
            class="h-7 px-3 cursor-pointer select-none group flex items-center gap-1.5 text-xs font-normal hover:shadow-sm transition-all"
            @click="visitTag(tag)"
            @contextmenu.prevent="openMenu(tag, $event)"
          >
            <!-- 首页图标 -->
            <Home v-if="tag.meta?.affix" class="h-3 w-3" />
            
            <!-- 标签标题 -->
            <span class="max-w-[120px] truncate">
              {{ tag.title || tag.meta?.title || tag.name }}
            </span>
            
            <!-- 关闭按钮 -->
            <Button
              v-if="isClosable(tag)"
              variant="ghost"
              size="icon"
              class="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground opacity-0 group-hover:opacity-100 transition-opacity"
              @click.stop="closeSelectedTag(tag)"
            >
              <X class="h-3 w-3" />
            </Button>
          </Badge>
        </template>
      </div>
    </ScrollArea>

    <!-- 右键菜单 -->
    <DropdownMenu v-model:open="contextMenuVisible">
      <DropdownMenuTrigger class="hidden" />
      <DropdownMenuContent
        :style="{ position: 'fixed', left: contextMenuX + 'px', top: contextMenuY + 'px' }"
        @click="closeMenu"
      >
        <DropdownMenuItem @click="refreshSelectedTag(selectedTag)">
          <RefreshCw class="mr-2 h-4 w-4" />
          刷新
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          v-if="isClosable(selectedTag)"
          @click="closeSelectedTag(selectedTag)"
        >
          <X class="mr-2 h-4 w-4" />
          关闭
        </DropdownMenuItem>
        <DropdownMenuItem @click="closeOthersTags(selectedTag)">
          <ArrowLeft class="mr-2 h-4 w-4" />
          关闭其他
        </DropdownMenuItem>
        <DropdownMenuItem @click="closeAllTags(selectedTag)">
          <ArrowRight class="mr-2 h-4 w-4" />
          关闭所有
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>

<style scoped>
.tags-view-container {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 1px 2px 0 rgba(0, 0, 0, 0.24);
}
</style>
