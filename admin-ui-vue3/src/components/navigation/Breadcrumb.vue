<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Home } from 'lucide-vue-next'

const route = useRoute()
const router = useRouter()

// 生成面包屑路径
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  const first = matched[0]
  
  if (!first || first.name !== 'Dashboard') {
    matched.unshift({ 
      path: '/dashboard', 
      meta: { title: '首页' },
      name: 'Dashboard'
    } as any)
  }
  
  return matched.map(item => ({
    title: item.meta?.title || item.name,
    path: item.path,
    disabled: item.path === route.path
  }))
})

// 导航到指定路径
const navigateTo = (path: string) => {
  if (path !== route.path) {
    router.push(path)
  }
}
</script>

<template>
  <Breadcrumb>
    <BreadcrumbList>
      <template v-for="(item, index) in breadcrumbs" :key="item.path">
        <BreadcrumbItem>
          <!-- 首页图标 -->
          <BreadcrumbLink
            v-if="index === 0"
            @click="navigateTo(item.path)"
            class="flex items-center gap-1.5 cursor-pointer hover:text-foreground"
          >
            <Home class="h-4 w-4" />
            <span>{{ item.title }}</span>
          </BreadcrumbLink>
          
          <!-- 当前页面（不可点击） -->
          <BreadcrumbPage v-else-if="item.disabled">
            {{ item.title }}
          </BreadcrumbPage>
          
          <!-- 可点击的面包屑项 -->
          <BreadcrumbLink
            v-else
            @click="navigateTo(item.path)"
            class="cursor-pointer hover:text-foreground"
          >
            {{ item.title }}
          </BreadcrumbLink>
        </BreadcrumbItem>
        
        <!-- 分隔符（不是最后一个时显示） -->
        <BreadcrumbSeparator v-if="index < breadcrumbs.length - 1" />
      </template>
    </BreadcrumbList>
  </Breadcrumb>
</template>
