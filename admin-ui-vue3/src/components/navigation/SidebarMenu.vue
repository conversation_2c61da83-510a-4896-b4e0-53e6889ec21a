<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/store/app'
import { usePermissionStore } from '@/store/permission'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { ChevronDown, ChevronRight } from 'lucide-vue-next'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const permissionStore = usePermissionStore()

const sidebarCollapsed = computed(() => appStore.sidebar.collapsed)
const routes = computed(() => permissionStore.routes)

// 展开的菜单项
const openedMenus = computed({
  get: () => appStore.sidebar.openedMenus,
  set: (value) => appStore.setSidebarOpenedMenus(value)
})

// 检查路由是否激活
const isActiveRoute = (routePath: string): boolean => {
  return route.path === routePath || route.path.startsWith(routePath + '/')
}

// 检查是否有激活的子菜单
const hasActiveChild = (menuItem: any): boolean => {
  if (menuItem.children) {
    return menuItem.children.some((child: any) => {
      return isActiveRoute(child.path) || hasActiveChild(child)
    })
  }
  return false
}

// 切换菜单展开状态
const toggleMenu = (path: string) => {
  if (sidebarCollapsed.value) return
  
  const currentOpenedMenus = [...openedMenus.value]
  const index = currentOpenedMenus.indexOf(path)
  
  if (index > -1) {
    currentOpenedMenus.splice(index, 1)
  } else {
    currentOpenedMenus.push(path)
  }
  
  openedMenus.value = currentOpenedMenus
}

// 导航到路由
const navigateTo = (routePath: string) => {
  router.push(routePath)
  // 移动端自动收起侧边栏
  if (appStore.device === 'mobile') {
    appStore.closeSidebar(false)
  }
}

// 获取菜单图标组件
const getMenuIcon = (icon?: string) => {
  // 这里可以根据 icon 字符串返回对应的图标组件
  // 暂时返回一个默认的圆点
  return 'div'
}

// 检查菜单是否应该展开
const isMenuOpen = (path: string): boolean => {
  return openedMenus.value.includes(path) || hasActiveChild({ path, children: [] })
}
</script>

<template>
  <ScrollArea class="flex-1 px-3 py-2">
    <nav class="space-y-1">
      <!-- 递归渲染菜单项 -->
      <template v-for="item in routes" :key="item.path">
        <!-- 有子菜单的项目 -->
        <div v-if="item.children && item.children.length > 0" class="space-y-1">
          <!-- 父菜单项 -->
          <Button
            variant="ghost"
            :class="[
              'w-full justify-start h-9 px-3',
              hasActiveChild(item) ? 'bg-accent text-accent-foreground' : '',
              sidebarCollapsed ? 'px-2' : ''
            ]"
            @click="toggleMenu(item.path)"
          >
            <!-- 菜单图标 -->
            <div class="w-4 h-4 rounded-sm bg-muted flex-shrink-0" />
            
            <!-- 菜单标题（非折叠状态显示） -->
            <span v-if="!sidebarCollapsed" class="ml-3 flex-1 text-left">
              {{ item.meta?.title || item.name }}
            </span>
            
            <!-- 展开/收起图标（非折叠状态显示） -->
            <ChevronDown
              v-if="!sidebarCollapsed"
              :class="[
                'w-4 h-4 transition-transform duration-200',
                isMenuOpen(item.path) ? 'rotate-180' : ''
              ]"
            />
          </Button>
          
          <!-- 子菜单（展开时显示，折叠状态不显示） -->
          <div
            v-if="!sidebarCollapsed && isMenuOpen(item.path)"
            class="ml-4 space-y-1 border-l border-border pl-4"
          >
            <template v-for="child in item.children" :key="child.path">
              <!-- 子菜单项 -->
              <Button
                variant="ghost"
                :class="[
                  'w-full justify-start h-8 px-3 text-sm',
                  isActiveRoute(child.path) ? 'bg-accent text-accent-foreground font-medium' : ''
                ]"
                @click="navigateTo(child.path)"
              >
                <div class="w-3 h-3 rounded-sm bg-muted/60 flex-shrink-0" />
                <span class="ml-3">{{ child.meta?.title || child.name }}</span>
              </Button>
            </template>
          </div>
        </div>
        
        <!-- 无子菜单的项目 -->
        <Button
          v-else
          variant="ghost"
          :class="[
            'w-full justify-start h-9 px-3',
            isActiveRoute(item.path) ? 'bg-accent text-accent-foreground font-medium' : '',
            sidebarCollapsed ? 'px-2' : ''
          ]"
          @click="navigateTo(item.path)"
        >
          <!-- 菜单图标 -->
          <div class="w-4 h-4 rounded-sm bg-muted flex-shrink-0" />
          
          <!-- 菜单标题（非折叠状态显示） -->
          <span v-if="!sidebarCollapsed" class="ml-3">
            {{ item.meta?.title || item.name }}
          </span>
        </Button>
      </template>
    </nav>
  </ScrollArea>
</template>
