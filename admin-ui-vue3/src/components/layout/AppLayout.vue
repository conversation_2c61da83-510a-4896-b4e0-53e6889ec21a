<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/store/app'
import Header from './Header.vue'
import Sidebar from './Sidebar.vue'
import Main from './Main.vue'

const appStore = useAppStore()

const sidebarCollapsed = computed(() => appStore.sidebar.collapsed)
const device = computed(() => appStore.device)

// 侧边栏宽度计算
const sidebarWidth = computed(() => {
  if (device.value === 'mobile') return '0px'
  return sidebarCollapsed.value ? '64px' : '256px'
})

// 主内容区域的左边距
const mainMarginLeft = computed(() => {
  if (device.value === 'mobile') return '0px'
  return sidebarWidth.value
})

// 检测是否为移动端
const isMobile = computed(() => device.value === 'mobile')
</script>

<template>
  <div class="h-screen bg-background overflow-hidden">
    <!-- 移动端遮罩层 -->
    <div
      v-if="isMobile && !sidebarCollapsed"
      class="fixed inset-0 z-40 bg-black/50 lg:hidden"
      @click="appStore.closeSidebar(false)"
    />
    
    <!-- 侧边栏 -->
    <Sidebar
      :class="[
        'fixed top-0 left-0 z-50 h-full transition-transform duration-300 ease-in-out',
        {
          'translate-x-0': !sidebarCollapsed || !isMobile,
          '-translate-x-full': sidebarCollapsed && isMobile,
        }
      ]"
      :style="{ width: sidebarWidth }"
    />
    
    <!-- 主内容区域 -->
    <div
      class="flex flex-col h-full transition-all duration-300 ease-in-out"
      :style="{ marginLeft: mainMarginLeft }"
    >
      <!-- 顶部导航栏 -->
      <Header class="flex-shrink-0" />
      
      <!-- 主要内容区域 -->
      <Main class="flex-1 overflow-hidden" />
    </div>
  </div>
</template>
