<script setup lang="ts">
import { computed } from 'vue'
import { useTagsViewStore } from '@/store/tagsView'
import { useSettingsStore } from '@/store/settings'
import Breadcrumb from '@/components/navigation/Breadcrumb.vue'
import TagsView from '@/components/navigation/TagsView.vue'
import { ScrollArea } from '@/components/ui/scroll-area'

const tagsViewStore = useTagsViewStore()
const settingsStore = useSettingsStore()

const showTagsView = computed(() => settingsStore.tagsView)
const visitedViews = computed(() => tagsViewStore.visitedViews)
</script>

<template>
  <main class="flex flex-col h-full bg-background">
    <!-- 面包屑导航 -->
    <div class="h-12 flex items-center px-4 lg:px-6 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <Breadcrumb />
    </div>
    
    <!-- 标签页视图（可选） -->
    <TagsView 
      v-if="showTagsView" 
      class="flex-shrink-0 border-b border-border" 
    />
    
    <!-- 主要内容区域 -->
    <div class="flex-1 overflow-hidden">
      <ScrollArea class="h-full">
        <div class="p-4 lg:p-6">
          <!-- 路由视图容器 -->
          <router-view v-slot="{ Component, route }">
            <transition
              name="fade-transform"
              mode="out-in"
              appear
            >
              <keep-alive>
                <component
                  :is="Component"
                  :key="route.path"
                />
              </keep-alive>
            </transition>
          </router-view>
        </div>
      </ScrollArea>
    </div>
  </main>
</template>

<style scoped>
/* 页面切换动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.2s ease;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(10px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}
</style>
