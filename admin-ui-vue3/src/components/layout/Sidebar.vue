<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/store/app'
import { useSettingsStore } from '@/store/settings'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { PanelLeftClose, PanelLeftOpen } from 'lucide-vue-next'
import SidebarMenu from '@/components/navigation/SidebarMenu.vue'

const appStore = useAppStore()
const settingsStore = useSettingsStore()

const sidebarCollapsed = computed(() => appStore.sidebar.collapsed)
const device = computed(() => appStore.device)
const title = computed(() => settingsStore.title)

// 切换侧边栏
const toggleSidebar = () => {
  appStore.toggleSidebar()
}
</script>

<template>
  <aside class="h-full bg-sidebar border-r border-sidebar-border flex flex-col">
    <!-- 侧边栏头部 -->
    <div class="h-16 flex items-center justify-between px-4 border-b border-sidebar-border">
      <!-- Logo 和标题区域 -->
      <div v-if="!sidebarCollapsed" class="flex items-center gap-3 min-w-0">
        <div class="w-8 h-8 bg-sidebar-primary rounded-lg flex items-center justify-center flex-shrink-0">
          <span class="text-sidebar-primary-foreground font-bold text-sm">DTS</span>
        </div>
        <h1 class="text-lg font-semibold text-sidebar-foreground truncate">
          {{ title }}
        </h1>
      </div>
      
      <!-- 折叠时只显示 Logo -->
      <div v-else class="flex justify-center w-full">
        <div class="w-8 h-8 bg-sidebar-primary rounded-lg flex items-center justify-center">
          <span class="text-sidebar-primary-foreground font-bold text-sm">DTS</span>
        </div>
      </div>
      
      <!-- 折叠/展开按钮（桌面端显示） -->
      <Button
        v-if="device !== 'mobile'"
        variant="ghost"
        size="icon"
        @click="toggleSidebar"
        class="text-sidebar-foreground hover:bg-sidebar-accent flex-shrink-0"
        :class="sidebarCollapsed ? 'ml-2' : ''"
      >
        <PanelLeftOpen v-if="sidebarCollapsed" class="h-4 w-4" />
        <PanelLeftClose v-else class="h-4 w-4" />
      </Button>
    </div>
    
    <!-- 分隔线 -->
    <Separator class="bg-sidebar-border" />
    
    <!-- 导航菜单 -->
    <SidebarMenu />
    
    <!-- 侧边栏底部 -->
    <div class="mt-auto p-4 border-t border-sidebar-border">
      <div v-if="!sidebarCollapsed" class="text-xs text-sidebar-foreground/60 text-center">
        DTS Shop Admin v3.0
      </div>
      <div v-else class="flex justify-center">
        <div class="w-2 h-2 bg-sidebar-accent rounded-full"></div>
      </div>
    </div>
  </aside>
</template>
