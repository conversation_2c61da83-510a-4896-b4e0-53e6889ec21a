import { AppLayout } from '@/components/layout'
import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
    {
        path: '/login',
        name: 'Login',
        component: () => import('../views/login/index.vue'),
        meta: { hidden: true }
    },
    {
        path: '/redirect',
        component: AppLayout,
        meta: { hidden: true },
        children: [
            {
                path: '/redirect/:path(.*)',
                component: () => import('../views/redirect/index.vue')
            }
        ]
    },
    {
        path: '/',
        component: AppLayout,
        redirect: '/dashboard',
        children: [
            {
                path: 'dashboard',
                name: 'Dashboard',
                component: () => import('../views/dashboard/index.vue'),
                meta: { 
                    title: '首页',
                    icon: 'dashboard',
                    affix: true
                }
            }
        ]
    },
    // 系统管理
    {
        path: '/system',
        component: AppLayout,
        redirect: '/system/user',
        name: 'System',
        meta: {
            title: '系统管理',
            icon: 'system'
        },
        children: [
            {
                path: 'user',
                name: 'User',
                component: () => import('../views/system/user/index.vue'),
                meta: { 
                    title: '用户管理',
                    icon: 'user'
                }
            },
            {
                path: 'role',
                name: 'Role', 
                component: () => import('../views/system/role/index.vue'),
                meta: {
                    title: '角色管理',
                    icon: 'peoples'
                }
            }
        ]
    },
    // 商城管理
    {
        path: '/business',
        component: AppLayout,
        redirect: '/business/goods',
        name: 'Business',
        meta: {
            title: '商城管理',
            icon: 'shopping'
        },
        children: [
            {
                path: 'goods',
                name: 'Goods',
                component: () => import('../views/business/goods/index.vue'),
                meta: {
                    title: '商品管理',
                    icon: 'goods'
                }
            },
            {
                path: 'order',
                name: 'Order',
                component: () => import('../views/business/order/index.vue'),
                meta: {
                    title: '订单管理',
                    icon: 'order'
                }
            },
            {
                path: 'user',
                name: 'BusinessUser',
                component: () => import('../views/business/user/index.vue'),
                meta: {
                    title: '用户管理',
                    icon: 'user'
                }
            }
        ]
    },
    // 404 页面
    {
        path: '/404',
        name: '404',
        component: () => import('../views/error-page/404.vue'),
        meta: { hidden: true }
    },
    // 捕获所有未匹配的路由
    {
        path: '/:pathMatch(.*)*',
        redirect: '/404',
        meta: { hidden: true }
    }
]

const router = createRouter({
    history: createWebHistory(),
    routes
})

export default router
