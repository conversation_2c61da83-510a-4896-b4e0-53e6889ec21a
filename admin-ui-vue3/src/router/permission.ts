import type { Router } from 'vue-router'

/**
 * 路由权限守卫示例，可根据实际业务扩展
 */
export function setupPermissionGuard(router: Router) {
  router.beforeEach((to, from, next) => {
    // 示例：未登录跳转到登录页
    // const isAuthenticated = Boolean(localStorage.getItem('token'))
    // if (to.meta.requiresAuth && !isAuthenticated) {
    //   next({ name: 'Login' })
    // } else {
    //   next()
    // }
    next()
  })
}
