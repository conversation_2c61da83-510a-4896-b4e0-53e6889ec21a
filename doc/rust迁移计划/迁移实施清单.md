# DTS电商系统Rust迁移 - 实施清单

基于深度技术分析，制定具体的迁移实施清单，确保每个步骤都有明确的交付物和验收标准。

---

## 阶段一：基础设施搭建 (Week 1-2)

### Week 1: 项目初始化

- [x] **任务1.1**: 创建迁移方案文档
  - 交付物: `tzw-shop-rust-migration-plan.md`
  - 验收标准: 技术选型完成，架构设计明确

- [ ] **任务1.2**: 创建Rust项目结构

  ```bash
  cargo new tzw-shop-rust --bin
  cd tzw-shop-rust
  mkdir -p {src/{config,models,handlers/{admin,wx},services,repositories,middleware,utils,external,jobs},tests/{integration,unit},docs,benchmarks,docker,scripts,migrations}
  ```

  - 交付物: 完整项目目录结构
  - 验收标准: `cargo build` 成功编译

- [ ] **任务1.3**: 配置核心依赖

  ```toml
  # 关键依赖清单
  actix-web = "4.8"
  tokio = { version = "1.35", features = ["full"] }
  sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "mysql", "chrono", "uuid"] }
  serde = { version = "1.0", features = ["derive"] }
  ```

  - 交付物: `Cargo.toml` 配置完成
  - 验收标准: 所有依赖下载成功，项目编译通过

- [ ] **任务1.4**: 建立开发环境

  ```bash
  # 安装开发工具
  cargo install cargo-watch sqlx-cli
  rustup component add clippy rustfmt
  ```

  - 交付物: 开发工具链配置完成
  - 验收标准: 代码格式化、静态检查工具正常工作

### Week 2: 核心基础设施

- [ ] **任务2.1**: 配置管理模块

  ```rust
  // src/config/mod.rs
  pub mod app;
  pub mod database;
  pub mod redis;
  
  pub use app::AppConfig;
  pub use database::DatabaseConfig;
  pub use redis::RedisConfig;
  ```

  - 交付物: 配置管理模块 `src/config/`
  - 验收标准: 支持环境变量、配置文件加载

- [ ] **任务2.2**: 数据库连接池

  ```rust
  // src/config/database.rs
  pub async fn create_mysql_pool(config: &DatabaseConfig) -> Result<MySqlPool>
  ```

  - 交付物: 数据库连接池实现
  - 验收标准: 连接池正常工作，支持健康检查

- [ ] **任务2.3**: Redis连接池

  ```rust
  // src/config/redis.rs
  pub async fn create_redis_pool(config: &RedisConfig) -> Result<RedisPool>
  ```

  - 交付物: Redis连接池实现
  - 验收标准: Redis操作正常，支持连接重试

- [ ] **任务2.4**: 错误处理框架

  ```rust
  // src/error.rs
  #[derive(thiserror::Error, Debug)]
  pub enum AppError {
      #[error("数据库错误: {0}")]
      Database(#[from] sqlx::Error),
      // ...
  }
  ```

  - 交付物: 统一错误处理系统
  - 验收标准: 所有错误类型有对应处理，HTTP状态码正确

- [ ] **任务2.5**: 日志系统配置

  ```rust
  // src/main.rs
  use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
  
  fn init_tracing() {
      tracing_subscriber::registry()
          .with(tracing_subscriber::fmt::layer())
          .with(tracing_subscriber::EnvFilter::from_default_env())
          .init();
  }
  ```

  - 交付物: 结构化日志系统
  - 验收标准: 日志输出格式正确，支持级别过滤

---

## 阶段二：数据模型迁移 (Week 3-4)

### Week 3: 数据库表分析与模型定义

- [ ] **任务3.1**: 分析现有数据库表结构

  ```sql
  -- 生成表结构文档
  SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
  FROM INFORMATION_SCHEMA.COLUMNS 
  WHERE TABLE_SCHEMA = 'dts_shop'
  ORDER BY TABLE_NAME, ORDINAL_POSITION;
  ```

  - 交付物: 数据库表结构分析文档
  - 验收标准: 所有47个表的字段类型、约束关系清晰

- [ ] **任务3.2**: 定义核心数据模型

  ```rust
  // src/models/user.rs
  #[derive(Debug, Clone, sqlx::FromRow, serde::Serialize, serde::Deserialize)]
  pub struct User {
      pub id: i32,
      pub username: String,
      pub mobile: String,
      // ...
  }
  ```

  - 交付物: 用户、商品、订单等核心模型
  - 验收标准: 所有字段类型正确，支持序列化

- [ ] **任务3.3**: 实现订单相关模型

  ```rust
  // src/models/order.rs
  #[derive(Debug, Clone, sqlx::FromRow)]
  pub struct Order {
      pub id: i32,
      pub user_id: i32,
      pub order_sn: String,
      pub order_status: OrderStatus,
      // ...
  }
  
  #[derive(Debug, Clone, sqlx::Type)]
  #[repr(i16)]
  pub enum OrderStatus {
      Unpaid = 101,
      Paid = 201,
      // ...
  }
  ```

  - 交付物: 订单模型与状态枚举
  - 验收标准: 状态流转逻辑正确

- [ ] **任务3.4**: 商品模型实现

  ```rust
  // src/models/goods.rs
  #[derive(Debug, Clone, sqlx::FromRow)]
  pub struct Goods {
      pub id: i32,
      pub goods_sn: String,
      pub name: String,
      pub category_id: i32,
      pub retail_price: Decimal,
      // ...
  }
  ```

  - 交付物: 商品、分类、规格等模型
  - 验收标准: 支持价格计算，规格组合

### Week 4: 数据访问层实现

- [ ] **任务4.1**: 基础仓储抽象

  ```rust
  // src/repositories/base_repo.rs
  #[async_trait]
  pub trait Repository<T> {
      async fn find_by_id(&self, id: i32) -> Result<Option<T>>;
      async fn create(&self, entity: &T) -> Result<i32>;
      async fn update(&self, entity: &T) -> Result<()>;
      async fn delete(&self, id: i32) -> Result<()>;
  }
  ```

  - 交付物: 通用仓储接口
  - 验收标准: CRUD操作接口统一

- [ ] **任务4.2**: 用户仓储实现

  ```rust
  // src/repositories/user_repo.rs
  pub struct UserRepository {
      pool: MySqlPool,
  }
  
  impl UserRepository {
      pub async fn find_by_mobile(&self, mobile: &str) -> Result<Option<User>> {
          sqlx::query_as!(
              User,
              "SELECT * FROM dts_user WHERE mobile = ? AND deleted = 0",
              mobile
          )
          .fetch_optional(&self.pool)
          .await
          .map_err(Into::into)
      }
  }
  ```

  - 交付物: 用户数据访问层
  - 验收标准: 支持按手机号、用户名查询

- [ ] **任务4.3**: 商品仓储实现

  ```rust
  // src/repositories/goods_repo.rs
  impl GoodsRepository {
      pub async fn search_goods(
          &self,
          keyword: Option<&str>,
          category_id: Option<i32>,
          page: u32,
          size: u32,
      ) -> Result<PageResult<Goods>> {
          // 动态SQL构建
          let mut query = QueryBuilder::new("SELECT * FROM dts_goods WHERE deleted = 0");
          
          if let Some(kw) = keyword {
              query.push(" AND name LIKE ").push_bind(format!("%{}%", kw));
          }
          
          if let Some(cid) = category_id {
              query.push(" AND category_id = ").push_bind(cid);
          }
          
          query.push(" ORDER BY sort_order ASC, id DESC");
          query.push(" LIMIT ").push_bind(size);
          query.push(" OFFSET ").push_bind((page - 1) * size);
          
          let goods = query.build_query_as::<Goods>()
              .fetch_all(&self.pool)
              .await?;
              
          // 获取总数用于分页
          let total = self.count_goods(keyword, category_id).await?;
          
          Ok(PageResult::new(goods, page, size, total))
      }
  }
  ```

  - 交付物: 商品搜索与分页
  - 验收标准: 支持关键字搜索、分类筛选、分页

- [ ] **任务4.4**: 订单仓储实现

  ```rust
  // src/repositories/order_repo.rs
  impl OrderRepository {
      pub async fn find_user_orders(
          &self,
          user_id: i32,
          status: Option<OrderStatus>,
          page: u32,
          size: u32,
      ) -> Result<PageResult<OrderWithGoods>> {
          // 联表查询订单和商品信息
          let orders = sqlx::query_as!(
              OrderWithGoods,
              r#"
              SELECT 
                  o.*,
                  JSON_ARRAYAGG(
                      JSON_OBJECT(
                          'id', og.id,
                          'goods_id', og.goods_id,
                          'goods_name', og.goods_name,
                          'number', og.number,
                          'price', og.price
                      )
                  ) as goods
              FROM dts_order o
              LEFT JOIN dts_order_goods og ON o.id = og.order_id
              WHERE o.user_id = ? AND o.deleted = 0
              GROUP BY o.id
              ORDER BY o.add_time DESC
              LIMIT ? OFFSET ?
              "#,
              user_id,
              size,
              (page - 1) * size
          )
          .fetch_all(&self.pool)
          .await?;
          
          Ok(PageResult::new(orders, page, size, total))
      }
  }
  ```

  - 交付物: 订单查询与统计
  - 验收标准: 支持状态筛选、商品信息联查

- [ ] **任务4.5**: 单元测试编写

  ```rust
  // tests/unit/repositories/user_repo_test.rs
  #[tokio::test]
  async fn test_create_user() {
      let pool = create_test_pool().await;
      let repo = UserRepository::new(pool);
      
      let user = User {
          username: "test_user".to_string(),
          mobile: "13800138000".to_string(),
          // ...
      };
      
      let user_id = repo.create(&user).await.unwrap();
      assert!(user_id > 0);
      
      let found_user = repo.find_by_id(user_id).await.unwrap().unwrap();
      assert_eq!(found_user.username, user.username);
  }
  ```

  - 交付物: 数据层单元测试
  - 验收标准: 测试覆盖率 >80%

---

## 阶段三：核心业务逻辑 (Week 5-8)

### Week 5: 用户认证系统

- [ ] **任务5.1**: JWT中间件实现

  ```rust
  // src/middleware/auth.rs
  pub struct JwtMiddleware {
      secret: String,
  }
  
  impl JwtMiddleware {
      pub fn new(secret: String) -> Self {
          Self { secret }
      }
      
      pub async fn verify_token(&self, token: &str) -> Result<Claims> {
          // JWT验证逻辑
      }
  }
  ```

  - 交付物: JWT认证中间件
  - 验收标准: 支持令牌生成、验证、刷新

- [ ] **任务5.2**: 权限控制系统

  ```rust
  // src/middleware/permission.rs
  #[proc_macro_attribute]
  pub fn require_permission(args: TokenStream, input: TokenStream) -> TokenStream {
      // 权限检查宏实现
  }
  
  // 使用示例
  #[web::get("/admin/users")]
  #[require_permission("admin:user:list")]
  pub async fn list_users() -> Result<HttpResponse> {
      // ...
  }
  ```

  - 交付物: 权限注解系统
  - 验收标准: 支持角色权限控制，与现有Shiro兼容

- [ ] **任务5.3**: 用户服务层

  ```rust
  // src/services/user_service.rs
  pub struct UserService {
      user_repo: UserRepository,
      permission_cache: PermissionCache,
      jwt_middleware: JwtMiddleware,
  }
  
  impl UserService {
      pub async fn authenticate(&self, username: &str, password: &str) -> Result<TokenPair> {
          // 1. 验证用户名密码
          let user = self.user_repo.find_by_username(username).await?
              .ok_or(AppError::UserNotFound)?;
              
          if !self.verify_password(password, &user.password)? {
              return Err(AppError::InvalidCredentials);
          }
          
          // 2. 获取用户权限
          let permissions = self.permission_cache.get_user_permissions(user.id).await?;
          
          // 3. 生成JWT令牌
          let token_pair = self.jwt_middleware.create_token_pair(user.id, permissions)?;
          
          Ok(token_pair)
      }
  }
  ```

  - 交付物: 用户认证服务
  - 验收标准: 支持密码验证、权限加载、令牌生成

- [ ] **任务5.4**: 限流防护

  ```rust
  // src/middleware/rate_limit.rs
  pub struct RateLimiter {
      redis_pool: RedisPool,
      max_requests: u32,
      window_seconds: u64,
  }
  
  impl RateLimiter {
      pub async fn check_limit(&self, key: &str) -> Result<bool> {
          // 滑动窗口限流算法
          let mut conn = self.redis_pool.get().await?;
          
          let lua_script = r#"
              local key = KEYS[1]
              local window = tonumber(ARGV[1])
              local limit = tonumber(ARGV[2])
              local now = tonumber(ARGV[3])
              
              -- 清理过期记录
              redis.call('ZREMRANGEBYSCORE', key, 0, now - window)
              
              -- 获取当前计数
              local current = redis.call('ZCARD', key)
              
              if current < limit then
                  -- 添加当前请求
                  redis.call('ZADD', key, now, now)
                  redis.call('EXPIRE', key, window)
                  return 1
              else
                  return 0
              end
          "#;
          
          let allowed: i32 = redis::Script::new(lua_script)
              .key(key)
              .arg(self.window_seconds)
              .arg(self.max_requests)
              .arg(chrono::Utc::now().timestamp())
              .invoke_async(&mut *conn)
              .await?;
              
          Ok(allowed == 1)
      }
  }
  ```

  - 交付物: 限流中间件
  - 验收标准: 支持IP限流、用户限流、接口限流

### Week 6: 商品管理模块

- [ ] **任务6.1**: 商品服务层

  ```rust
  // src/services/goods_service.rs
  pub struct GoodsService {
      goods_repo: GoodsRepository,
      cache_service: GoodsCacheService,
  }
  
  impl GoodsService {
      pub async fn get_goods_detail(&self, goods_id: i32) -> Result<GoodsDetail> {
          // 1. 尝试从缓存获取
          if let Some(cached) = self.cache_service.get(&goods_id.to_string()).await? {
              return Ok(cached);
          }
          
          // 2. 从数据库查询
          let goods = self.goods_repo.find_by_id(goods_id).await?
              .ok_or(AppError::GoodsNotFound)?;
              
          let specifications = self.goods_repo.find_specifications(goods_id).await?;
          let products = self.goods_repo.find_products(goods_id).await?;
          let attributes = self.goods_repo.find_attributes(goods_id).await?;
          
          let detail = GoodsDetail {
              goods,
              specifications,
              products,
              attributes,
          };
          
          // 3. 写入缓存
          self.cache_service.set(&goods_id.to_string(), &detail).await?;
          
          Ok(detail)
      }
  }
  ```

  - 交付物: 商品查询服务
  - 验收标准: 支持缓存、规格组合、价格计算

- [ ] **任务6.2**: 商品分类管理

  ```rust
  // src/services/category_service.rs
  impl CategoryService {
      pub async fn get_category_tree(&self) -> Result<Vec<CategoryTree>> {
          let categories = self.category_repo.find_all().await?;
          
          // 构建树形结构
          let mut tree = Vec::new();
          let mut map: HashMap<i32, Vec<Category>> = HashMap::new();
          
          // 按父级分组
          for category in categories {
              map.entry(category.pid).or_default().push(category);
          }
          
          // 递归构建树
          fn build_tree(
              map: &HashMap<i32, Vec<Category>>,
              pid: i32,
          ) -> Vec<CategoryTree> {
              map.get(&pid).map_or(Vec::new(), |children| {
                  children.iter().map(|cat| CategoryTree {
                      id: cat.id,
                      name: cat.name.clone(),
                      icon_url: cat.icon_url.clone(),
                      children: build_tree(map, cat.id),
                  }).collect()
              })
          }
          
          tree = build_tree(&map, 0);
          Ok(tree)
      }
  }
  ```

  - 交付物: 分类树形结构
  - 验收标准: 支持无限级分类、懒加载

- [ ] **任务6.3**: 商品搜索优化

  ```rust
  // src/services/search_service.rs
  pub struct SearchService {
      goods_repo: GoodsRepository,
      redis_pool: RedisPool,
  }
  
  impl SearchService {
      pub async fn search_goods(&self, query: &SearchQuery) -> Result<SearchResult> {
          // 1. 构建搜索条件
          let mut conditions = Vec::new();
          let mut params = Vec::new();
          
          if let Some(keyword) = &query.keyword {
              conditions.push("(name LIKE ? OR keywords LIKE ?)");
              let pattern = format!("%{}%", keyword);
              params.push(pattern.clone());
              params.push(pattern);
              
              // 记录搜索历史
              self.record_search_history(query.user_id, keyword).await?;
          }
          
          if let Some(category_id) = query.category_id {
              conditions.push("category_id = ?");
              params.push(category_id.to_string());
          }
          
          // 2. 价格区间筛选
          if let Some(min_price) = query.min_price {
              conditions.push("retail_price >= ?");
              params.push(min_price.to_string());
          }
          
          if let Some(max_price) = query.max_price {
              conditions.push("retail_price <= ?");
              params.push(max_price.to_string());
          }
          
          // 3. 执行搜索
          let goods = self.goods_repo.search_with_conditions(
              &conditions.join(" AND "),
              &params,
              query.page,
              query.size,
              &query.sort_by,
          ).await?;
          
          Ok(SearchResult {
              goods,
              total: goods.len() as u64,
              aggregations: self.build_aggregations(&query).await?,
          })
      }
  }
  ```

  - 交付物: 商品搜索服务
  - 验收标准: 支持关键字、价格、分类筛选

### Week 7: 订单处理流程

- [ ] **任务7.1**: 购物车服务

  ```rust
  // src/services/cart_service.rs
  pub struct CartService {
      cart_repo: CartRepository,
      goods_service: GoodsService,
      redis_pool: RedisPool,
  }
  
  impl CartService {
      pub async fn add_to_cart(&self, request: AddToCartRequest) -> Result<()> {
          // 1. 验证商品信息
          let goods = self.goods_service.get_goods_detail(request.goods_id).await?;
          let product = goods.products.iter()
              .find(|p| p.id == request.product_id)
              .ok_or(AppError::ProductNotFound)?;
              
          // 2. 检查库存
          if product.number < request.number {
              return Err(AppError::InsufficientStock);
          }
          
          // 3. 检查是否已存在
          if let Some(existing) = self.cart_repo
              .find_by_user_and_product(request.user_id, request.product_id).await? {
              // 更新数量
              self.cart_repo.update_quantity(
                  existing.id,
                  existing.number + request.number,
              ).await?;
          } else {
              // 新增购物车项
              let cart_item = CartItem {
                  user_id: request.user_id,
                  goods_id: request.goods_id,
                  product_id: request.product_id,
                  number: request.number,
                  price: product.price,
                  specifications: request.specifications,
                  // ...
              };
              
              self.cart_repo.create(&cart_item).await?;
          }
          
          // 4. 清除购物车缓存
          self.clear_cart_cache(request.user_id).await?;
          
          Ok(())
      }
  }
  ```

  - 交付物: 购物车管理服务
  - 验收标准: 支持添加、删除、数量修改

- [ ] **任务7.2**: 订单创建服务

  ```rust
  // src/services/order_service.rs
  impl OrderService {
      pub async fn create_order(&self, request: CreateOrderRequest) -> Result<Order> {
          let mut tx = self.db_pool.begin().await?;
          
          // 1. 获取购物车商品
          let cart_items = self.cart_repo
              .find_checked_items(request.user_id).await?;
              
          if cart_items.is_empty() {
              return Err(AppError::EmptyCart);
          }
          
          // 2. 计算订单金额
          let mut goods_price = Decimal::ZERO;
          let mut order_goods = Vec::new();
          
          for item in &cart_items {
              // 重新获取商品价格（防止篡改）
              let goods = self.goods_service.get_goods_detail(item.goods_id).await?;
              let product = goods.find_product(item.product_id)?;
              
              // 检查库存并锁定
              let affected = sqlx::query!(
                  "UPDATE dts_goods_product 
                   SET number = number - ? 
                   WHERE id = ? AND number >= ?",
                  item.number,
                  item.product_id,
                  item.number
              )
              .execute(&mut *tx)
              .await?;
              
              if affected.rows_affected() == 0 {
                  return Err(AppError::InsufficientStock);
              }
              
              goods_price += product.price * Decimal::from(item.number);
              
              order_goods.push(OrderGoods {
                  goods_id: item.goods_id,
                  goods_name: goods.goods.name.clone(),
                  product_id: item.product_id,
                  number: item.number,
                  price: product.price,
                  specifications: item.specifications.clone(),
                  // ...
              });
          }
          
          // 3. 应用优惠券
          let coupon_price = if let Some(coupon_id) = request.coupon_id {
              self.apply_coupon(request.user_id, coupon_id, goods_price, &mut tx).await?
          } else {
              Decimal::ZERO
          };
          
          // 4. 计算运费
          let freight_price = self.calculate_freight(
              &request.address,
              &order_goods,
          ).await?;
          
          // 5. 创建订单
          let order_price = goods_price + freight_price - coupon_price;
          let order = Order {
              user_id: request.user_id,
              order_sn: self.generate_order_sn().await?,
              order_status: OrderStatus::Unpaid,
              goods_price,
              freight_price,
              coupon_price,
              order_price,
              actual_price: order_price, // 暂不支持积分抵扣
              consignee: request.address.name.clone(),
              mobile: request.address.mobile.clone(),
              address: request.address.full_address(),
              message: request.message.unwrap_or_default(),
              add_time: Utc::now(),
              // ...
          };
          
          let order_id = sqlx::query!(
              "INSERT INTO dts_order (...) VALUES (...)",
              // 订单字段值
          )
          .execute(&mut *tx)
          .await?
          .last_insert_id();
          
          // 6. 创建订单商品
          for goods in order_goods {
              sqlx::query!(
                  "INSERT INTO dts_order_goods (...) VALUES (...)",
                  order_id,
                  // 商品字段值
              )
              .execute(&mut *tx)
              .await?;
          }
          
          // 7. 清空购物车
          sqlx::query!(
              "DELETE FROM dts_cart WHERE user_id = ? AND checked = 1",
              request.user_id
          )
          .execute(&mut *tx)
          .await?;
          
          tx.commit().await?;
          
          // 8. 异步处理
          tokio::spawn(async move {
              // 发送订单创建通知
              // 更新库存统计
              // 计算分销佣金
          });
          
          Ok(order)
      }
  }
  ```

  - 交付物: 订单创建服务
  - 验收标准: 支持库存扣减、优惠券、运费计算

### Week 8: 支付集成

- [ ] **任务8.1**: 微信支付集成

  ```rust
  // src/external/wechat.rs
  pub struct WechatPayService {
      app_id: String,
      mch_id: String,
      api_key: String,
      client: reqwest::Client,
  }
  
  impl WechatPayService {
      pub async fn create_prepay_order(&self, order: &Order) -> Result<PrepayResponse> {
          let request = PrepayRequest {
              appid: self.app_id.clone(),
              mch_id: self.mch_id.clone(),
              out_trade_no: order.order_sn.clone(),
              total_fee: (order.actual_price * Decimal::from(100)).to_i32().unwrap(),
              body: format!("订单-{}", order.order_sn),
              notify_url: self.get_notify_url(),
              trade_type: "JSAPI".to_string(),
              openid: order.user.weixin_openid.clone(),
              // ...
          };
          
          let xml_data = self.build_xml_request(&request)?;
          let signed_xml = self.sign_request(&xml_data)?;
          
          let response = self.client
              .post("https://api.mch.weixin.qq.com/pay/unifiedorder")
              .body(signed_xml)
              .send()
              .await?;
              
          let response_xml = response.text().await?;
          let prepay_response = self.parse_xml_response::<PrepayResponse>(&response_xml)?;
          
          if prepay_response.return_code != "SUCCESS" {
              return Err(AppError::WechatPayError(prepay_response.return_msg));
          }
          
          Ok(prepay_response)
      }
      
      pub async fn handle_payment_notify(&self, xml_data: &str) -> Result<PaymentNotify> {
          // 1. 验证签名
          let notify = self.parse_xml_response::<PaymentNotify>(xml_data)?;
          if !self.verify_signature(&notify)? {
              return Err(AppError::InvalidSignature);
          }
          
          // 2. 验证订单状态
          if notify.result_code != "SUCCESS" {
              return Err(AppError::PaymentFailed(notify.err_code_des));
          }
          
          // 3. 更新订单状态
          let order = self.order_service
              .find_by_order_sn(&notify.out_trade_no).await?
              .ok_or(AppError::OrderNotFound)?;
              
          if order.order_status == OrderStatus::Paid {
              return Ok(notify); // 重复通知，直接返回成功
          }
          
          self.order_service.update_payment_status(
              order.id,
              OrderStatus::Paid,
              &notify.transaction_id,
              Utc::now(),
          ).await?;
          
          // 4. 异步处理后续逻辑
          tokio::spawn(async move {
              // 发送支付成功通知
              // 触发发货流程
              // 计算分销佣金
          });
          
          Ok(notify)
      }
  }
  ```

  - 交付物: 微信支付服务
  - 验收标准: 支持下单、支付、回调处理

- [ ] **任务8.2**: 分销佣金计算

  ```rust
  // src/services/commission_service.rs
  pub struct CommissionService {
      commission_repo: CommissionRepository,
      user_relation_repo: UserRelationRepository,
      order_repo: OrderRepository,
  }
  
  impl CommissionService {
      pub async fn calculate_order_commission(&self, order_id: i32) -> Result<()> {
          let order = self.order_repo.find_by_id(order_id).await?
              .ok_or(AppError::OrderNotFound)?;
              
          // 获取分销关系链
          let relation_chain = self.user_relation_repo
              .get_parent_chain(order.user_id, 3).await?; // 最多3级
              
          if relation_chain.is_empty() {
              return Ok(()); // 无分销关系
          }
          
          let order_goods = self.order_repo.find_order_goods(order_id).await?;
          
          for (level, parent_user_id) in relation_chain.iter().enumerate() {
              let mut total_commission = Decimal::ZERO;
              
              for goods in &order_goods {
                  // 获取商品佣金规则
                  let commission_rule = self.commission_repo
                      .find_goods_commission_rule(goods.goods_id).await?;
                      
                  let commission_rate = match level {
                      0 => commission_rule.level1_rate, // 一级分销商
                      1 => commission_rule.level2_rate, // 二级分销商
                      2 => commission_rule.level3_rate, // 三级分销商
                      _ => continue,
                  };
                  
                  let commission_amount = goods.price 
                      * Decimal::from(goods.number) 
                      * commission_rate 
                      / Decimal::from(100);
                      
                  total_commission += commission_amount;
              }
              
              if total_commission > Decimal::ZERO {
                  // 创建佣金记录
                  let commission = OrderCommission {
                      order_id,
                      user_id: *parent_user_id,
                      commission_level: level as i16 + 1,
                      commission_amount: total_commission,
                      status: CommissionStatus::Pending,
                      add_time: Utc::now(),
                      // ...
                  };
                  
                  self.commission_repo.create(&commission).await?;
              }
          }
          
          Ok(())
      }
  }
  ```

  - 交付物: 分销佣金服务
  - 验收标准: 支持多级分销、佣金计算

---

## 阶段四：API接口实现 (Week 9-12)

### Week 9: 管理后台API

- [ ] **任务9.1**: 管理员认证接口

  ```rust
  // src/handlers/admin/auth.rs
  #[web::post("/admin/auth/login")]
  pub async fn admin_login(
      request: web::Json<AdminLoginRequest>,
      auth_service: web::Data<AdminAuthService>,
      rate_limiter: web::Data<RateLimiter>,
  ) -> Result<HttpResponse> {
      // IP限流检查
      let client_ip = get_client_ip(&req)?;
      if !rate_limiter.check_limit(&format!("admin_login:{}", client_ip)).await? {
          return Err(AppError::RateLimitExceeded);
      }
      
      // 管理员认证
      let token_pair = auth_service
          .authenticate(&request.username, &request.password).await?;
          
      Ok(HttpResponse::Ok().json(AdminLoginResponse {
          access_token: token_pair.access_token,
          refresh_token: token_pair.refresh_token,
          expires_in: 900,
      }))
  }
  ```

  - 交付物: 管理员登录接口
  - 验收标准: 支持JWT认证、限流保护

- [ ] **任务9.2**: 用户管理接口

  ```rust
  // src/handlers/admin/user.rs
  #[web::get("/admin/users")]
  #[require_permission("admin:user:list")]
  pub async fn list_users(
      query: web::Query<UserListQuery>,
      user_service: web::Data<UserService>,
  ) -> Result<HttpResponse> {
      let users = user_service.list_users(&query).await?;
      Ok(HttpResponse::Ok().json(ApiResponse::success(users)))
  }
  
  #[web::post("/admin/users/{id}/status")]
  #[require_permission("admin:user:edit")]
  pub async fn update_user_status(
      path: web::Path<i32>,
      request: web::Json<UpdateStatusRequest>,
      user_service: web::Data<UserService>,
  ) -> Result<HttpResponse> {
      let user_id = path.into_inner();
      user_service.update_status(user_id, request.status).await?;
      Ok(HttpResponse::Ok().json(ApiResponse::success(())))
  }
  ```

  - 交付物: 用户管理接口
  - 验收标准: 支持用户列表、状态修改

- [ ] **任务9.3**: 订单管理接口

  ```rust
  // src/handlers/admin/order.rs
  #[web::get("/admin/orders")]
  #[require_permission("admin:order:list")]
  pub async fn list_orders(
      query: web::Query<OrderListQuery>,
      order_service: web::Data<OrderService>,
  ) -> Result<HttpResponse> {
      let orders = order_service.admin_list_orders(&query).await?;
      Ok(HttpResponse::Ok().json(ApiResponse::success(orders)))
  }
  
  #[web::post("/admin/orders/{id}/ship")]
  #[require_permission("admin:order:ship")]
  pub async fn ship_order(
      path: web::Path<i32>,
      request: web::Json<ShipOrderRequest>,
      order_service: web::Data<OrderService>,
  ) -> Result<HttpResponse> {
      let order_id = path.into_inner();
      order_service.ship_order(order_id, &request).await?;
      Ok(HttpResponse::Ok().json(ApiResponse::success(())))
  }
  ```

  - 交付物: 订单管理接口
  - 验收标准: 支持订单查询、发货操作

### Week 10: 微信小程序API

- [ ] **任务10.1**: 微信登录接口

  ```rust
  // src/handlers/wx/auth.rs
  #[web::post("/wx/auth/login")]
  pub async fn wx_login(
      request: web::Json<WxLoginRequest>,
      wx_service: web::Data<WxAuthService>,
  ) -> Result<HttpResponse> {
      // 1. 向微信服务器验证code
      let session_info = wx_service.code2session(&request.js_code).await?;
      
      // 2. 查找或创建用户
      let user = wx_service.find_or_create_user(
          &session_info.openid,
          &request.user_info,
      ).await?;
      
      // 3. 生成JWT令牌
      let token_pair = wx_service.create_user_token(user.id).await?;
      
      Ok(HttpResponse::Ok().json(WxLoginResponse {
          access_token: token_pair.access_token,
          refresh_token: token_pair.refresh_token,
          user_info: UserInfo::from(user),
      }))
  }
  ```

  - 交付物: 微信登录接口
  - 验收标准: 支持code2session、用户信息获取

- [ ] **任务10.2**: 商品相关接口

  ```rust
  // src/handlers/wx/goods.rs
  #[web::get("/wx/goods")]
  pub async fn list_goods(
      query: web::Query<GoodsListQuery>,
      goods_service: web::Data<GoodsService>,
  ) -> Result<HttpResponse> {
      let goods = goods_service.list_goods(&query).await?;
      Ok(HttpResponse::Ok().json(ApiResponse::success(goods)))
  }
  
  #[web::get("/wx/goods/{id}")]
  pub async fn get_goods_detail(
      path: web::Path<i32>,
      user: Option<UserContext>,
      goods_service: web::Data<GoodsService>,
      footprint_service: web::Data<FootprintService>,
  ) -> Result<HttpResponse> {
      let goods_id = path.into_inner();
      
      // 记录浏览足迹
      if let Some(user_ctx) = user {
          tokio::spawn(async move {
              let _ = footprint_service.record_footprint(user_ctx.user_id, goods_id).await;
          });
      }
      
      let detail = goods_service.get_goods_detail(goods_id).await?;
      Ok(HttpResponse::Ok().json(ApiResponse::success(detail)))
  }
  ```

  - 交付物: 商品浏览接口
  - 验收标准: 支持商品列表、详情、足迹记录

- [ ] **任务10.3**: 购物车接口

  ```rust
  // src/handlers/wx/cart.rs
  #[web::post("/wx/cart/add")]
  pub async fn add_to_cart(
      request: web::Json<AddToCartRequest>,
      user: UserContext,
      cart_service: web::Data<CartService>,
  ) -> Result<HttpResponse> {
      let mut req = request.into_inner();
      req.user_id = user.user_id;
      
      cart_service.add_to_cart(req).await?;
      Ok(HttpResponse::Ok().json(ApiResponse::success(())))
  }
  
  #[web::get("/wx/cart")]
  pub async fn get_cart_items(
      user: UserContext,
      cart_service: web::Data<CartService>,
  ) -> Result<HttpResponse> {
      let items = cart_service.get_user_cart(user.user_id).await?;
      Ok(HttpResponse::Ok().json(ApiResponse::success(items)))
  }
  ```

  - 交付物: 购物车接口
  - 验收标准: 支持添加、查询、修改购物车

### Week 11: 订单相关接口

- [ ] **任务11.1**: 订单创建接口

  ```rust
  // src/handlers/wx/order.rs
  #[web::post("/wx/orders")]
  pub async fn create_order(
      request: web::Json<CreateOrderRequest>,
      user: UserContext,
      order_service: web::Data<OrderService>,
  ) -> Result<HttpResponse> {
      let mut req = request.into_inner();
      req.user_id = user.user_id;
      
      let order = order_service.create_order(req).await?;
      Ok(HttpResponse::Ok().json(ApiResponse::success(order)))
  }
  
  #[web::post("/wx/orders/{id}/pay")]
  pub async fn prepare_payment(
      path: web::Path<i32>,
      user: UserContext,
      payment_service: web::Data<PaymentService>,
  ) -> Result<HttpResponse> {
      let order_id = path.into_inner();
      let prepay_info = payment_service.create_prepay_order(user.user_id, order_id).await?;
      Ok(HttpResponse::Ok().json(ApiResponse::success(prepay_info)))
  }
  ```

  - 交付物: 订单创建与支付接口
  - 验收标准: 支持订单创建、支付预下单

- [ ] **任务11.2**: 订单查询接口

  ```rust
  #[web::get("/wx/orders")]
  pub async fn list_user_orders(
      query: web::Query<UserOrderQuery>,
      user: UserContext,
      order_service: web::Data<OrderService>,
  ) -> Result<HttpResponse> {
      let mut query = query.into_inner();
      query.user_id = Some(user.user_id);
      
      let orders = order_service.list_user_orders(&query).await?;
      Ok(HttpResponse::Ok().json(ApiResponse::success(orders)))
  }
  
  #[web::get("/wx/orders/{id}")]
  pub async fn get_order_detail(
      path: web::Path<i32>,
      user: UserContext,
      order_service: web::Data<OrderService>,
  ) -> Result<HttpResponse> {
      let order_id = path.into_inner();
      let order = order_service.get_user_order_detail(user.user_id, order_id).await?;
      Ok(HttpResponse::Ok().json(ApiResponse::success(order)))
  }
  ```

  - 交付物: 订单查询接口
  - 验收标准: 支持订单列表、详情查询

### Week 12: 支付回调与测试

- [ ] **任务12.1**: 支付回调处理

  ```rust
  // src/handlers/wx/payment.rs
  #[web::post("/wx/payment/notify")]
  pub async fn payment_notify(
      body: web::Bytes,
      payment_service: web::Data<PaymentService>,
  ) -> Result<HttpResponse> {
      let xml_data = String::from_utf8(body.to_vec())
          .map_err(|_| AppError::InvalidPaymentNotify)?;
          
      match payment_service.handle_payment_notify(&xml_data).await {
          Ok(_) => {
              // 返回微信要求的XML格式
              let response_xml = r#"
                  <xml>
                      <return_code><![CDATA[SUCCESS]]></return_code>
                      <return_msg><![CDATA[OK]]></return_msg>
                  </xml>
              "#;
              Ok(HttpResponse::Ok()
                  .content_type("application/xml")
                  .body(response_xml))
          }
          Err(e) => {
              tracing::error!("Payment notify error: {:?}", e);
              let response_xml = r#"
                  <xml>
                      <return_code><![CDATA[FAIL]]></return_code>
                      <return_msg><![CDATA[处理失败]]></return_msg>
                  </xml>
              "#;
              Ok(HttpResponse::Ok()
                  .content_type("application/xml")
                  .body(response_xml))
          }
      }
  }
  ```

  - 交付物: 支付回调接口
  - 验收标准: 支持微信支付回调处理

- [ ] **任务12.2**: API集成测试

  ```rust
  // tests/integration/api_test.rs
  use actix_web::test;
  
  #[actix_web::test]
  async fn test_user_order_flow() {
      let app = test::init_service(create_test_app().await).await;
      
      // 1. 用户登录
      let login_req = test::TestRequest::post()
          .uri("/wx/auth/login")
          .set_json(&WxLoginRequest {
              js_code: "test_code".to_string(),
              user_info: create_test_user_info(),
          })
          .to_request();
          
      let login_resp = test::call_service(&app, login_req).await;
      assert_eq!(login_resp.status(), 200);
      
      let login_data: WxLoginResponse = test::read_body_json(login_resp).await;
      let token = login_data.access_token;
      
      // 2. 添加商品到购物车
      let add_cart_req = test::TestRequest::post()
          .uri("/wx/cart/add")
          .insert_header(("Authorization", format!("Bearer {}", token)))
          .set_json(&AddToCartRequest {
              goods_id: 1,
              product_id: 1,
              number: 2,
              specifications: vec![],
          })
          .to_request();
          
      let add_cart_resp = test::call_service(&app, add_cart_req).await;
      assert_eq!(add_cart_resp.status(), 200);
      
      // 3. 创建订单
      let create_order_req = test::TestRequest::post()
          .uri("/wx/orders")
          .insert_header(("Authorization", format!("Bearer {}", token)))
          .set_json(&CreateOrderRequest {
              address_id: 1,
              coupon_id: None,
              message: None,
          })
          .to_request();
          
      let create_order_resp = test::call_service(&app, create_order_req).await;
      assert_eq!(create_order_resp.status(), 200);
      
      // 验证订单创建成功
      let order_data: ApiResponse<Order> = test::read_body_json(create_order_resp).await;
      assert!(order_data.data.id > 0);
  }
  ```

  - 交付物: API集成测试套件
  - 验收标准: 测试覆盖主要业务流程

---

## 验收标准与质量保证

### 代码质量要求

- [ ] 代码覆盖率 ≥ 80%
- [ ] 无clippy警告
- [ ] 统一代码格式（rustfmt）
- [ ] 无unsafe代码（特殊情况除外）

### 性能要求

- [ ] API响应时间 P95 < 100ms
- [ ] 数据库连接池利用率 < 80%
- [ ] 内存使用 < 200MB（空载）
- [ ] 支持1000+ 并发用户

### 安全要求

- [ ] SQL注入防护
- [ ] XSS防护
- [ ] JWT令牌安全
- [ ] 接口限流保护
- [ ] 敏感数据加密

### 兼容性要求

- [ ] API接口向后兼容
- [ ] 数据库表结构兼容
- [ ] 现有前端无需修改
- [ ] 支持现有部署环境

---

*该实施清单将作为项目执行的详细指南，确保迁移过程的可控性和质量。*
