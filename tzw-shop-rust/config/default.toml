# TZW电商系统Rust版本 - 默认配置

[app]
name = "TZW Shop Rust"
version = "0.1.0"
host = "127.0.0.1"
port = 8080
debug = true
log_level = "info"
jwt_secret = "your-secret-key-change-in-production"
jwt_access_expires = 900  # 15分钟
jwt_refresh_expires = 604800  # 7天

[app.cors]
allowed_origins = ["*"]
allowed_headers = ["Content-Type", "Authorization", "X-Requested-With"]
allowed_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
allow_credentials = true
max_age = 3600

[app.rate_limit]
enabled = true
max_requests_per_ip = 1000
window_seconds = 3600  # 1小时
login_max_requests = 5
login_window_seconds = 300  # 5分钟

[database]
url = "mysql://root:password@localhost:3306/dts_shop"
max_connections = 20
min_connections = 5
connect_timeout = 30
idle_timeout = 600  # 10分钟
max_lifetime = 3600  # 1小时
enable_logging = true
slow_query_threshold = 1000  # 1秒

[redis]
url = "redis://127.0.0.1:6379"
max_open = 100
max_idle = 50
timeout = 30
max_lifetime = 3600  # 1小时
idle_timeout = 600   # 10分钟
database = 0
