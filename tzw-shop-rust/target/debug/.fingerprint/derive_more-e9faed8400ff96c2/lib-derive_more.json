{"rustc": 15497389221046826682, "features": "[\"as_ref\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"std\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"std\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 7165309211519594838, "profile": 9248397660552393121, "path": 180052110974371625, "deps": [[15774985133158646067, "derive_more_impl", false, 8727384727515447736]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_more-e9faed8400ff96c2/dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}