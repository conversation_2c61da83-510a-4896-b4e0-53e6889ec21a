{"rustc": 15497389221046826682, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 8276155916380437441, "path": 5829916604419308305, "deps": [[5466618496199522463, "crc32fast", false, 10884422461448786512], [7636735136738807108, "miniz_oxide", false, 15074537234785137282]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-6e664642c69d95c5/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}