{"rustc": 15497389221046826682, "features": "[\"alloc\", \"bytes\", \"futures-core-03\", \"pin-project-lite\", \"std\", \"tokio\", \"tokio-dep\", \"tokio-util\"]", "declared_features": "[\"alloc\", \"bytes\", \"bytes_05\", \"default\", \"futures-03\", \"futures-core-03\", \"futures-io-03\", \"mp4\", \"pin-project\", \"pin-project-lite\", \"regex\", \"std\", \"tokio\", \"tokio-02\", \"tokio-02-dep\", \"tokio-03\", \"tokio-03-dep\", \"tokio-dep\", \"tokio-util\"]", "target": 2090804380371586739, "profile": 8276155916380437441, "path": 1197836743604559865, "deps": [[1288403060204016458, "tokio_util", false, 12809264000560244136], [1906322745568073236, "pin_project_lite", false, 14824965517151627551], [7620660491849607393, "futures_core_03", false, 5997558050176428920], [9538054652646069845, "tokio_dep", false, 17946585307374682436], [15932120279885307830, "memchr", false, 3882521768326308528], [16066129441945555748, "bytes", false, 15715078194843767081]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/combine-1d6963101622536d/dep-lib-combine", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}