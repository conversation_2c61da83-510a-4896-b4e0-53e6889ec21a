{"$message_type":"diagnostic","message":"unused import: `HttpResponse`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/middleware/auth.rs","byte_start":201,"byte_end":213,"line_start":9,"line_end":9,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    HttpResponse,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/middleware/auth.rs","byte_start":195,"byte_end":213,"line_start":8,"line_end":9,"column_start":16,"column_end":17,"is_primary":true,"text":[{"text":"    HttpMessage,","highlight_start":16,"highlight_end":17},{"text":"    HttpResponse,","highlight_start":1,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `HttpResponse`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/middleware/auth.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    HttpResponse,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src/middleware/auth.rs","byte_start":10872,"byte_end":10888,"line_start":358,"line_end":358,"column_start":24,"column_end":40,"is_primary":true,"text":[{"text":"                    Ok(service_response)","highlight_start":24,"highlight_end":40}],"label":"expected `ServiceResponse<B>`, found `ServiceResponse`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/middleware/auth.rs","byte_start":9084,"byte_end":9085,"line_start":311,"line_end":311,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"impl<S, B> actix_web::dev::Service<ServiceRequest>","highlight_start":9,"highlight_end":10}],"label":"expected this type parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/middleware/auth.rs","byte_start":10869,"byte_end":10871,"line_start":358,"line_end":358,"column_start":21,"column_end":23,"is_primary":false,"text":[{"text":"                    Ok(service_response)","highlight_start":21,"highlight_end":23}],"label":"arguments to this enum variant are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected struct `ServiceResponse<B>`\n   found struct `ServiceResponse<BoxBody>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the type constructed contains `ServiceResponse` due to the type of the argument passed","code":null,"level":"help","spans":[{"file_name":"src/middleware/auth.rs","byte_start":10872,"byte_end":10888,"line_start":358,"line_end":358,"column_start":24,"column_end":40,"is_primary":false,"text":[{"text":"                    Ok(service_response)","highlight_start":24,"highlight_end":40}],"label":"this argument influences the type of `Ok`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/middleware/auth.rs","byte_start":10869,"byte_end":10889,"line_start":358,"line_end":358,"column_start":21,"column_end":41,"is_primary":true,"text":[{"text":"                    Ok(service_response)","highlight_start":21,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"tuple variant defined here","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.rustup/toolchains/stable-aarch64-apple-darwin/lib/rustlib/src/rust/library/core/src/result.rs","byte_start":19729,"byte_end":19731,"line_start":532,"line_end":532,"column_start":5,"column_end":7,"is_primary":true,"text":[{"text":"    Ok(#[stable(feature = \"rust1\", since = \"1.0.0\")] T),","highlight_start":5,"highlight_end":7}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/middleware/auth.rs:358:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m311\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl<S, B> actix_web::dev::Service<ServiceRequest>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected this type parameter\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m358\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    Ok(service_response)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `ServiceResponse<B>`, found `ServiceResponse`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12marguments to this enum variant are incorrect\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected struct `ServiceResponse<\u001b[0m\u001b[0m\u001b[1m\u001b[35mB\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m               found struct `ServiceResponse<\u001b[0m\u001b[0m\u001b[1m\u001b[35mBoxBody\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: the type constructed contains `ServiceResponse` due to the type of the argument passed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/middleware/auth.rs:358:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m358\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    Ok(service_response)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis argument influences the type of `Ok`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: tuple variant defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.rustup/toolchains/stable-aarch64-apple-darwin/lib/rustlib/src/rust/library/core/src/result.rs:532:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m532\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Ok(#[stable(feature = \"rust1\", since = \"1.0.0\")] T),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`match` arms have incompatible types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src/middleware/rate_limit.rs","byte_start":9088,"byte_end":9108,"line_start":296,"line_end":296,"column_start":21,"column_end":41,"is_primary":true,"text":[{"text":"                    Ok(service_response)","highlight_start":21,"highlight_end":41}],"label":"expected `Result<ServiceResponse<B>, _>`, found `Result<ServiceResponse, _>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/middleware/rate_limit.rs","byte_start":7761,"byte_end":7762,"line_start":266,"line_end":266,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"impl<S, B> actix_web::dev::Service<ServiceRequest> for IpRateLimitMiddlewareService<S>","highlight_start":9,"highlight_end":10}],"label":"expected this type parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/middleware/rate_limit.rs","byte_start":8723,"byte_end":8730,"line_start":289,"line_end":289,"column_start":21,"column_end":28,"is_primary":false,"text":[{"text":"                    Ok(res)","highlight_start":21,"highlight_end":28}],"label":"this is found to be of type `Result<ServiceResponse<B>, _>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/middleware/rate_limit.rs","byte_start":8484,"byte_end":9444,"line_start":284,"line_end":305,"column_start":13,"column_end":14,"is_primary":false,"text":[{"text":"            match rate_limiter.check_limit(&client_ip).await {","highlight_start":13,"highlight_end":63},{"text":"                Ok(true) => {","highlight_start":1,"highlight_end":30},{"text":"                    // 通过限流检查","highlight_start":1,"highlight_end":30},{"text":"                    let fut = self.service.call(req);","highlight_start":1,"highlight_end":54},{"text":"                    let res = fut.await?;","highlight_start":1,"highlight_end":42},{"text":"                    Ok(res)","highlight_start":1,"highlight_end":28},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"                Ok(false) => {","highlight_start":1,"highlight_end":31},{"text":"                    // 触发限流","highlight_start":1,"highlight_end":28},{"text":"                    let (req, _) = req.into_parts();","highlight_start":1,"highlight_end":53},{"text":"                    let response = AppError::RateLimitExceeded.error_response().map_into_boxed_body();","highlight_start":1,"highlight_end":103},{"text":"                    let service_response = actix_web::dev::ServiceResponse::new(req, response);","highlight_start":1,"highlight_end":96},{"text":"                    Ok(service_response)","highlight_start":1,"highlight_end":41},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"                Err(e) => {","highlight_start":1,"highlight_end":28},{"text":"                    // 限流检查出错，记录日志但允许请求通过","highlight_start":1,"highlight_end":42},{"text":"                    warn!(\"限流检查失败: {}\", e);","highlight_start":1,"highlight_end":44},{"text":"                    let fut = self.service.call(req);","highlight_start":1,"highlight_end":54},{"text":"                    let res = fut.await?;","highlight_start":1,"highlight_end":42},{"text":"                    Ok(res)","highlight_start":1,"highlight_end":28},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            }","highlight_start":1,"highlight_end":14}],"label":"`match` arms have incompatible types","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `Result<ServiceResponse<B>, _>`\n   found enum `Result<ServiceResponse<BoxBody>, _>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try wrapping the expression in `Err`","code":null,"level":"help","spans":[{"file_name":"src/middleware/rate_limit.rs","byte_start":9088,"byte_end":9088,"line_start":296,"line_end":296,"column_start":21,"column_end":21,"is_primary":true,"text":[{"text":"                    Ok(service_response)","highlight_start":21,"highlight_end":21}],"label":null,"suggested_replacement":"Err(","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/middleware/rate_limit.rs","byte_start":9108,"byte_end":9108,"line_start":296,"line_end":296,"column_start":41,"column_end":41,"is_primary":true,"text":[{"text":"                    Ok(service_response)","highlight_start":41,"highlight_end":41}],"label":null,"suggested_replacement":")","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: `match` arms have incompatible types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/middleware/rate_limit.rs:296:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m266\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0mimpl<S, B> actix_web::dev::Service<ServiceRequest> for IpRateLimitMiddlewareSer\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected this type parameter\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m284\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m            match rate_limiter.check_limit(&client_ip).await {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m285\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Ok(true) => {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m286\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    // 通过限流检查\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m287\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let fut = self.service.call(req);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m288\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let res = fut.await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m289\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    Ok(res)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is found to be of type `Result<ServiceResponse<B>, _>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m296\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    Ok(service_response)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Result<ServiceResponse<B>, _>`, found `Result<ServiceResponse, _>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_____________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m`match` arms have incompatible types\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected enum `Result<ServiceResponse<\u001b[0m\u001b[0m\u001b[1m\u001b[35mB\u001b[0m\u001b[0m>, \u001b[0m\u001b[0m\u001b[1m\u001b[35m_\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m               found enum `Result<ServiceResponse<\u001b[0m\u001b[0m\u001b[1m\u001b[35mBoxBody\u001b[0m\u001b[0m>, \u001b[0m\u001b[0m\u001b[1m\u001b[35m_\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: try wrapping the expression in `Err`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m296\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[38;5;10mErr(\u001b[0m\u001b[0mOk(service_response)\u001b[0m\u001b[0m\u001b[38;5;10m)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[38;5;10m++++\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`match` arms have incompatible types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src/middleware/rate_limit.rs","byte_start":12363,"byte_end":12383,"line_start":388,"line_end":388,"column_start":21,"column_end":41,"is_primary":true,"text":[{"text":"                    Ok(service_response)","highlight_start":21,"highlight_end":41}],"label":"expected `Result<ServiceResponse<B>, _>`, found `Result<ServiceResponse, _>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/middleware/rate_limit.rs","byte_start":10942,"byte_end":10943,"line_start":357,"line_end":357,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"impl<S, B> actix_web::dev::Service<ServiceRequest> for UserRateLimitMiddlewareService<S>","highlight_start":9,"highlight_end":10}],"label":"expected this type parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/middleware/rate_limit.rs","byte_start":11998,"byte_end":12005,"line_start":381,"line_end":381,"column_start":21,"column_end":28,"is_primary":false,"text":[{"text":"                    Ok(res)","highlight_start":21,"highlight_end":28}],"label":"this is found to be of type `Result<ServiceResponse<B>, _>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/middleware/rate_limit.rs","byte_start":11760,"byte_end":12725,"line_start":376,"line_end":397,"column_start":13,"column_end":14,"is_primary":false,"text":[{"text":"            match rate_limiter.check_limit(&user_key).await {","highlight_start":13,"highlight_end":62},{"text":"                Ok(true) => {","highlight_start":1,"highlight_end":30},{"text":"                    // 通过限流检查","highlight_start":1,"highlight_end":30},{"text":"                    let fut = self.service.call(req);","highlight_start":1,"highlight_end":54},{"text":"                    let res = fut.await?;","highlight_start":1,"highlight_end":42},{"text":"                    Ok(res)","highlight_start":1,"highlight_end":28},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"                Ok(false) => {","highlight_start":1,"highlight_end":31},{"text":"                    // 触发限流","highlight_start":1,"highlight_end":28},{"text":"                    let (req, _) = req.into_parts();","highlight_start":1,"highlight_end":53},{"text":"                    let response = AppError::RateLimitExceeded.error_response().map_into_boxed_body();","highlight_start":1,"highlight_end":103},{"text":"                    let service_response = actix_web::dev::ServiceResponse::new(req, response);","highlight_start":1,"highlight_end":96},{"text":"                    Ok(service_response)","highlight_start":1,"highlight_end":41},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"                Err(e) => {","highlight_start":1,"highlight_end":28},{"text":"                    // 限流检查出错，记录日志但允许请求通过","highlight_start":1,"highlight_end":42},{"text":"                    warn!(\"用户限流检查失败: {}\", e);","highlight_start":1,"highlight_end":46},{"text":"                    let fut = self.service.call(req);","highlight_start":1,"highlight_end":54},{"text":"                    let res = fut.await?;","highlight_start":1,"highlight_end":42},{"text":"                    Ok(res)","highlight_start":1,"highlight_end":28},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            }","highlight_start":1,"highlight_end":14}],"label":"`match` arms have incompatible types","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `Result<ServiceResponse<B>, _>`\n   found enum `Result<ServiceResponse<BoxBody>, _>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try wrapping the expression in `Err`","code":null,"level":"help","spans":[{"file_name":"src/middleware/rate_limit.rs","byte_start":12363,"byte_end":12363,"line_start":388,"line_end":388,"column_start":21,"column_end":21,"is_primary":true,"text":[{"text":"                    Ok(service_response)","highlight_start":21,"highlight_end":21}],"label":null,"suggested_replacement":"Err(","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/middleware/rate_limit.rs","byte_start":12383,"byte_end":12383,"line_start":388,"line_end":388,"column_start":41,"column_end":41,"is_primary":true,"text":[{"text":"                    Ok(service_response)","highlight_start":41,"highlight_end":41}],"label":null,"suggested_replacement":")","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: `match` arms have incompatible types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/middleware/rate_limit.rs:388:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m357\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0mimpl<S, B> actix_web::dev::Service<ServiceRequest> for UserRateLimitMiddlewareS\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected this type parameter\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m376\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m            match rate_limiter.check_limit(&user_key).await {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m377\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Ok(true) => {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m378\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    // 通过限流检查\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m379\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let fut = self.service.call(req);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m380\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let res = fut.await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m381\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    Ok(res)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is found to be of type `Result<ServiceResponse<B>, _>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m388\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    Ok(service_response)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Result<ServiceResponse<B>, _>`, found `Result<ServiceResponse, _>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m397\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_____________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m`match` arms have incompatible types\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected enum `Result<ServiceResponse<\u001b[0m\u001b[0m\u001b[1m\u001b[35mB\u001b[0m\u001b[0m>, \u001b[0m\u001b[0m\u001b[1m\u001b[35m_\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m               found enum `Result<ServiceResponse<\u001b[0m\u001b[0m\u001b[1m\u001b[35mBoxBody\u001b[0m\u001b[0m>, \u001b[0m\u001b[0m\u001b[1m\u001b[35m_\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: try wrapping the expression in `Err`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m388\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[38;5;10mErr(\u001b[0m\u001b[0mOk(service_response)\u001b[0m\u001b[0m\u001b[38;5;10m)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[38;5;10m++++\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`match` arms have incompatible types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src/middleware/rate_limit.rs","byte_start":16816,"byte_end":16836,"line_start":518,"line_end":518,"column_start":25,"column_end":45,"is_primary":true,"text":[{"text":"                        Ok(service_response)","highlight_start":25,"highlight_end":45}],"label":"expected `Result<ServiceResponse<B>, _>`, found `Result<ServiceResponse, _>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/middleware/rate_limit.rs","byte_start":14875,"byte_end":14876,"line_start":472,"line_end":472,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"impl<S, B> actix_web::dev::Service<ServiceRequest> for PathRateLimitMiddlewareService<S>","highlight_start":9,"highlight_end":10}],"label":"expected this type parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/middleware/rate_limit.rs","byte_start":16423,"byte_end":16430,"line_start":511,"line_end":511,"column_start":25,"column_end":32,"is_primary":false,"text":[{"text":"                        Ok(res)","highlight_start":25,"highlight_end":32}],"label":"this is found to be of type `Result<ServiceResponse<B>, _>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/middleware/rate_limit.rs","byte_start":16164,"byte_end":17214,"line_start":506,"line_end":527,"column_start":17,"column_end":18,"is_primary":false,"text":[{"text":"                match limiter.check_limit(&rate_limit_key).await {","highlight_start":17,"highlight_end":67},{"text":"                    Ok(true) => {","highlight_start":1,"highlight_end":34},{"text":"                        // 通过限流检查","highlight_start":1,"highlight_end":34},{"text":"                        let fut = self.service.call(req);","highlight_start":1,"highlight_end":58},{"text":"                        let res = fut.await?;","highlight_start":1,"highlight_end":46},{"text":"                        Ok(res)","highlight_start":1,"highlight_end":32},{"text":"                    }","highlight_start":1,"highlight_end":22},{"text":"                    Ok(false) => {","highlight_start":1,"highlight_end":35},{"text":"                        // 触发限流","highlight_start":1,"highlight_end":32},{"text":"                        let (req, _) = req.into_parts();","highlight_start":1,"highlight_end":57},{"text":"                        let response = AppError::RateLimitExceeded.error_response().map_into_boxed_body();","highlight_start":1,"highlight_end":107},{"text":"                        let service_response = actix_web::dev::ServiceResponse::new(req, response);","highlight_start":1,"highlight_end":100},{"text":"                        Ok(service_response)","highlight_start":1,"highlight_end":45},{"text":"                    }","highlight_start":1,"highlight_end":22},{"text":"                    Err(e) => {","highlight_start":1,"highlight_end":32},{"text":"                        // 限流检查出错，记录日志但允许请求通过","highlight_start":1,"highlight_end":46},{"text":"                        warn!(\"路径限流检查失败: {}\", e);","highlight_start":1,"highlight_end":50},{"text":"                        let fut = self.service.call(req);","highlight_start":1,"highlight_end":58},{"text":"                        let res = fut.await?;","highlight_start":1,"highlight_end":46},{"text":"                        Ok(res)","highlight_start":1,"highlight_end":32},{"text":"                    }","highlight_start":1,"highlight_end":22},{"text":"                }","highlight_start":1,"highlight_end":18}],"label":"`match` arms have incompatible types","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `Result<ServiceResponse<B>, _>`\n   found enum `Result<ServiceResponse<BoxBody>, _>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try wrapping the expression in `Err`","code":null,"level":"help","spans":[{"file_name":"src/middleware/rate_limit.rs","byte_start":16816,"byte_end":16816,"line_start":518,"line_end":518,"column_start":25,"column_end":25,"is_primary":true,"text":[{"text":"                        Ok(service_response)","highlight_start":25,"highlight_end":25}],"label":null,"suggested_replacement":"Err(","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/middleware/rate_limit.rs","byte_start":16836,"byte_end":16836,"line_start":518,"line_end":518,"column_start":45,"column_end":45,"is_primary":true,"text":[{"text":"                        Ok(service_response)","highlight_start":45,"highlight_end":45}],"label":null,"suggested_replacement":")","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: `match` arms have incompatible types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/middleware/rate_limit.rs:518:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m472\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0mimpl<S, B> actix_web::dev::Service<ServiceRequest> for PathRateLimitMiddlewareS\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected this type parameter\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m506\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m                match limiter.check_limit(&rate_limit_key).await {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m507\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    Ok(true) => {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m508\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        // 通过限流检查\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m509\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        let fut = self.service.call(req);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m510\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        let res = fut.await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m511\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        Ok(res)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is found to be of type `Result<ServiceResponse<B>, _>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m518\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        Ok(service_response)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Result<ServiceResponse<B>, _>`, found `Result<ServiceResponse, _>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m527\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_________________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m`match` arms have incompatible types\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected enum `Result<ServiceResponse<\u001b[0m\u001b[0m\u001b[1m\u001b[35mB\u001b[0m\u001b[0m>, \u001b[0m\u001b[0m\u001b[1m\u001b[35m_\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m               found enum `Result<ServiceResponse<\u001b[0m\u001b[0m\u001b[1m\u001b[35mBoxBody\u001b[0m\u001b[0m>, \u001b[0m\u001b[0m\u001b[1m\u001b[35m_\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: try wrapping the expression in `Err`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m518\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[38;5;10mErr(\u001b[0m\u001b[0mOk(service_response)\u001b[0m\u001b[0m\u001b[38;5;10m)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[38;5;10m++++\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 4 previous errors; 1 warning emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 4 previous errors; 1 warning emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0308`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about this error, try `rustc --explain E0308`.\u001b[0m\n"}
