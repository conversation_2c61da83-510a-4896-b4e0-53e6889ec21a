{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"actix-server\", \"experimental-io-uring\", \"tokio-uring\"]", "target": 13418831855529891677, "profile": 8276155916380437441, "path": 17433140568739456985, "deps": [[40386456601120721, "percent_encoding", false, 18175561887729487669], [1906322745568073236, "pin_project_lite", false, 14824965517151627551], [3064692270587553479, "actix_service", false, 12955660266757204224], [5384016313853579615, "actix_utils", false, 10182928119848046409], [5986029879202738730, "log", false, 4732121007680142088], [7620660491849607393, "futures_core", false, 5997558050176428920], [7896293946984509699, "bitflags", false, 6590612031470613045], [8866577183823226611, "http_range", false, 1003749735587384658], [9504753771229857410, "derive_more", false, 10165366865650269912], [10229185211513642314, "mime", false, 6925447606632632643], [14335890238902064286, "v_htmlescape", false, 9829106070347190073], [16066129441945555748, "bytes", false, 15715078194843767081], [16779987285852933470, "actix_web", false, 12789269638144633132], [17681776630119969552, "actix_http", false, 17272229354352096590], [18071510856783138481, "mime_guess", false, 1444490926209303474]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/actix-files-e11addc64fb4b0f2/dep-lib-actix_files", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}