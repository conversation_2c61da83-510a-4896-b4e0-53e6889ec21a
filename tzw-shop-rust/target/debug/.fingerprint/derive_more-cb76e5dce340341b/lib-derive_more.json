{"rustc": 15497389221046826682, "features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"not\", \"rustc_version\", \"sum\", \"try_into\", \"unwrap\"]", "declared_features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"generate-parsing-rs\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"nightly\", \"not\", \"peg\", \"rustc_version\", \"sum\", \"testing-helpers\", \"track-caller\", \"try_into\", \"unwrap\"]", "target": 12153973509411789784, "profile": 3033921117576893, "path": 2905275312329568154, "deps": [[3060637413840920116, "proc_macro2", false, 4073356016091061424], [10640660562325816595, "syn", false, 13419316461212647077], [14907448031486326382, "convert_case", false, 2627843194655496743], [17990358020177143287, "quote", false, 13515053211124113931]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_more-cb76e5dce340341b/dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}