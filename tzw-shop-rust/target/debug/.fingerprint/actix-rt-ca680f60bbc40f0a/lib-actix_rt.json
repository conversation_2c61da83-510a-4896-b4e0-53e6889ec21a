{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"actix-macros\", \"default\", \"io-uring\", \"macros\", \"tokio-uring\"]", "target": 7145233089273210151, "profile": 8276155916380437441, "path": 15345995048216597012, "deps": [[7620660491849607393, "futures_core", false, 5997558050176428920], [9538054652646069845, "tokio", false, 17946585307374682436]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/actix-rt-ca680f60bbc40f0a/dep-lib-actix_rt", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}