{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 9589772125425470163, "path": 1584237090307194688, "deps": [[14814905555676593471, "clap_builder", false, 3721780244371617203]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-87c2632ff57a1cac/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}