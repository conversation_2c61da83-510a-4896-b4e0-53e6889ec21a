{"rustc": 15497389221046826682, "features": "[\"as_ref\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 11796376952621915773, "profile": 5373973626925704462, "path": 18253346021742759680, "deps": [[3060637413840920116, "proc_macro2", false, 4073356016091061424], [10640660562325816595, "syn", false, 13419316461212647077], [16126285161989458480, "unicode_xid", false, 241025703244504791], [17990358020177143287, "quote", false, 13515053211124113931]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_more-impl-777be430c963f892/dep-lib-derive_more_impl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}