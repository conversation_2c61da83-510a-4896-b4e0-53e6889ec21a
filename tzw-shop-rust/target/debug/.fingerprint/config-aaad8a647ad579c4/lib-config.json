{"rustc": 15497389221046826682, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 1398949370558892206, "path": 2773562219215671342, "deps": [[1213098572879462490, "json5_rs", false, 13864914593363952407], [1965680986145237447, "yaml_rust2", false, 6014638621613587177], [2244620803250265856, "ron", false, 3757451964474709326], [6502365400774175331, "nom", false, 16675475800424395876], [6517602928339163454, "path<PERSON><PERSON>", false, 6908972073225613863], [9689903380558560274, "serde", false, 14418909433380882700], [11946729385090170470, "async_trait", false, 2794680592336022471], [13475460906694513802, "convert_case", false, 1638930320740152536], [14618892375165583068, "ini", false, 6087805030292917757], [15367738274754116744, "serde_json", false, 14005663958285762221], [15609422047640926750, "toml", false, 16495989826840561923]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-aaad8a647ad579c4/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}