{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"actix-server\", \"experimental-io-uring\", \"tokio-uring\"]", "target": 13418831855529891677, "profile": 5347358027863023418, "path": 17433140568739456985, "deps": [[40386456601120721, "percent_encoding", false, 16358572279217632947], [1906322745568073236, "pin_project_lite", false, 13187962730359849300], [3064692270587553479, "actix_service", false, 8334609225815398465], [5384016313853579615, "actix_utils", false, 17690291513628191096], [5986029879202738730, "log", false, 12686123598378474570], [7620660491849607393, "futures_core", false, 14358925094007800934], [7896293946984509699, "bitflags", false, 16750717130144360380], [8866577183823226611, "http_range", false, 2415478179824162792], [9504753771229857410, "derive_more", false, 10165366865650269912], [10229185211513642314, "mime", false, 6084625375669343117], [14335890238902064286, "v_htmlescape", false, 10257619905651478288], [16066129441945555748, "bytes", false, 4356714266081795432], [16779987285852933470, "actix_web", false, 10454412130722361320], [17681776630119969552, "actix_http", false, 11912363354581489188], [18071510856783138481, "mime_guess", false, 16813682963620088404]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/actix-files-6db63684b86c4522/dep-lib-actix_files", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}