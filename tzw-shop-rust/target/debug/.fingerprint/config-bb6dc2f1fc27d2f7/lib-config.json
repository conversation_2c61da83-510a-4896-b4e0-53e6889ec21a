{"rustc": 15497389221046826682, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 18330098564635666122, "path": 2773562219215671342, "deps": [[1213098572879462490, "json5_rs", false, 15391137931290594730], [1965680986145237447, "yaml_rust2", false, 8323447568465310156], [2244620803250265856, "ron", false, 10213128121094910040], [6502365400774175331, "nom", false, 10905439974038995350], [6517602928339163454, "path<PERSON><PERSON>", false, 14082752917895868067], [9689903380558560274, "serde", false, 9624892823048196402], [11946729385090170470, "async_trait", false, 2794680592336022471], [13475460906694513802, "convert_case", false, 18193175740569539417], [14618892375165583068, "ini", false, 16140432681122940390], [15367738274754116744, "serde_json", false, 6977943378727572000], [15609422047640926750, "toml", false, 11338884503700196174]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-bb6dc2f1fc27d2f7/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}