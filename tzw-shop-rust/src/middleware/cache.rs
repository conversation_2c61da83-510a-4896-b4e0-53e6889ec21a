use async_trait::async_trait;
use std::sync::Arc;
use mobc::Pool;
use mobc_redis::{redis, RedisConnectionManager};
use crate::config::redis::RedisPool;

#[async_trait]
pub trait CacheService: Send + Sync {
    async fn set<K, V>(&self, key: K, value: V, ttl: Option<usize>) -> Result<(), redis::RedisError>
    where
        K: redis::ToRedisArgs + Send + Sync,
        V: redis::ToRedisArgs + Send + Sync;

    async fn get<K, V>(&self, key: K) -> Result<Option<V>, redis::RedisError>
    where
        K: redis::ToRedisArgs + Send + Sync,
        V: redis::FromRedisValue + Send;

    async fn del<K>(&self, key: K) -> Result<bool, redis::RedisError>
    where
        K: redis::ToRedisArgs + Send + Sync;

    async fn exists<K>(&self, key: K) -> Result<bool, redis::RedisError>
    where
        K: redis::ToRedisArgs + Send + Sync;
}

pub struct RedisCacheService {
    pool: Arc<RedisPool>,
}

impl RedisCacheService {
    pub fn new(pool: Arc<RedisPool>) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl CacheService for RedisCacheService {
    async fn set<K, V>(&self, key: K, value: V, ttl: Option<usize>) -> Result<(), redis::RedisError>
    where
        K: redis::ToRedisArgs + Send + Sync,
        V: redis::ToRedisArgs + Send + Sync,
    {
        let mut conn = self.pool.get().await.map_err(|e| redis::RedisError::from((redis::ErrorKind::IoError, "连接池获取连接失败", e.to_string())))?;
        match ttl {
            Some(seconds) => {
                redis::cmd("SETEX").arg(key).arg(seconds).arg(value).query_async(&mut *conn).await
            }
            None => {
                redis::cmd("SET").arg(key).arg(value).query_async(&mut *conn).await
            }
        }
    }

    async fn get<K, V>(&self, key: K) -> Result<Option<V>, redis::RedisError>
    where
        K: redis::ToRedisArgs + Send + Sync,
        V: redis::FromRedisValue + Send,
    {
        let mut conn = self.pool.get().await.map_err(|e| redis::RedisError::from((redis::ErrorKind::IoError, "连接池获取连接失败", e.to_string())))?;
        let result: Option<V> = redis::cmd("GET").arg(key).query_async(&mut *conn).await?;
        Ok(result)
    }

    async fn del<K>(&self, key: K) -> Result<bool, redis::RedisError>
    where
        K: redis::ToRedisArgs + Send + Sync,
    {
        let mut conn = self.pool.get().await.map_err(|e| redis::RedisError::from((redis::ErrorKind::IoError, "连接池获取连接失败", e.to_string())))?;
        let result: i32 = redis::cmd("DEL").arg(key).query_async(&mut *conn).await?;
        Ok(result > 0)
    }

    async fn exists<K>(&self, key: K) -> Result<bool, redis::RedisError>
    where
        K: redis::ToRedisArgs + Send + Sync,
    {
        let mut conn = self.pool.get().await.map_err(|e| redis::RedisError::from((redis::ErrorKind::IoError, "连接池获取连接失败", e.to_string())))?;
        let result: i32 = redis::cmd("EXISTS").arg(key).query_async(&mut *conn).await?;
        Ok(result > 0)
    }
} 