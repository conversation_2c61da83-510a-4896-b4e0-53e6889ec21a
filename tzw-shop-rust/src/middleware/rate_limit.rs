//! 限流中间件
//! 
//! 提供基于Redis的滑动窗口限流功能

use crate::{AppError, AppResult};
use crate::config::redis::RedisPool;
use actix_web::{dev::ServiceRequest, HttpMessage, ResponseError};
use mobc_redis::redis;
use std::future::{ready, Ready};
use std::time::{SystemTime, UNIX_EPOCH};
use tracing::{debug, warn};

/// 限流配置
#[derive(Debug, Clone)]
pub struct RateLimitConfig {
    /// 是否启用限流
    pub enabled: bool,
    /// 每个键的最大请求数
    pub max_requests: u32,
    /// 时间窗口（秒）
    pub window_seconds: u64,
    /// Redis键前缀
    pub key_prefix: String,
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_requests: 100,
            window_seconds: 60,
            key_prefix: "rate_limit".to_string(),
        }
    }
}

/// 限流器
#[derive(Clone)]
pub struct RateLimiter {
    /// Redis连接池
    redis_pool: RedisPool,
    /// 限流配置
    config: RateLimitConfig,
}

impl RateLimiter {
    /// 创建新的限流器
    pub fn new(redis_pool: RedisPool, config: RateLimitConfig) -> Self {
        Self {
            redis_pool,
            config,
        }
    }

    /// 检查是否超过限流
    pub async fn check_limit(&self, key: &str) -> AppResult<bool> {
        if !self.config.enabled {
            return Ok(true);
        }

        let mut conn = self.redis_pool
            .get()
            .await
            .map_err(|e| AppError::Redis(redis::RedisError::from((
                redis::ErrorKind::IoError,
                "获取Redis连接失败",
                e.to_string(),
            ))))?;

        let rate_limit_key = format!("{}:{}", self.config.key_prefix, key);
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        let window_start = now - self.config.window_seconds;

        // 使用Lua脚本实现原子性操作
        let lua_script = r#"
            local key = KEYS[1]
            local window = tonumber(ARGV[1])
            local limit = tonumber(ARGV[2])
            local now = tonumber(ARGV[3])
            local window_start = tonumber(ARGV[4])
            
            -- 清理过期记录
            redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
            
            -- 获取当前窗口内的请求数
            local current = redis.call('ZCARD', key)
            
            if current < limit then
                -- 添加当前请求
                redis.call('ZADD', key, now, now .. ':' .. math.random())
                redis.call('EXPIRE', key, window)
                return { 1, current + 1 }
            else
                return { 0, current }
            end
        "#;

        let result: Vec<i32> = redis::Script::new(lua_script)
            .key(&rate_limit_key)
            .arg(self.config.window_seconds)
            .arg(self.config.max_requests)
            .arg(now)
            .arg(window_start)
            .invoke_async(&mut *conn)
            .await
            .map_err(AppError::Redis)?;

        let allowed = result.get(0).unwrap_or(&0) == &1;
        let current_count = result.get(1).unwrap_or(&0);

        if allowed {
            debug!(
                "限流检查通过: key={}, current={}/{}, window={}s",
                key, current_count, self.config.max_requests, self.config.window_seconds
            );
        } else {
            warn!(
                "限流触发: key={}, current={}/{}, window={}s",
                key, current_count, self.config.max_requests, self.config.window_seconds
            );
        }

        Ok(allowed)
    }

    /// 获取剩余请求次数
    pub async fn get_remaining(&self, key: &str) -> AppResult<u32> {
        if !self.config.enabled {
            return Ok(self.config.max_requests);
        }

        let mut conn = self.redis_pool
            .get()
            .await
            .map_err(|e| AppError::Redis(redis::RedisError::from((
                redis::ErrorKind::IoError,
                "获取Redis连接失败",
                e.to_string(),
            ))))?;

        let rate_limit_key = format!("{}:{}", self.config.key_prefix, key);
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        let window_start = now - self.config.window_seconds;

        // 清理过期记录并获取当前计数
        let lua_script = r#"
            local key = KEYS[1]
            local window_start = tonumber(ARGV[1])
            
            -- 清理过期记录
            redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
            
            -- 获取当前窗口内的请求数
            return redis.call('ZCARD', key)
        "#;

        let current_count: u32 = redis::Script::new(lua_script)
            .key(&rate_limit_key)
            .arg(window_start)
            .invoke_async(&mut *conn)
            .await
            .map_err(AppError::Redis)?;

        let remaining = if current_count >= self.config.max_requests {
            0
        } else {
            self.config.max_requests - current_count
        };

        Ok(remaining)
    }

    /// 重置限流计数器
    pub async fn reset(&self, key: &str) -> AppResult<()> {
        let mut conn = self.redis_pool
            .get()
            .await
            .map_err(|e| AppError::Redis(redis::RedisError::from((
                redis::ErrorKind::IoError,
                "获取Redis连接失败",
                e.to_string(),
            ))))?;

        let rate_limit_key = format!("{}:{}", self.config.key_prefix, key);

        let _: () = redis::cmd("DEL")
            .arg(&rate_limit_key)
            .query_async(&mut *conn)
            .await
            .map_err(AppError::Redis)?;

        debug!("重置限流计数器: key={}", key);
        Ok(())
    }
}

/// 获取客户端IP地址
pub fn get_client_ip(req: &ServiceRequest) -> String {
    // 尝试从X-Forwarded-For头获取
    if let Some(forwarded_for) = req.headers().get("X-Forwarded-For") {
        if let Ok(forwarded_for_str) = forwarded_for.to_str() {
            if let Some(first_ip) = forwarded_for_str.split(',').next() {
                return first_ip.trim().to_string();
            }
        }
    }

    // 尝试从X-Real-IP头获取
    if let Some(real_ip) = req.headers().get("X-Real-IP") {
        if let Ok(real_ip_str) = real_ip.to_str() {
            return real_ip_str.to_string();
        }
    }

    // 从连接信息获取
    if let Some(peer_addr) = req.peer_addr() {
        return peer_addr.ip().to_string();
    }

    // 默认值
    "unknown".to_string()
}

/// IP限流中间件
pub struct IpRateLimitMiddleware {
    rate_limiter: RateLimiter,
}

impl IpRateLimitMiddleware {
    pub fn new(rate_limiter: RateLimiter) -> Self {
        Self { rate_limiter }
    }
}

impl<S, B> actix_web::dev::Transform<S, ServiceRequest> for IpRateLimitMiddleware
where
    S: actix_web::dev::Service<ServiceRequest, Response = actix_web::dev::ServiceResponse<B>, Error = actix_web::Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = actix_web::dev::ServiceResponse<B>;
    type Error = actix_web::Error;
    type InitError = ();
    type Transform = IpRateLimitMiddlewareService<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(IpRateLimitMiddlewareService {
            service,
            rate_limiter: self.rate_limiter.clone(),
        }))
    }
}

pub struct IpRateLimitMiddlewareService<S> {
    service: S,
    rate_limiter: RateLimiter,
}

impl<S, B> actix_web::dev::Service<ServiceRequest> for IpRateLimitMiddlewareService<S>
where
    S: actix_web::dev::Service<ServiceRequest, Response = actix_web::dev::ServiceResponse<B>, Error = actix_web::Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = actix_web::dev::ServiceResponse<B>;
    type Error = actix_web::Error;
    type Future = std::pin::Pin<Box<dyn std::future::Future<Output = Result<Self::Response, Self::Error>>>>;

    actix_web::dev::forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let client_ip = get_client_ip(&req);
        let rate_limiter = self.rate_limiter.clone();

        Box::pin(async move {
            // 检查限流
            match rate_limiter.check_limit(&client_ip).await {
                Ok(true) => {
                    // 通过限流检查
                    let fut = self.service.call(req);
                    let res = fut.await?;
                    Ok(res)
                }
                Ok(false) => {
                    // 触发限流
                    let (req, _) = req.into_parts();
                    let response = AppError::RateLimitExceeded.error_response().map_into_boxed_body();
                    let service_response = actix_web::dev::ServiceResponse::new(req, response);
                    Ok(service_response)
                }
                Err(e) => {
                    // 限流检查出错，记录日志但允许请求通过
                    warn!("限流检查失败: {}", e);
                    let fut = self.service.call(req);
                    let res = fut.await?;
                    Ok(res)
                }
            }
        })
    }
}

/// 用户限流中间件
pub struct UserRateLimitMiddleware {
    rate_limiter: RateLimiter,
}

impl UserRateLimitMiddleware {
    pub fn new(rate_limiter: RateLimiter) -> Self {
        Self { rate_limiter }
    }

    /// 从请求中获取用户标识
    fn get_user_key(req: &ServiceRequest) -> Option<String> {
        // 尝试从请求扩展中获取用户上下文
        if let Some(user_context) = req.extensions().get::<crate::middleware::auth::UserContext>() {
            return Some(format!("user:{}", user_context.user_id));
        }

        // 如果没有用户上下文，使用IP作为备用
        Some(format!("ip:{}", get_client_ip(req)))
    }
}

impl<S, B> actix_web::dev::Transform<S, ServiceRequest> for UserRateLimitMiddleware
where
    S: actix_web::dev::Service<ServiceRequest, Response = actix_web::dev::ServiceResponse<B>, Error = actix_web::Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = actix_web::dev::ServiceResponse<B>;
    type Error = actix_web::Error;
    type InitError = ();
    type Transform = UserRateLimitMiddlewareService<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(UserRateLimitMiddlewareService {
            service,
            rate_limiter: self.rate_limiter.clone(),
        }))
    }
}

pub struct UserRateLimitMiddlewareService<S> {
    service: S,
    rate_limiter: RateLimiter,
}

impl<S, B> actix_web::dev::Service<ServiceRequest> for UserRateLimitMiddlewareService<S>
where
    S: actix_web::dev::Service<ServiceRequest, Response = actix_web::dev::ServiceResponse<B>, Error = actix_web::Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = actix_web::dev::ServiceResponse<B>;
    type Error = actix_web::Error;
    type Future = std::pin::Pin<Box<dyn std::future::Future<Output = Result<Self::Response, Self::Error>>>>;

    actix_web::dev::forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let user_key = UserRateLimitMiddleware::get_user_key(&req)
            .unwrap_or_else(|| format!("ip:{}", get_client_ip(&req)));
        let rate_limiter = self.rate_limiter.clone();

        Box::pin(async move {
            // 检查限流
            match rate_limiter.check_limit(&user_key).await {
                Ok(true) => {
                    // 通过限流检查
                    let fut = self.service.call(req);
                    let res = fut.await?;
                    Ok(res)
                }
                Ok(false) => {
                    // 触发限流
                    let (req, _) = req.into_parts();
                    let response = AppError::RateLimitExceeded.error_response().map_into_boxed_body();
                    let service_response = actix_web::dev::ServiceResponse::new(req, response);
                    Ok(service_response)
                }
                Err(e) => {
                    // 限流检查出错，记录日志但允许请求通过
                    warn!("用户限流检查失败: {}", e);
                    let fut = self.service.call(req);
                    let res = fut.await?;
                    Ok(res)
                }
            }
        })
    }
}

/// 路径限流中间件
/// 为不同的API路径配置不同的限流策略
pub struct PathRateLimitMiddleware {
    /// 路径限流配置映射
    path_configs: Vec<(String, RateLimiter)>,
    /// 默认限流器
    default_limiter: Option<RateLimiter>,
}

impl PathRateLimitMiddleware {
    pub fn new() -> Self {
        Self {
            path_configs: Vec::new(),
            default_limiter: None,
        }
    }

    /// 添加路径限流配置
    pub fn add_path_config(mut self, path_pattern: String, rate_limiter: RateLimiter) -> Self {
        self.path_configs.push((path_pattern, rate_limiter));
        self
    }

    /// 设置默认限流器
    pub fn with_default_limiter(mut self, rate_limiter: RateLimiter) -> Self {
        self.default_limiter = Some(rate_limiter);
        self
    }

    /// 根据路径获取对应的限流器
    fn get_rate_limiter(&self, path: &str) -> Option<&RateLimiter> {
        // 查找匹配的路径配置
        for (pattern, limiter) in &self.path_configs {
            if path.starts_with(pattern) {
                return Some(limiter);
            }
        }

        // 使用默认限流器
        self.default_limiter.as_ref()
    }
}

impl<S, B> actix_web::dev::Transform<S, ServiceRequest> for PathRateLimitMiddleware
where
    S: actix_web::dev::Service<ServiceRequest, Response = actix_web::dev::ServiceResponse<B>, Error = actix_web::Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = actix_web::dev::ServiceResponse<B>;
    type Error = actix_web::Error;
    type InitError = ();
    type Transform = PathRateLimitMiddlewareService<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(PathRateLimitMiddlewareService {
            service,
            path_configs: self.path_configs.clone(),
            default_limiter: self.default_limiter.clone(),
        }))
    }
}

pub struct PathRateLimitMiddlewareService<S> {
    service: S,
    path_configs: Vec<(String, RateLimiter)>,
    default_limiter: Option<RateLimiter>,
}

impl<S, B> actix_web::dev::Service<ServiceRequest> for PathRateLimitMiddlewareService<S>
where
    S: actix_web::dev::Service<ServiceRequest, Response = actix_web::dev::ServiceResponse<B>, Error = actix_web::Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = actix_web::dev::ServiceResponse<B>;
    type Error = actix_web::Error;
    type Future = std::pin::Pin<Box<dyn std::future::Future<Output = Result<Self::Response, Self::Error>>>>;

    actix_web::dev::forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let path = req.path().to_string();
        
        // 查找匹配的限流器
        let rate_limiter = {
            let mut found_limiter = None;
            for (pattern, limiter) in &self.path_configs {
                if path.starts_with(pattern) {
                    found_limiter = Some(limiter.clone());
                    break;
                }
            }
            
            found_limiter.or_else(|| self.default_limiter.clone())
        };

        Box::pin(async move {
            if let Some(limiter) = rate_limiter {
                let client_ip = get_client_ip(&req);
                let rate_limit_key = format!("path:{}:{}", path, client_ip);

                // 检查限流
                match limiter.check_limit(&rate_limit_key).await {
                    Ok(true) => {
                        // 通过限流检查
                        let fut = self.service.call(req);
                        let res = fut.await?;
                        Ok(res)
                    }
                    Ok(false) => {
                        // 触发限流
                        let (req, _) = req.into_parts();
                        let response = AppError::RateLimitExceeded.error_response().map_into_boxed_body();
                        let service_response = actix_web::dev::ServiceResponse::new(req, response);
                        Ok(service_response)
                    }
                    Err(e) => {
                        // 限流检查出错，记录日志但允许请求通过
                        warn!("路径限流检查失败: {}", e);
                        let fut = self.service.call(req);
                        let res = fut.await?;
                        Ok(res)
                    }
                }
            } else {
                // 没有配置限流器，直接通过
                let fut = self.service.call(req);
                let res = fut.await?;
                Ok(res)
            }
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_rate_limit_config_default() {
        let config = RateLimitConfig::default();
        assert!(config.enabled);
        assert_eq!(config.max_requests, 100);
        assert_eq!(config.window_seconds, 60);
        assert_eq!(config.key_prefix, "rate_limit");
    }

    #[test]
    fn test_get_client_ip() {
        // 这里需要mockServiceRequest来测试，暂时跳过
        // 实际测试需要创建mock的ServiceRequest
    }
}
