//! 错误处理模块
//! 
//! 提供统一的错误类型定义和HTTP响应处理

use actix_web::{HttpResponse, ResponseError};
use serde_json::json;

/// 应用程序错误类型
#[derive(thiserror::Error, Debug)]
pub enum AppError {
    // === 数据库相关错误 ===
    #[error("数据库错误: {0}")]
    Database(#[from] sqlx::Error),

    #[error("数据库连接失败")]
    DatabaseConnection,

    #[error("事务处理失败: {0}")]
    Transaction(String),

    // === Redis相关错误 ===
    #[error("缓存错误: {0}")]
    Redis(#[from] mobc_redis::redis::RedisError),

    #[error("缓存连接失败")]
    RedisConnection,

    // === 认证授权错误 ===
    #[error("未授权访问")]
    Unauthorized,

    #[error("权限不足")]
    Forbidden,

    #[error("令牌无效")]
    InvalidToken,

    #[error("令牌已过期")]
    TokenExpired,

    #[error("令牌编码失败: {0}")]
    TokenEncoding(String),

    #[error("令牌解码失败: {0}")]
    TokenDecoding(String),

    #[error("认证失败")]
    AuthenticationFailed,

    #[error("用户不存在")]
    UserNotFound,

    #[error("密码错误")]
    InvalidCredentials,

    // === 业务逻辑错误 ===
    #[error("商品不存在")]
    GoodsNotFound,

    #[error("商品规格不存在")]
    ProductNotFound,

    #[error("库存不足")]
    InsufficientStock,

    #[error("订单不存在")]
    OrderNotFound,

    #[error("订单状态异常")]
    InvalidOrderStatus,

    #[error("购物车为空")]
    EmptyCart,

    #[error("优惠券不可用")]
    CouponUnavailable,

    #[error("地址不存在")]
    AddressNotFound,

    #[error("分类不存在")]
    CategoryNotFound,

    // === 支付相关错误 ===
    #[error("支付失败: {0}")]
    PaymentFailed(String),

    #[error("微信支付错误: {0}")]
    WechatPayError(String),

    #[error("支付通知无效")]
    InvalidPaymentNotify,

    #[error("签名验证失败")]
    InvalidSignature,

    // === 限流错误 ===
    #[error("请求过于频繁")]
    RateLimitExceeded,

    #[error("库存锁定失败")]
    InventoryLockFailed,

    // === 验证错误 ===
    #[error("参数验证失败: {0}")]
    ValidationError(String),

    #[error("参数格式错误: {0}")]
    InvalidInput(String),

    #[error("手机号格式错误")]
    InvalidMobile,

    #[error("邮箱格式错误")]
    InvalidEmail,

    // === 外部服务错误 ===
    #[error("短信发送失败: {0}")]
    SmsError(String),

    #[error("文件上传失败: {0}")]
    FileUploadError(String),

    #[error("HTTP请求失败: {0}")]
    HttpRequestError(#[from] reqwest::Error),

    // === 序列化错误 ===
    #[error("JSON序列化失败: {0}")]
    JsonError(#[from] serde_json::Error),

    #[error("数据格式错误")]
    SerializationError,

    // === 系统错误 ===
    #[error("内部服务器错误")]
    InternalError,

    #[error("服务不可用")]
    ServiceUnavailable,

    #[error("配置错误: {0}")]
    ConfigError(String),

    #[error("IO错误: {0}")]
    IoError(#[from] std::io::Error),

    // === 自定义业务错误 ===
    #[error("业务错误: {message}")]
    Business { message: String },

    #[error("资源不存在: {resource}")]
    NotFound { resource: String },

    #[error("资源冲突: {message}")]
    Conflict { message: String },
}

impl ResponseError for AppError {
    fn error_response(&self) -> HttpResponse {
        let (status_code, error_code, message) = match self {
            // 认证授权相关
            AppError::Unauthorized => (401, "UNAUTHORIZED", "未授权访问"),
            AppError::Forbidden => (403, "FORBIDDEN", "权限不足"),
            AppError::InvalidToken | AppError::TokenExpired => (401, "INVALID_TOKEN", "令牌无效或已过期"),
            AppError::AuthenticationFailed => (401, "AUTH_FAILED", "认证失败"),
            AppError::UserNotFound => (404, "USER_NOT_FOUND", "用户不存在"),
            AppError::InvalidCredentials => (401, "INVALID_CREDENTIALS", "用户名或密码错误"),

            // 业务逻辑相关
            AppError::GoodsNotFound => (404, "GOODS_NOT_FOUND", "商品不存在"),
            AppError::ProductNotFound => (404, "PRODUCT_NOT_FOUND", "商品规格不存在"),
            AppError::InsufficientStock => (400, "INSUFFICIENT_STOCK", "库存不足"),
            AppError::OrderNotFound => (404, "ORDER_NOT_FOUND", "订单不存在"),
            AppError::InvalidOrderStatus => (400, "INVALID_ORDER_STATUS", "订单状态异常"),
            AppError::EmptyCart => (400, "EMPTY_CART", "购物车为空"),
            AppError::CouponUnavailable => (400, "COUPON_UNAVAILABLE", "优惠券不可用"),
            AppError::AddressNotFound => (404, "ADDRESS_NOT_FOUND", "地址不存在"),
            AppError::CategoryNotFound => (404, "CATEGORY_NOT_FOUND", "分类不存在"),

            // 支付相关
            AppError::PaymentFailed(_) => (400, "PAYMENT_FAILED", "支付失败"),
            AppError::WechatPayError(_) => (400, "WECHAT_PAY_ERROR", "微信支付错误"),
            AppError::InvalidPaymentNotify => (400, "INVALID_PAYMENT_NOTIFY", "支付通知无效"),
            AppError::InvalidSignature => (400, "INVALID_SIGNATURE", "签名验证失败"),

            // 限流相关
            AppError::RateLimitExceeded => (429, "RATE_LIMIT_EXCEEDED", "请求过于频繁"),
            AppError::InventoryLockFailed => (409, "INVENTORY_LOCK_FAILED", "库存锁定失败"),

            // 验证相关
            AppError::ValidationError(_) => (400, "VALIDATION_ERROR", "参数验证失败"),
            AppError::InvalidInput(_) => (400, "INVALID_INPUT", "参数格式错误"),
            AppError::InvalidMobile => (400, "INVALID_MOBILE", "手机号格式错误"),
            AppError::InvalidEmail => (400, "INVALID_EMAIL", "邮箱格式错误"),

            // 外部服务相关
            AppError::SmsError(_) => (500, "SMS_ERROR", "短信发送失败"),
            AppError::FileUploadError(_) => (500, "FILE_UPLOAD_ERROR", "文件上传失败"),
            AppError::HttpRequestError(_) => (500, "HTTP_REQUEST_ERROR", "外部服务请求失败"),

            // 自定义业务错误
            AppError::Business { .. } => (400, "BUSINESS_ERROR", "业务处理失败"),
            AppError::NotFound { .. } => (404, "NOT_FOUND", "资源不存在"),
            AppError::Conflict { .. } => (409, "CONFLICT", "资源冲突"),

            // 系统错误
            AppError::Database(_) => (500, "DATABASE_ERROR", "数据库错误"),
            AppError::Redis(_) => (500, "CACHE_ERROR", "缓存错误"),
            AppError::JsonError(_) => (400, "JSON_ERROR", "数据格式错误"),
            AppError::IoError(_) => (500, "IO_ERROR", "IO错误"),
            AppError::ConfigError(_) => (500, "CONFIG_ERROR", "配置错误"),
            
            // 默认内部错误
            _ => (500, "INTERNAL_ERROR", "内部服务器错误"),
        };

        let response_body = json!({
            "errno": error_code,
            "errmsg": message,
            "data": null,
            "timestamp": chrono::Utc::now().timestamp(),
            "details": self.to_string()
        });

        HttpResponse::build(actix_web::http::StatusCode::from_u16(status_code).unwrap_or(actix_web::http::StatusCode::INTERNAL_SERVER_ERROR))
            .json(response_body)
    }

    fn status_code(&self) -> actix_web::http::StatusCode {
        match self {
            AppError::Unauthorized | AppError::InvalidToken | AppError::TokenExpired 
            | AppError::AuthenticationFailed | AppError::InvalidCredentials => {
                actix_web::http::StatusCode::UNAUTHORIZED
            }
            AppError::Forbidden => actix_web::http::StatusCode::FORBIDDEN,
            AppError::UserNotFound | AppError::GoodsNotFound | AppError::ProductNotFound 
            | AppError::OrderNotFound | AppError::AddressNotFound | AppError::CategoryNotFound 
            | AppError::NotFound { .. } => {
                actix_web::http::StatusCode::NOT_FOUND
            }
            AppError::RateLimitExceeded => actix_web::http::StatusCode::TOO_MANY_REQUESTS,
            AppError::InventoryLockFailed | AppError::Conflict { .. } => {
                actix_web::http::StatusCode::CONFLICT
            }
            AppError::ValidationError(_) | AppError::InvalidInput(_) | AppError::InvalidMobile 
            | AppError::InvalidEmail | AppError::InsufficientStock | AppError::InvalidOrderStatus 
            | AppError::EmptyCart | AppError::CouponUnavailable | AppError::PaymentFailed(_) 
            | AppError::WechatPayError(_) | AppError::InvalidPaymentNotify | AppError::InvalidSignature 
            | AppError::JsonError(_) | AppError::Business { .. } => {
                actix_web::http::StatusCode::BAD_REQUEST
            }
            _ => actix_web::http::StatusCode::INTERNAL_SERVER_ERROR,
        }
    }
}

/// 应用程序结果类型
pub type AppResult<T> = Result<T, AppError>;

/// 标准API响应格式
#[derive(serde::Serialize, serde::Deserialize)]
pub struct ApiResponse<T> {
    pub errno: i32,
    pub errmsg: String,
    pub data: Option<T>,
}

impl<T> ApiResponse<T> {
    /// 创建成功响应
    pub fn success(data: T) -> Self {
        Self {
            errno: 0,
            errmsg: "成功".to_string(),
            data: Some(data),
        }
    }

    /// 创建成功响应（无数据）
    pub fn success_empty() -> ApiResponse<()> {
        ApiResponse {
            errno: 0,
            errmsg: "成功".to_string(),
            data: Some(()),
        }
    }

    /// 创建错误响应
    pub fn error(errno: i32, errmsg: String) -> ApiResponse<()> {
        ApiResponse {
            errno,
            errmsg,
            data: None,
        }
    }
}

/// 分页响应数据
#[derive(serde::Serialize, serde::Deserialize)]
pub struct PageResponse<T> {
    pub list: Vec<T>,
    pub total: u64,
    pub page: u32,
    pub limit: u32,
    pub pages: u32,
}

impl<T> PageResponse<T> {
    pub fn new(list: Vec<T>, total: u64, page: u32, limit: u32) -> Self {
        let pages = if total == 0 {
            0
        } else {
            ((total as f64) / (limit as f64)).ceil() as u32
        };

        Self {
            list,
            total,
            page,
            limit,
            pages,
        }
    }
}

/// 从validator错误转换为AppError
impl From<validator::ValidationErrors> for AppError {
    fn from(err: validator::ValidationErrors) -> Self {
        let mut messages = Vec::new();
        for (field, errors) in err.field_errors() {
            for error in errors {
                let message = error.message
                    .as_ref()
                    .map(|m| m.to_string())
                    .unwrap_or_else(|| format!("{} 验证失败", field));
                messages.push(message);
            }
        }
        AppError::ValidationError(messages.join(", "))
    }
}

/// 从配置错误转换为AppError
impl From<config::ConfigError> for AppError {
    fn from(err: config::ConfigError) -> Self {
        AppError::ConfigError(err.to_string())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use actix_web::test;

    #[test]
    async fn test_api_response_success() {
        let response = ApiResponse::success("test data");
        assert_eq!(response.errno, 0);
        assert_eq!(response.errmsg, "成功");
        assert_eq!(response.data, Some("test data"));
    }

    #[test]
   async fn test_api_response_error() {
        let response = ApiResponse::<()>::error(1001, "测试错误".to_string());
        assert_eq!(response.errno, 1001);
        assert_eq!(response.errmsg, "测试错误");
        assert_eq!(response.data, None);
    }

    #[actix_web::test]
    async fn test_app_error_response() {
        let error = AppError::UserNotFound;
        let response = error.error_response();
        assert_eq!(response.status(), 404);
    }

    #[test]
    async fn test_page_response() {
        let data = vec![1, 2, 3];
        let page_response = PageResponse::new(data, 10, 1, 5);
        assert_eq!(page_response.total, 10);
        assert_eq!(page_response.pages, 2);
    }
}
