//! TZW电商系统Rust版本 - 库模块
//! 
//! 提供系统的核心功能模块和初始化逻辑

pub mod config;
pub mod error;
pub mod middleware;
// TODO: 实现这些模块
// pub mod models;
// pub mod handlers;
// pub mod services;
// pub mod repositories;
// pub mod utils;
// pub mod external;
// pub mod jobs;

// 重新导出常用类型
pub use error::{AppError, AppResult, ApiResponse, PageResponse};
pub use config::{Settings, AppConfig, DatabaseConfig, RedisConfig};

use actix_web::{middleware::Logger, web, App, HttpServer};
use actix_cors::Cors;
use sqlx::MySqlPool;
use tracing::{info, warn};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use config::redis::RedisPool;

/// 应用程序状态
#[derive(Clone)]
pub struct AppState {
    pub db_pool: MySqlPool,
    pub redis_pool: RedisPool,
    pub settings: Settings,
}

/// 初始化日志系统
pub fn init_tracing(log_level: &str) -> anyhow::Result<()> {
    // 设置环境变量（如果未设置）
    if std::env::var("RUST_LOG").is_err() {
        unsafe {
            std::env::set_var("RUST_LOG", log_level);
        }
    }

    // 构建日志订阅器
    let subscriber = tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| {
                    format!("tzw_shop_rust={},actix_web=info,sqlx=warn", log_level).into()
                })
        )
        .with(
            tracing_subscriber::fmt::layer()
                .with_file(true)
                .with_line_number(true)
                .with_thread_ids(true)
                .with_target(false)
                .compact()
        );

    // 如果是生产环境，添加JSON格式化
    #[cfg(not(debug_assertions))]
    let subscriber = subscriber.with(
        tracing_subscriber::fmt::layer()
            .json()
            .with_current_span(false)
            .with_span_list(true)
    );

    subscriber.init();

    info!("日志系统初始化完成，级别: {}", log_level);
    Ok(())
}

/// 创建应用程序状态
pub async fn create_app_state(settings: Settings) -> anyhow::Result<AppState> {
    info!("正在初始化应用程序状态...");

    // 创建数据库连接池
    let db_pool = settings.database.create_pool().await
        .map_err(|e| anyhow::anyhow!("数据库连接池创建失败: {}", e))?;

    // 创建Redis连接池
    let redis_pool = settings.redis.create_pool().await
        .map_err(|e| anyhow::anyhow!("Redis连接池创建失败: {}", e))?;

    info!("应用程序状态初始化完成");

    Ok(AppState {
        db_pool,
        redis_pool,
        settings,
    })
}

/// 配置CORS中间件
pub fn configure_cors(cors_config: &config::app::CorsConfig) -> Cors {
    let mut cors = Cors::default();

    // 允许的源
    for origin in &cors_config.allowed_origins {
        if origin == "*" {
            cors = cors.allow_any_origin();
        } else {
            cors = cors.allowed_origin(origin);
        }
    }

    // 允许的方法
    let methods: Vec<actix_web::http::Method> = cors_config.allowed_methods
        .iter()
        .filter_map(|method| method.parse().ok())
        .collect();
    cors = cors.allowed_methods(methods);

    // 允许的请求头
    let headers: Vec<actix_web::http::header::HeaderName> = cors_config.allowed_headers
        .iter()
        .filter_map(|header| header.parse().ok())
        .collect();
    cors = cors.allowed_headers(headers);

    // 其他配置
    if cors_config.allow_credentials {
        cors = cors.supports_credentials();
    }

    cors = cors.max_age(cors_config.max_age);

    cors
}

/// 健康检查处理器
pub async fn health_check(app_state: web::Data<AppState>) -> Result<web::Json<serde_json::Value>, AppError> {
    use serde_json::json;

    // 检查数据库连接
    let db_health = config::DatabaseConfig::health_check(&app_state.db_pool)
        .await
        .unwrap_or(false);

    // 检查Redis连接
    let redis_health = config::RedisConfig::health_check(&app_state.redis_pool)
        .await
        .unwrap_or(false);

    // 获取连接池状态
    let db_status = config::DatabaseConfig::get_pool_status(&app_state.db_pool);
    let redis_status = config::RedisConfig::get_pool_status(&app_state.redis_pool).await;

    let health_data = json!({
        "status": if db_health && redis_health { "healthy" } else { "unhealthy" },
        "timestamp": chrono::Utc::now().timestamp(),
        "version": env!("CARGO_PKG_VERSION"),
        "database": {
            "healthy": db_health,
            "connections": db_status.size,
            "idle_connections": db_status.idle,
            "is_closed": db_status.is_closed
        },
        "redis": {
            "healthy": redis_health,
            "connections": redis_status.connections,
            "idle_connections": redis_status.idle_connections,
            "max_connections": redis_status.max_open
        }
    });

    if db_health && redis_health {
        Ok(web::Json(health_data))
    } else {
        warn!("健康检查失败 - 数据库: {}, Redis: {}", db_health, redis_health);
        Err(AppError::ServiceUnavailable)
    }
}

/// 配置公共路由
pub fn configure_common_routes(cfg: &mut web::ServiceConfig) {
    cfg.route("/health", web::get().to(health_check))
       .route("/", web::get().to(|| async {
           web::Json(serde_json::json!({
               "name": "TZW Shop Rust",
               "version": env!("CARGO_PKG_VERSION"),
               "description": "TZW电商系统Rust版本"
           }))
       }));
}

/// 创建HTTP服务器
pub fn create_server(
    app_state: AppState,
) -> actix_web::dev::Server {
    let cors_config = app_state.settings.app.cors.clone();
    let bind_addr = format!("{}:{}", app_state.settings.app.host, app_state.settings.app.port);
    
    HttpServer::new(move || {
        let cors = configure_cors(&cors_config);
        
        App::new()
            .app_data(web::Data::new(app_state.clone()))
            .wrap(cors)
            .wrap(Logger::default())
            .wrap(tracing_actix_web::TracingLogger::default())
            .configure(configure_common_routes)
            // TODO: 添加具体的路由配置
            // .configure(handlers::admin::configure)
            // .configure(handlers::wx::configure)
    })
    .bind(&bind_addr)
    .unwrap_or_else(|_| panic!("无法绑定到地址: {}", bind_addr))
    .run()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_init_tracing() {
        let result = init_tracing("debug");
        assert!(result.is_ok());
    }

    #[test]
    fn test_configure_cors() {
        let cors_config = config::app::CorsConfig::default();
        let _cors = configure_cors(&cors_config);
        // CORS配置成功创建
    }
}
