//! TZW电商系统Rust版本 - 主程序入口
//! 
//! 统一的应用程序入口，支持管理后台和微信小程序API

use anyhow::Result;
use dotenvy::dotenv;
use tracing::info;
use tzw_shop_rust::{create_app_state, create_server, init_tracing, Settings};

#[tokio::main]
async fn main() -> Result<()> {
    // 加载环境变量
    dotenv().ok();

    // 加载配置
    let settings = Settings::new()?;

    // 初始化日志系统
    init_tracing(&settings.app.log_level)?;

    info!("启动 TZW电商系统Rust版本 v{}", env!("CARGO_PKG_VERSION"));
    info!("运行模式: {}", std::env::var("RUN_MODE").unwrap_or_else(|_| "dev".to_string()));

    // 创建应用程序状态
    let app_state = create_app_state(settings.clone()).await?;

    // 创建HTTP服务器
    let server = create_server(app_state);

    let bind_address = format!("{}:{}", settings.app.host, settings.app.port);
    info!("服务器启动在: http://{}", bind_address);
    info!("健康检查: http://{}/health", bind_address);

    // 启动服务器
    server.await?;

    Ok(())
}
