//! Redis配置模块

use mobc::Pool;
use mobc_redis::{ redis, RedisConnectionManager };
use serde::Deserialize;
use std::time::Duration;
use tracing::{ info, warn };

/// Redis连接池类型
pub type RedisPool = Pool<RedisConnectionManager>;
/// Redis连接类型
pub type RedisConnection = mobc::Connection<RedisConnectionManager>;

/// Redis配置
#[derive(Debug, Deserialize, Clone)]
pub struct RedisConfig {
    /// Redis连接URL
    pub url: String,
    /// 最大连接数
    pub max_open: u64,
    /// 最大空闲连接数
    pub max_idle: u64,
    /// 连接超时时间（秒）
    pub timeout: u64,
    /// 连接最大生命周期（秒）
    pub max_lifetime: u64,
    /// 连接空闲超时时间（秒）
    pub idle_timeout: u64,
    /// 数据库索引
    pub database: u8,
    /// 密码
    pub password: Option<String>,
}

impl Default for RedisConfig {
    fn default() -> Self {
        Self {
            url: "redis://127.0.0.1:6379".to_string(),
            max_open: 100,
            max_idle: 50,
            timeout: 30,
            max_lifetime: 3600, // 1小时
            idle_timeout: 600, // 10分钟
            database: 0,
            password: None,
        }
    }
}

impl RedisConfig {
    /// 创建Redis连接池
    pub async fn create_pool(&self) -> Result<RedisPool, redis::RedisError> {
        info!("正在初始化Redis连接池...");
        info!("Redis URL: {}", self.mask_url());
        info!("最大连接数: {}", self.max_open);
        info!("最大空闲连接数: {}", self.max_idle);

        // 构建Redis客户端
        let client = redis::Client::open(self.url.as_str())?;
        let manager = RedisConnectionManager::new(client);

        // 构建连接池
        let pool = Pool::builder()
            .max_open(self.max_open)
            .max_idle(self.max_idle)
            .get_timeout(Some(Duration::from_secs(self.timeout)))
            .max_lifetime(Some(Duration::from_secs(self.max_lifetime)))
            .build(manager);

        info!("Redis连接池初始化成功");

        // 测试连接
        match self.test_connection(&pool).await {
            Ok(_) => info!("Redis连接测试成功"),
            Err(e) => {
                warn!("Redis连接测试失败: {}", e);
                return Err(e);
            }
        }

        Ok(pool)
    }

    /// 测试Redis连接
    async fn test_connection(&self, pool: &RedisPool) -> Result<(), redis::RedisError> {
        let mut conn = pool
            .get().await
            .map_err(|e|
                redis::RedisError::from((
                    redis::ErrorKind::IoError,
                    "连接池获取连接失败",
                    e.to_string(),
                ))
            )?;

        redis::cmd("PING").query_async::<_, String>(&mut *conn).await?;

        Ok(())
    }

    /// 掩码化Redis URL（隐藏密码）
    fn mask_url(&self) -> String {
        if let Some(pos) = self.url.find("://") {
            let scheme = &self.url[..pos + 3];
            let rest = &self.url[pos + 3..];

            if let Some(at_pos) = rest.find('@') {
                let credentials = &rest[..at_pos];
                let host_part = &rest[at_pos..];

                if let Some(colon_pos) = credentials.find(':') {
                    let username = &credentials[..colon_pos];
                    format!("{}{}:***{}", scheme, username, host_part)
                } else {
                    self.url.clone()
                }
            } else {
                self.url.clone()
            }
        } else {
            self.url.clone()
        }
    }

    /// 健康检查
    pub async fn health_check(pool: &RedisPool) -> Result<bool, redis::RedisError> {
        let mut conn = pool
            .get().await
            .map_err(|e|
                redis::RedisError::from((
                    redis::ErrorKind::IoError,
                    "连接池获取连接失败",
                    e.to_string(),
                ))
            )?;

        let result: String = redis::cmd("PING").query_async(&mut *conn).await?;

        Ok(result == "PONG")
    }

    /// 获取连接池统计信息
    pub async fn get_pool_status(pool: &RedisPool) -> RedisPoolStatus {
        let state = pool.state().await;
        RedisPoolStatus {
            connections: state.connections as u32,
            idle_connections: 0, // mobc不提供此信息
            max_open: state.max_open,
        }
    }
}

/// Redis连接池状态
#[derive(Debug)]
pub struct RedisPoolStatus {
    /// 当前连接数
    pub connections: u32,
    /// 空闲连接数
    pub idle_connections: u32,
    /// 最大连接数
    pub max_open: u64,
}
