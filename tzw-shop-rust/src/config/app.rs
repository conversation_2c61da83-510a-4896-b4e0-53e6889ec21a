//! 应用配置模块

use serde::Deserialize;

/// 应用程序配置
#[derive(Debug, Deserialize, Clone)]
pub struct AppConfig {
    /// 应用名称
    pub name: String,
    /// 应用版本
    pub version: String,
    /// 服务器主机地址
    pub host: String,
    /// 服务器端口
    pub port: u16,
    /// 是否启用调试模式
    pub debug: bool,
    /// 日志级别
    pub log_level: String,
    /// JWT密钥
    pub jwt_secret: String,
    /// JWT访问令牌过期时间（秒）
    pub jwt_access_expires: i64,
    /// JWT刷新令牌过期时间（秒）
    pub jwt_refresh_expires: i64,
    /// 跨域配置
    pub cors: CorsConfig,
    /// 限流配置
    pub rate_limit: RateLimitConfig,
}

/// 跨域配置
#[derive(Debug, Deserialize, Clone)]
pub struct CorsConfig {
    /// 允许的源
    pub allowed_origins: Vec<String>,
    /// 允许的请求头
    pub allowed_headers: Vec<String>,
    /// 允许的方法
    pub allowed_methods: Vec<String>,
    /// 是否允许凭证
    pub allow_credentials: bool,
    /// 预检请求缓存时间（秒）
    pub max_age: usize,
}

/// 限流配置
#[derive(Debug, Deserialize, Clone)]
pub struct RateLimitConfig {
    /// 是否启用限流
    pub enabled: bool,
    /// 每IP最大请求数
    pub max_requests_per_ip: u32,
    /// 限流窗口时间（秒）
    pub window_seconds: u64,
    /// 登录接口最大请求数
    pub login_max_requests: u32,
    /// 登录限流窗口时间（秒）
    pub login_window_seconds: u64,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            name: "TZW Shop Rust".to_string(),
            version: "0.1.0".to_string(),
            host: "127.0.0.1".to_string(),
            port: 8080,
            debug: true,
            log_level: "info".to_string(),
            jwt_secret: "your-secret-key-change-in-production".to_string(),
            jwt_access_expires: 900,  // 15分钟
            jwt_refresh_expires: 604800,  // 7天
            cors: CorsConfig::default(),
            rate_limit: RateLimitConfig::default(),
        }
    }
}

impl Default for CorsConfig {
    fn default() -> Self {
        Self {
            allowed_origins: vec!["*".to_string()],
            allowed_headers: vec![
                "Content-Type".to_string(),
                "Authorization".to_string(),
                "X-Requested-With".to_string(),
            ],
            allowed_methods: vec![
                "GET".to_string(),
                "POST".to_string(),
                "PUT".to_string(),
                "DELETE".to_string(),
                "OPTIONS".to_string(),
            ],
            allow_credentials: true,
            max_age: 3600,
        }
    }
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_requests_per_ip: 1000,
            window_seconds: 3600,  // 1小时
            login_max_requests: 5,
            login_window_seconds: 300,  // 5分钟
        }
    }
}
