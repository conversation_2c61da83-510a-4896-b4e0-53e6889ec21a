//! 配置管理模块
//! 
//! 提供应用程序的配置管理功能，包括数据库、Redis、应用配置等

pub mod app;
pub mod database;
pub mod redis;

pub use app::AppConfig;
pub use database::DatabaseConfig;
pub use redis::RedisConfig;

use anyhow::Result;
use config::{Config, ConfigError, Environment, File};
use serde::Deserialize;
use std::env;

/// 完整的应用配置
#[derive(Debug, Deserialize, Clone)]
pub struct Settings {
    pub app: AppConfig,
    pub database: DatabaseConfig,
    pub redis: RedisConfig,
}

impl Settings {
    /// 从配置文件和环境变量加载配置
    pub fn new() -> Result<Self, ConfigError> {
        let run_mode = env::var("RUN_MODE").unwrap_or_else(|_| "dev".into());

        let s = Config::builder()
            // 从默认配置文件开始
            .add_source(File::with_name("config/default"))
            // 添加环境特定的配置文件
            .add_source(File::with_name(&format!("config/{}", run_mode)).required(false))
            // 添加本地配置文件（可选）
            .add_source(File::with_name("config/local").required(false))
            // 从环境变量添加设置（前缀为APP）
            // 例如 `APP_DEBUG=1 ./target/app` 将设置 `debug` 为true
            .add_source(Environment::with_prefix("app"))
            .build()?;

        // 反序列化配置
        s.try_deserialize()
    }
}

impl Default for Settings {
    fn default() -> Self {
        Self::new().expect("配置加载失败")
    }
}
