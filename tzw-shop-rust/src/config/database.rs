//! 数据库配置模块

use serde::Deserialize;
use sqlx::{mysql::MySqlPoolOptions, MySqlPool};
use std::time::Duration;
use tracing::{info, warn};

/// 数据库配置
#[derive(Debug, Deserialize, Clone)]
pub struct DatabaseConfig {
    /// 数据库连接URL
    pub url: String,
    /// 最大连接数
    pub max_connections: u32,
    /// 最小连接数
    pub min_connections: u32,
    /// 连接超时时间（秒）
    pub connect_timeout: u64,
    /// 空闲连接超时时间（秒）
    pub idle_timeout: u64,
    /// 最大连接生命周期（秒）
    pub max_lifetime: u64,
    /// 是否启用SQL日志
    pub enable_logging: bool,
    /// 慢查询阈值（毫秒）
    pub slow_query_threshold: u64,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            url: "mysql://root:password@localhost:3306/dts_shop".to_string(),
            max_connections: 20,
            min_connections: 5,
            connect_timeout: 30,
            idle_timeout: 600,  // 10分钟
            max_lifetime: 3600,  // 1小时
            enable_logging: true,
            slow_query_threshold: 1000,  // 1秒
        }
    }
}

impl DatabaseConfig {
    /// 创建MySQL连接池
    pub async fn create_pool(&self) -> Result<MySqlPool, sqlx::Error> {
        info!("正在初始化数据库连接池...");
        info!("数据库URL: {}", self.mask_url());
        info!("最大连接数: {}", self.max_connections);
        info!("最小连接数: {}", self.min_connections);

        let pool = MySqlPoolOptions::new()
            .max_connections(self.max_connections)
            .min_connections(self.min_connections)
            .acquire_timeout(Duration::from_secs(self.connect_timeout))
            .idle_timeout(Duration::from_secs(self.idle_timeout))
            .max_lifetime(Duration::from_secs(self.max_lifetime))
            .test_before_acquire(true)
            .connect(&self.url)
            .await?;

        info!("数据库连接池初始化成功");

        // 测试连接
        match sqlx::query("SELECT 1 as test")
            .fetch_one(&pool)
            .await
        {
            Ok(_) => info!("数据库连接测试成功"),
            Err(e) => {
                warn!("数据库连接测试失败: {}", e);
                return Err(e);
            }
        }

        Ok(pool)
    }

    /// 掩码化数据库URL（隐藏密码）
    fn mask_url(&self) -> String {
        if let Some(pos) = self.url.find("://") {
            let scheme = &self.url[..pos + 3];
            let rest = &self.url[pos + 3..];
            
            if let Some(at_pos) = rest.find('@') {
                let credentials = &rest[..at_pos];
                let host_part = &rest[at_pos..];
                
                if let Some(colon_pos) = credentials.find(':') {
                    let username = &credentials[..colon_pos];
                    format!("{}{}:***{}", scheme, username, host_part)
                } else {
                    self.url.clone()
                }
            } else {
                self.url.clone()
            }
        } else {
            self.url.clone()
        }
    }

    /// 健康检查
    pub async fn health_check(pool: &MySqlPool) -> Result<bool, sqlx::Error> {
        let result = sqlx::query_scalar::<_, i32>("SELECT 1")
            .fetch_one(pool)
            .await?;
        
        Ok(result == 1)
    }

    /// 获取连接池统计信息
    pub fn get_pool_status(pool: &MySqlPool) -> PoolStatus {
        PoolStatus {
            size: pool.size(),
            idle: pool.num_idle(),
            is_closed: pool.is_closed(),
        }
    }
}

/// 连接池状态
#[derive(Debug)]
pub struct PoolStatus {
    /// 总连接数
    pub size: u32,
    /// 空闲连接数
    pub idle: usize,
    /// 是否已关闭
    pub is_closed: bool,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_mask_url() {
        let config = DatabaseConfig {
            url: "mysql://user:password@localhost:3306/test".to_string(),
            ..Default::default()
        };
        
        let masked = config.mask_url();
        assert_eq!(masked, "mysql://user:***@localhost:3306/test");
    }

    #[test]
    fn test_mask_url_no_password() {
        let config = DatabaseConfig {
            url: "mysql://user@localhost:3306/test".to_string(),
            ..Default::default()
        };
        
        let masked = config.mask_url();
        assert_eq!(masked, "mysql://user@localhost:3306/test");
    }
}
